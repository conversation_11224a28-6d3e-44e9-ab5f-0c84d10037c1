import v3, { ApiError } from '@/services/v3';
import { createQueryFunction } from '@/utils/query';
import {
  CreateExampleOutputParams,
  DeleteExampleOutputParams,
  GetExampleOutputParams,
  GetExampleOutputsParams,
  GetTaskOutputExampleUriParams,
} from '@floqastinc/transform-v3';
import { queryOptions } from '@tanstack/react-query';
import { t } from '@/utils/i18n';
import { queryKeys } from './query-keys';

const getExampleOutputs = createQueryFunction(
  queryKeys.exampleOutputs.getExampleOutputs,
  async (params) => {
    const result = await v3.exampleOutputs.getExampleOutputs(params as GetExampleOutputsParams);
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
    if (!result.data) {
      throw new Error(t('components.Builder.Errors.failedExampleOutputs'));
    }
    return result.data;
  },
);
export const getExampleOutputsQuery = (params: GetExampleOutputsParams) => {
  return queryOptions({
    queryKey: queryKeys.exampleOutputs.getExampleOutputs(params),
    queryFn: getExampleOutputs,
  });
};

const getExampleOutput = createQueryFunction(
  queryKeys.exampleOutputs.getExampleOutput,
  async (params) => {
    const result = await v3.exampleOutputs.getExampleOutputs(params as GetExampleOutputsParams);
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
    if (!result.data) {
      throw new Error(t('components.Builder.Errors.failedExampleOutputs'));
    }
    return result;
  },
);

export const getExampleOutputQuery = (params: GetExampleOutputParams) => {
  return queryOptions({
    queryKey: queryKeys.exampleOutputs.getExampleOutput(params),
    queryFn: getExampleOutput,
  });
};

const getExampleOutputFileUri = createQueryFunction(
  queryKeys.exampleOutputs.getTaskOutputExampleUri,
  async (params) => {
    const result = await v3.exampleOutputs.getTaskOutputExampleUri(
      params as GetTaskOutputExampleUriParams,
    );
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
    if (!result.data) {
      throw new Error(t('components.Builder.Errors.failedOutputFile'));
    }
    return result;
  },
);

export const getExampleOutputFileUriQuery = (params: GetTaskOutputExampleUriParams) => {
  return queryOptions({
    queryKey: queryKeys.exampleOutputs.getTaskOutputExampleUri(params),
    queryFn: getExampleOutputFileUri,
  });
};

export const createExampleOutput = async (params: CreateExampleOutputParams) => {
  const result = await v3.exampleOutputs.createExampleOutput(params);
  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (!result.data) {
    throw new Error(t('components.Builder.Errors.failedCreateOutputs'));
  }
  return result;
};

export const deleteExampleOutput = async (params: DeleteExampleOutputParams) => {
  const result = await v3.exampleOutputs.deleteExampleOutput(params);
  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (!result.data) {
    throw new Error(t('components.Builder.Errors.failedDeleteOutputs'));
  }
  return result;
};
