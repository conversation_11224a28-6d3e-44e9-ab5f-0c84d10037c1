import { ExampleSetStatus } from '@floqastinc/transform-v3';
import { styled } from 'styled-components';
import { Button } from '@floqastinc/flow-ui_core';

export const HeaderArea = styled.div`
  display: grid;
  grid-template-areas:
    'title close'
    'description close';
  gap: 4px;
  padding: 24px;
`;

export const Header = styled.div`
  grid-area: title;
`;

export const VersionsDescription = styled.div`
  display: flex;
  grid-area: description;
  flex-direction: column;
  justify-content: space-between;
  gap: 10px;
`;

export const CloseIconButton = styled.div`
  grid-area: close;
`;

export const Versions = styled.div`
  display: flex;
  flex-direction: column;
  padding: 24px;
`;

export const Tabs = styled.div`
  display: flex;
  flex-direction: row;
  gap: 8px;
`;

export const WarningIcon = styled.div`
  margin-left: -20px;
`;

export const VersionList = styled.ul`
  display: flex;
  flex-direction: column;
  gap: 36px;
  padding: 24px 0;
`;

export const VersionItem = styled.li`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export const VersionItemLeft = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: fit-content;
  gap: 24px;
`;

export const SelectButton = styled(Button)`
  height: 40px;
  width: 72px;
`;

export const VersionItemTitle = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

// StatusDropdown
export const PillButton = styled.button<{ $status?: ExampleSetStatus }>`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 24px;
  padding: 4px 6px;
  gap: 8px;
  background-color: ${({ $status }) => {
    switch ($status) {
      case 'ACTIVE':
        return '#F0F5FF';
      case 'DRAFT':
      case 'ARCHIVED':
        return '#F1F3F9';
      default:
        return '#E5E5E5';
    }
  }};
  color: ${({ $status }) => {
    switch ($status) {
      case 'ACTIVE':
        return '#3D7BF7';
      case 'DRAFT':
      case 'ARCHIVED':
        return '#6B7280';
      default:
        return '#333';
    }
  }};
  border-radius: 100px;
`;

export const KeyboardArrowDownIcon = styled.div<{ $status?: ExampleSetStatus }>`
  display: flex;
  align-items: center;
  height: 14px;
  width: 14px;
  border-radius: 100px;
  background-color: ${({ $status }) => {
    switch ($status) {
      case 'ACTIVE':
        return '#DCE7FE';
      case 'DRAFT':
      case 'ARCHIVED':
        return '#D9D9D9';
      default:
        return '#E5E5E5';
    }
  }};
  & > svg {
    fill: ${({ $status }) => {
      switch ($status) {
        case 'ACTIVE':
          return '#3D7BF7';
        case 'DRAFT':
        case 'ARCHIVED':
          return '#6B7280';
        default:
          return '#333';
      }
    }};
  }
`;

export const Hr = styled.hr`
  height: 1px;
  width: 100%;
  border: 1px solid #e5e5e5;
`;
