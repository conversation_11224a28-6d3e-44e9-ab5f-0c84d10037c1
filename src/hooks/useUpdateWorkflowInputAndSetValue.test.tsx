import v3 from '@/services/v3';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { setWorkflowInputValue } from '@Transform/api/set-workflow-input-value';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderHook } from 'vitest-browser-react';
import {
  useUpdateWorkflowInputAndSetValue,
  UseUpdateWorkflowInputAndSetValueInput,
} from './useUpdateWorkflowInputAndSetValue';

vi.mock('@/services/v3', () => ({
  default: {
    workflowInputs: {
      updateWorkflowInput: vi.fn(),
      deleteWorkflowInput: vi.fn(),
    },
  },
}));
vi.mock('@Transform/api/set-workflow-input-value', () => ({
  setWorkflowInputValue: vi.fn(),
}));

const mockInput: UseUpdateWorkflowInputAndSetValueInput = {
  workflowId: 'workflow-1',
  workflowInputId: 'input-1',
  input: {
    name: 'Test Input',
    description: 'desc',
    type: 'TEXT',
    value: {
      kind: 'TEXT',
      value: 'test-value',
    },
  },
};

const queryClient = new QueryClient();
vi.spyOn(queryClient, 'setQueryData');

const createWrapper = () => {
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useUpdateWorkflowInputAndSetValue', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    queryClient.clear();
  });

  it('successfully creates workflow input and sets value', async () => {
    // Arrange
    queryClient.setQueryData(['/workflows/workflow-1/inputs'], {
      data: [],
    });

    vi.mocked(v3.workflowInputs.updateWorkflowInput).mockResolvedValue({
      data: {
        id: 'input-1',
        workflowId: 'workflow-1',
        name: 'Test Input',
        description: 'desc',
        type: 'TEXT',
      },
      errors: [],
    });
    vi.mocked(setWorkflowInputValue).mockResolvedValue({} as any);

    const { result } = renderHook(() => useUpdateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act
    const res = await result.current.mutateAsync(mockInput);

    await vi.waitFor(() => result.current.isSuccess);

    // Assert
    expect(res).toEqual({
      id: 'input-1',
      workflowId: 'workflow-1',
      name: 'Test Input',
      description: 'desc',
      type: 'TEXT',
    });

    expect(v3.workflowInputs.updateWorkflowInput).toHaveBeenCalled();
    expect(setWorkflowInputValue).toHaveBeenCalled();
  });

  it('throws if updateWorkflowInput returns errors', async () => {
    // Arrange
    (v3.workflowInputs.updateWorkflowInput as any).mockResolvedValue({
      errors: ['Some error'],
    });

    const { result } = renderHook(() => useUpdateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act & Assert
    await expect(result.current.mutateAsync(mockInput)).rejects.toThrow(
      'Failed to update workflow input: Some error',
    );
  });

  it('throws if updateWorkflowInput returns no data', async () => {
    // Arrange
    (v3.workflowInputs.updateWorkflowInput as any).mockResolvedValue({
      errors: [],
      data: null,
    });

    const { result } = renderHook(() => useUpdateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act & Assert
    await expect(result.current.mutateAsync(mockInput)).rejects.toThrow(
      'Unexpected error: no data returned from updateWorkflowInput',
    );
  });

  it('restores previous inputs on error', async () => {
    // Arrange
    (v3.workflowInputs.updateWorkflowInput as any).mockRejectedValue(new Error('Create failed'));

    const prevInputs = [
      {
        id: 'old',
        name: 'Old',
        description: '',
        type: 'TEXT',
        value: { kind: 'TEXT', value: 'old' },
      },
    ];
    queryClient.setQueryData(['/workflows/workflow-1/inputs'], {
      data: prevInputs,
    });

    const { result } = renderHook(() => useUpdateWorkflowInputAndSetValue(), {
      wrapper: createWrapper(),
    });

    // Act
    try {
      await result.current.mutateAsync(mockInput);
    } catch {}

    // Assert
    expect((queryClient.getQueryData(['/workflows/workflow-1/inputs']) as any)?.data).toEqual(
      prevInputs,
    );
  });
});
