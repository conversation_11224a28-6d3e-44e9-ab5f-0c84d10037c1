import * as xlsx from 'xlsx';
import { AgGridReact } from 'ag-grid-react';
import { AllCommunityModule, type ColDef, ModuleRegistry, themeQuartz } from 'ag-grid-community';
ModuleRegistry.registerModules([AllCommunityModule]);
import {
  Heading,
  IconButton,
  Text,
  EmptyState,
  TableStatusBadge,
  LinkButton,
  Tooltip,
} from '@floqastinc/flow-ui_core';
import Clear from '@floqastinc/flow-ui_icons/legacy/Clear';
import Download from '@floqastinc/flow-ui_icons/material/Download';
import { useEffect, useState, useRef, useCallback } from 'react';
import { ExampleSet, ExampleSetStatus } from '@floqastinc/transform-v3';
import { useParams } from 'react-router-dom';
import * as Styled from './index.styles';
import { t } from '@/utils/i18n';
import { downloadFile } from '@/utils/browser';
import { Loading } from '@/components/Loading';
import { StatusChangeDialog } from '@Transform/pages/builder/ui/Chat/components/StatusChange/StatusChange';

const gridFilePreviewTheme = themeQuartz.withParams({
  columnBorder: { style: 'solid', color: '#cccccc' },
  headerColumnBorder: { style: 'solid', color: '#cccccc' },
  rowBorder: { style: 'solid', color: '#cccccc' },
  headerRowBorder: { style: 'solid', color: '#cccccc' },
  headerBackgroundColor: '#EDEDED',
});

// Default dimensions for cells
const DEFAULT_ROW_HEIGHT = 28; // pixels
const DEFAULT_COL_WIDTH = 100; // pixels

const parseExcel = async (file: File): Promise<xlsx.WorkBook> => {
  const fileBuffer = await file.arrayBuffer();
  const wb = xlsx.read(fileBuffer);
  return wb;
};

const getColumnLettersFromRange = (range: string | undefined) => {
  if (!range) return [];

  const match = range.match(/^([A-Z]+)\d+:([A-Z]+)\d+$/);
  if (!match) return [];
  const [_, __, endCol] = match;

  const endNum = xlsx.utils.decode_col(endCol);

  // Create an array of column letters from A to the last column
  return Array.from({ length: endNum + 1 }, (_, i) => xlsx.utils.encode_col(i));
};

export type FilePreviewMode = 'builder' | 'runner';

type FilePreviewProps = {
  file: File | null;
  isLoading: boolean;
  mode: FilePreviewMode;
  previewName: string;
  example: ExampleSet | undefined;
  fileTabs: FileTab[];
  activeFileId: string;
  onFileTabClick?: (fileId: string) => void;
  onFileTabClose?: (fileId: string) => void;
  currentTaskName?: string;
};
type FileTab = {
  id: string;
  name: string;
  displayName: string;
  type?: 'Input' | 'Output' | 'PreviousOutput';
  stepNumber?: number;
};
export const FilePreview = ({
  file,
  isLoading,
  mode,
  previewName,
  example,
  fileTabs,
  activeFileId,
  onFileTabClick,
  onFileTabClose,
  currentTaskName,
}: FilePreviewProps) => {
  const [sheets, setSheets] = useState<{
    [sheetName: string]: xlsx.WorkSheet;
  } | null>();
  const [activeTab, setActiveTab] = useState('');
  const [columnDefs, setColumnDefs] = useState<ColDef<{ [key: string]: unknown }>[]>([]);
  const [rowData, setRowData] = useState<{ [key: string]: unknown }[]>([]);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [processingState, setProcessingState] = useState<'not-started' | 'processing' | 'finished'>(
    'not-started',
  );
  // Add this new state to track if we're switching tabs vs loading new file
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [statusChange, setStatusChange] = useState<{
    fromStatus: ExampleSetStatus | null;
    toStatus: ExampleSetStatus | null;
    exampleSetId: string | null;
  }>({
    fromStatus: null,
    toStatus: null,
    exampleSetId: null,
  });
  const isStatusChangeDialogOpen = Boolean(
    statusChange.fromStatus && statusChange.toStatus && statusChange.exampleSetId,
  );
  const { workflowId, taskId } = useParams();
  // Refs for container measurements
  const gridContainerRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<AgGridReact>(null);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  useEffect(() => {
    const parseFile = async () => {
      setProcessingState('not-started');
      if (!file) return;

      const isExcel =
        file.name.endsWith('.xlsx') ||
        file.name.endsWith('.csv') ||
        file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

      try {
        setProcessingState('processing');
        const data = isExcel ? await parseExcel(file) : null;
        if (data?.Sheets) {
          setSheets(data.Sheets);
          const firstSheet = Object.keys(data.Sheets)[0];
          setActiveTab(firstSheet);
        }
      } catch {
        // NOTE: Only setting finished on `catch`, not on `finally`.
        // Updates here trigger another useEffect where processExcelSheets is called
        setProcessingState('finished');
      } finally {
        setIsInitialLoad(false);
      }
    };

    parseFile();
  }, [file]);

  // Set up ResizeObserver to monitor container size changes
  useEffect(() => {
    const currentRef = gridContainerRef.current; // Capture ref value
    if (!currentRef) return;

    // Clean up previous observer if it exists
    if (resizeObserverRef.current) {
      resizeObserverRef.current.disconnect();
    }

    // Create new observer
    resizeObserverRef.current = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry) {
        const { width, height } = entry.contentRect;
        setContainerSize({ width, height });
      }
    });

    // Start observing
    resizeObserverRef.current.observe(currentRef);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, []);

  // Calculate and add empty rows/columns to fill available space
  const calculateEmptyCells = useCallback(
    (dataRowCount: number, dataColCount: number) => {
      if (containerSize.width === 0 || containerSize.height === 0) {
        return { rowCount: dataRowCount, colCount: dataColCount };
      }

      // Apply adjustment factors to account for headers, borders, etc.
      const adjustedRowHeight = DEFAULT_ROW_HEIGHT;
      const adjustedColWidth = DEFAULT_COL_WIDTH;

      // Calculate visible area (subtract header height - ~40px)
      const gridHeight = containerSize.height - containerSize.height * 0.35;
      const gridWidth = containerSize.width - containerSize.width * 0.5;

      // Calculate how many rows/columns can fit in the container
      const visibleRows = Math.ceil(gridHeight / adjustedRowHeight);
      const visibleCols = Math.ceil(gridWidth / adjustedColWidth);

      // Only add empty rows/columns if we need them to fill space
      const emptyRowsToAdd = Math.max(0, visibleRows - dataRowCount);
      const emptyColsToAdd = Math.max(0, visibleCols - dataColCount);

      return {
        rowCount: dataRowCount + emptyRowsToAdd,
        colCount: dataColCount + emptyColsToAdd,
      };
    },
    [containerSize],
  );

  useEffect(() => {
    if (!sheets || !activeTab || !sheets[activeTab]) {
      return;
    }

    const processSheetData = () => {
      if (isInitialLoad) {
        setProcessingState('processing');
      }
      try {
        const sheet = sheets[activeTab];
        const range = getColumnLettersFromRange(sheet['!ref']);
        const sheetJson = xlsx.utils.sheet_to_json<Record<string, unknown>>(sheets[activeTab], {
          header: 'A',
          defval: '',
          blankrows: true,
          // use formatted string if available (e.g. for dates)
          raw: false,
        });
        if (sheetJson.length > 0) {
          // Original data dimensions
          const dataRowCount = sheetJson.length;
          const dataColCount = range.length;

          // Calculate needed empty cells
          const { rowCount, colCount } = calculateEmptyCells(dataRowCount, dataColCount);

          // Generate additional empty columns if needed
          const extendedRange = [...range];
          for (let i = dataColCount; i < colCount; i++) {
            extendedRange.push(xlsx.utils.encode_col(i));
          }

          setColumnDefs([
            {
              headerName: ' ',
              field: '__',
              width: 32,
              sortable: false,
              pinned: 'left',
              cellStyle: {
                backgroundColor: '#EDEDED',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              },
            },
            // I disabled sorting however if we want to enable sorting,
            // we would need to be sure the logic for csvs is using the actual csv headers as columnDefs because currently
            // they are defined in the sheetJson and included in the sorting since they are 'technically' a row
            ...extendedRange.map((col: string, _: number) => ({
              field: col,
              sortable: false,
            })),
          ]);
          // Generate rows including empty ones
          const extendedRows = [
            ...sheetJson.map((row: Record<string, unknown>, i: number) => ({
              ...row,
              __: i + 1,
            })),
          ];

          // Add empty rows
          for (let i = dataRowCount; i < rowCount; i++) {
            const emptyRow: Record<string, unknown> = { __: i + 1 };
            extendedRange.forEach((col) => {
              emptyRow[col] = '';
            });
            extendedRows.push(emptyRow as { __: number });
          }

          setRowData(extendedRows);
        }
      } finally {
        if (isInitialLoad) {
          setProcessingState('finished');
        }
      }
    };

    processSheetData();
  }, [activeTab, sheets, calculateEmptyCells, containerSize, isInitialLoad]);

  // Only show loading for initial file load or when explicitly loading
  const isCurrentlyLoading = isLoading || (processingState === 'processing' && isInitialLoad);

  // Add this check to prevent showing empty state when switching files
  const shouldShowEmptyState = !isCurrentlyLoading && !file && !isLoading;

  if (isCurrentlyLoading) {
    return <Loading />;
  }

  if (shouldShowEmptyState) {
    return (
      <Styled.EmptyFilePreview data-testid="empty-file-preview">
        <EmptyState />
        <div>
          <Text weight={5} size={5} lineHeight={4}>
            {t('components.Runner.noDataPreview')}
          </Text>
        </div>
      </Styled.EmptyFilePreview>
    );
  }

  // Remove the useCallback and make it a regular function
  const getFileWithoutClearButton = () => {
    if (!fileTabs || fileTabs.length === 0) return null;

    // Find current output first
    const currentOutput = fileTabs.find((tab) => tab.type === 'Output');
    if (currentOutput) {
      return currentOutput.id;
    }

    // If no current output, find first previous output
    const previousOutput = fileTabs.find((tab) => tab.type === 'PreviousOutput');
    if (previousOutput) {
      return previousOutput.id;
    }

    // If no outputs at all, find first input
    const firstInput = fileTabs.find((tab) => tab.type === 'Input');
    return firstInput?.id || null;
  };

  const statusColor = {
    ACTIVE: 'success',
    PUBLISHED: 'info',
    DRAFT: 'default',
    ARCHIVED: 'default',
    OUTDATED: 'default',
  };

  const fileWithoutClearButton = getFileWithoutClearButton();

  return (
    <Styled.FilePreviewPanel>
      <StatusChangeDialog
        isStatusChangeDialogOpen={isStatusChangeDialogOpen}
        statusChange={statusChange}
        setStatusChange={setStatusChange}
        workflowId={workflowId || ''}
        taskId={taskId || ''}
        makeActive={statusChange.fromStatus === 'PUBLISHED' && statusChange.toStatus === 'ACTIVE'}
      />
      <Styled.FilePreviewHeader>
        {!isCurrentlyLoading && file ? (
          <>
            <Styled.FilePreviewHeaderLeft>
              <Styled.FilePreviewMetaData>
                <Styled.StepName mode={mode}>
                  <Heading variant="body-base" weight="medium">
                    {previewName}
                  </Heading>
                </Styled.StepName>
                {mode === 'builder' && (
                  // TODO: Need to make a fix in flow-ui to allow truncation without text needing to be wrapped in an element
                  <TableStatusBadge
                    hasIcon={false}
                    truncateText={false}
                    color={example ? statusColor[example.status] : 'default'}
                  >
                    <p>{example?.name}</p>
                    {example && example.status === 'PUBLISHED' && (
                      <LinkButton
                        styleOverrides={{
                          text: {
                            fontSize: 'var(--flo-base-font-size-2)',
                            fontWeight: 'var(--flo-base-font-weight-5)',
                            lineHeight: 'var(--flo-base-line-height-2)',
                          },
                        }}
                        onClick={() => {
                          setStatusChange({
                            fromStatus: example.status,
                            toStatus: 'ACTIVE',
                            exampleSetId: example.id,
                          });
                        }}
                      >
                        {t('components.Runner.makeActive')}
                      </LinkButton>
                    )}
                  </TableStatusBadge>
                )}
              </Styled.FilePreviewMetaData>
              <Styled.FileTabs>
                {(fileTabs ?? []).map((tab) => {
                  const shouldShowClearButton = tab.id !== fileWithoutClearButton;

                  return (
                    <Styled.FileTab
                      key={tab.id}
                      active={tab.id === activeFileId}
                      onClick={() => {
                        onFileTabClick?.(tab.id);
                      }}
                      title={!shouldShowClearButton ? currentTaskName : tab.displayName}
                      hasCloseButton={shouldShowClearButton}
                    >
                      <Text size={4} weight={5} lineHeight={3}>
                        {!shouldShowClearButton ? currentTaskName : tab.displayName}
                      </Text>
                      {shouldShowClearButton && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onFileTabClose?.(tab.id);
                          }}
                        >
                          <Clear
                            style={{
                              width: '18px',
                              height: '18px',
                              flexShrink: 0,
                            }}
                          />
                        </button>
                      )}
                    </Styled.FileTab>
                  );
                })}
              </Styled.FileTabs>
            </Styled.FilePreviewHeaderLeft>
            <Tooltip>
              <Tooltip.Trigger>
                <IconButton
                  aria-label={t('components.Runner.downloadFile')}
                  size="md"
                  data-tracking-id="runner-download-file-button"
                  disabled={!file}
                  onClick={() => {
                    if (file) downloadFile(file, { fileName: file.name });
                  }}
                >
                  <Download />
                </IconButton>
              </Tooltip.Trigger>
              <Tooltip.Content style={{ zIndex: 9001 }}>
                {t('components.Runner.downloadFile')}
              </Tooltip.Content>
            </Tooltip>
          </>
        ) : null}
      </Styled.FilePreviewHeader>
      {!isCurrentlyLoading && file && sheets && (
        <Styled.FilePreview>
          <Styled.GridSection>
            <Styled.GridContainer ref={gridContainerRef}>
              <AgGridReact
                ref={gridRef}
                theme={gridFilePreviewTheme}
                columnDefs={columnDefs}
                rowData={rowData}
                getRowClass={(params) => {
                  // Mark rows beyond the original data count as empty
                  const originalDataRowCount =
                    sheets && activeTab
                      ? xlsx.utils.sheet_to_json(sheets[activeTab], { header: 'A' }).length
                      : 0;
                  return params.data && (params.data as { __: number }).__ > originalDataRowCount
                    ? 'empty-row'
                    : '';
                }}
                domLayout="normal"
                suppressCellFocus={true}
              />
            </Styled.GridContainer>
            <Styled.SheetTabs>
              {Object.keys(sheets).map((tabName) => (
                <Styled.SheetTab
                  key={tabName}
                  active={activeTab === tabName}
                  onClick={() => setActiveTab(tabName)}
                  title={tabName}
                >
                  <Text size={4} weight={5} lineHeight={3}>
                    {tabName}
                  </Text>
                </Styled.SheetTab>
              ))}
            </Styled.SheetTabs>
          </Styled.GridSection>
        </Styled.FilePreview>
      )}
    </Styled.FilePreviewPanel>
  );
};
