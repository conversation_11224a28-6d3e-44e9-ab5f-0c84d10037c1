import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Flex,
  Button,
  TabGroup,
  Tab,
  Divider,
  Input,
  Heading,
  Text,
} from '@floqastinc/flow-ui_core';
import Search from '@floqastinc/flow-ui_icons/material/SearchOutlined';
import { useMutation } from '@tanstack/react-query';
import { ProtectedComponent } from '@floqastinc/auth-module-client';
import { Workflow, WorkflowStatus } from '@floqastinc/transform-v3';
import { AgentsTable } from './ui/AgentsTable/AgentsTable';
import * as Styled from './AgentListPage.styles';
import { t } from '@/utils/i18n';
import { USER_ACTION_KEYS } from '@/authorization';
import { createWorkflow } from '@BuilderV3/api/workflows';
import { createTask } from '@BuilderV3/api/tasks';
import { createExample } from '@BuilderV3/api/examples';
import { AGENT, BUILDER, AGENTS, V3, STEPS, EXAMPLES } from '@/constants';
import { CreateWorkflowWizard } from '@BuilderV3/routes/workflows/components/CreateWorkflowWizard/CreateWorkflowWizard';
import { queryClient } from '@/components/queryClient';
import { queryKeys } from '@BuilderV3/api/query-keys';
import { useDebouncedState } from '@/hooks/useDebouncedState';

const ACTIVE = 'ACTIVE';
const DRAFT = 'DRAFT';
const ARCHIVED = 'ARCHIVED';
const CONNECTIONS = 'CONNECTIONS';

export const AgentListPage = () => {
  const [activeTab, setActiveTab] = useState<WorkflowStatus>(ACTIVE);
  const [search, setSearch, debouncedSearch] = useDebouncedState('', 1200);
  const [isWizardOpen, setIsWizardOpen] = useState(false);
  const navigate = useNavigate();

  const createWorkflowMutation = useMutation({
    mutationFn: createWorkflow,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.all(),
      });
    },
    onError: (error) => {
      console.error('error', error);
    },
  });

  const createIntegrationsStepMutation = useMutation({
    mutationFn: async (workflowId: string) => {
      const { task } = await createTask({
        workflowId,
        task: {
          name: 'Integrations',
          description: '',
          strategy: {
            kind: 'FLOLAKE',
            statement: '',
            sources: [],
          },
        },
      });

      const example = await createExample({
        workflowId,
        taskId: task.id,
        example: {
          status: 'DRAFT',
        },
      });

      return { task, example, workflowId };
    },
    onSuccess: ({ task, example, workflowId }) => {
      navigate(
        `/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${task.id}/${EXAMPLES}/${example.id}`,
      );
    },
    onError: (error, _) => {
      console.error('Failed to create Integrations step:', error);
    },
  });

  const handleSetActiveTab = (tab: WorkflowStatus | typeof CONNECTIONS) => {
    if (tab === CONNECTIONS) {
      window.open('/settings/connections', '_blank');
      return;
    }
    setActiveTab(tab);
  };

  return (
    <Styled.Wrapper>
      <Styled.Header>
        <Flex justify="space-between" align="center" padding="200px">
          <Heading variant="h2" weight="semibold">
            {t('components.AgentList.agents')}
          </Heading>
          <Flex align="center" justify="center" gap={16}>
            <Input
              styleOverrides={{ root: { width: '312px' } }}
              aria-label={t('components.AgentList.searchAgents')}
              isSearchable
              onChange={(value: string) => {
                setSearch(value);
              }}
              placeholder={t('generics.search')}
              value={search}
            >
              <Input.LeftItem>
                <Search size={20} />
              </Input.LeftItem>
            </Input>
            <ProtectedComponent actionKey={USER_ACTION_KEYS.TRANSFORM_WORKFLOW_WRITE}>
              <CreateWorkflowWizard
                isOpen={isWizardOpen}
                trigger={
                  <Button
                    onClick={() => {
                      setIsWizardOpen(true);
                    }}
                  >
                    <Styled.AddIcon />
                    <Text size={5} color={'white'}>
                      {t('generics.create')} {AGENT}
                    </Text>
                  </Button>
                }
                onOpenChange={setIsWizardOpen}
                onCompletion={(params: {
                  workflow: Pick<Workflow, 'name' | 'description' | 'entityId' | 'settings'>;
                  useCase: { name: string; content: string };
                  experimentAssignments: Record<string, string>;
                  initialInput: string | null;
                }) => {
                  const { workflow, useCase, experimentAssignments, initialInput } = params;

                  createWorkflowMutation.mutate(
                    {
                      workflow: {
                        ...workflow,
                        useCase: useCase.content,
                      },
                      experimentAssignments,
                    },
                    {
                      onSuccess: ({ workflow: createdWorkflow }) => {
                        queryClient.invalidateQueries({
                          queryKey: queryKeys.workflows.all(),
                        });

                        if (initialInput === 'upload-file') {
                          navigate(`/${BUILDER}/${V3}/${AGENTS}/${createdWorkflow.id}/inputs`);
                        } else if (initialInput === 'integration') {
                          createIntegrationsStepMutation.mutate(createdWorkflow.id);
                        } else {
                          navigate(`/${BUILDER}/${V3}/${AGENTS}/${createdWorkflow.id}`);
                        }
                      },
                    },
                  );
                }}
                isWorkflowLoading={
                  createWorkflowMutation.isPending || createIntegrationsStepMutation.isPending
                }
              />
            </ProtectedComponent>
          </Flex>
        </Flex>
      </Styled.Header>
      <ProtectedComponent actionKey={USER_ACTION_KEYS.TRANSFORM_WORKFLOW_WRITE}>
        <Styled.TabGroup>
          <TabGroup
            padding="1rem"
            activationMode="manual"
            defaultValue={activeTab}
            value={activeTab}
            onValueChange={handleSetActiveTab}
          >
            <Tab title={ACTIVE} tabId={ACTIVE} />
            <Tab title={DRAFT} tabId={DRAFT} />
            <Tab title={ARCHIVED} tabId={ARCHIVED} />
            <ProtectedComponent actionKey={USER_ACTION_KEYS.CONNECTIONS_FULL}>
              <Tab title={CONNECTIONS} tabId={CONNECTIONS} />
            </ProtectedComponent>
          </TabGroup>
        </Styled.TabGroup>
        <Divider />
      </ProtectedComponent>
      <Styled.TabContent>
        <AgentsTable search={debouncedSearch} activeTab={activeTab} />
      </Styled.TabContent>
    </Styled.Wrapper>
  );
};
