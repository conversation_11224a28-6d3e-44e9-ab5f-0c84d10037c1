import { FC, ReactNode } from 'react';
import { useRouteError } from 'react-router-dom';
import { Heading } from '@floqastinc/flow-ui_core';
import styled from 'styled-components';

const GenericCenteredError = styled.div<{ isRoot?: boolean }>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
`;

interface ErrorBoundaryProps {
  isRoot?: boolean;
  children?: ReactNode;
}

export const ErrorBoundary: FC<ErrorBoundaryProps> = ({
  isRoot = false,
  children = null,
}) => {
  const error = useRouteError();

  console.error(error);

  return (
    <GenericCenteredError isRoot={isRoot}>
      <Heading variant="h4">
        An unexpected error occurred while loading this page.
      </Heading>
      {error instanceof Error && error.message}
      {children}
    </GenericCenteredError>
  );
};
