/// <reference types="vite/client" />
/// <reference types="vite-plugin-svgr/client" />

interface ImportMetaEnv {
  // NOTE: Vite parses all values as strings
  readonly REACT_APP_INJECTABLE: 'true' | 'false';
  readonly REACT_APP_RUNTIME_MODE: 'injected' | 'standalone';
  readonly REACT_APP_DEVELOPMENT_AUTHORIZATION: string;
  readonly REACT_APP_NODE_ENV: 'development' | 'production';
  readonly REACT_APP_USE_PROXY: 'true' | 'false';
  readonly REACT_APP_USE_CLOSE_PROXY: 'true' | 'false';
  readonly REACT_APP_USE_LAMBDA_PROXY: 'true' | 'false';
  readonly REACT_APP_PROXY_ENV: string;
  readonly REACT_APP_LOCAL_LAMBDA_HOSTNAME: string;
  readonly REACT_APP_X_LILJWTY_GATE?: string;
  readonly REACT_APP_PROXY_EXCLUDE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
