import { describe, test, expect, beforeEach } from 'vitest';
import { ClientFeatureFlag } from './FeatureFlag';
import { customRender } from '@/utils/testing';

const Fallback = () => <p>Fallback</p>;
const FlaggedComponent = () => <p>Flag</p>;

describe('FeatureFlag', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  test(`GIVEN that feature flags are not enabled,
  AND children are provided to ClientFeatureFlag
  THEN nothing should render.`, async () => {
    const screen = customRender(
      <ClientFeatureFlag flag="anything">
        <FlaggedComponent />
      </ClientFeatureFlag>,
    );

    await expect.element(screen.getByText('Flag')).not.toBeInTheDocument();
  });

  test(`GIVEN that feature flags are not enabled,
AND render is provided to ClientFeatureFlag
THEN the fallback component should render.`, async () => {
    const screen = customRender(
      <ClientFeatureFlag
        flag="anything"
        render={(flagValue) => (flagValue ? <FlaggedComponent /> : <Fallback />)}
      />,
    );

    await expect.element(screen.getByText('Flag')).not.toBeInTheDocument();
    await expect.element(screen.getByText('Fallback')).toBeInTheDocument();
  });

  test(`GIVEN that feature flags are enabled,
AND children are provided to ClientFeatureFlag
AND the flag is not enabled
THEN nothing should render.`, async () => {
    localStorage.setItem('transform-feature-flags-enabled', 'true');
    localStorage.setItem('transform-feature-flags', JSON.stringify({ anything: false }));

    const screen = customRender(
      <ClientFeatureFlag flag="anything">
        <FlaggedComponent />
      </ClientFeatureFlag>,
    );

    await expect.element(screen.getByText('Flag')).not.toBeInTheDocument();
  });

  test(`GIVEN that feature flags are enabled,
AND render is provided to ClientFeatureFlag
AND the flag is not enabled
THEN the fallback component should render.`, async () => {
    localStorage.setItem('transform-feature-flags-enabled', 'true');
    localStorage.setItem('transform-feature-flags', JSON.stringify({ anything: false }));

    const screen = customRender(
      <ClientFeatureFlag
        flag="anything"
        render={(flagValue) => (flagValue ? <FlaggedComponent /> : <Fallback />)}
      />,
    );

    await expect.element(screen.getByText('Flag')).not.toBeInTheDocument();
    await expect.element(screen.getByText('Fallback')).toBeInTheDocument();
  });

  test(`GIVEN that feature flags are enabled,
AND children are provided to ClientFeatureFlag
AND the flag is enabled
THEN the flagged component should render.`, async () => {
    localStorage.setItem('transform-feature-flags-enabled', 'true');
    localStorage.setItem('transform-feature-flags', JSON.stringify({ anything: true }));

    const screen = customRender(
      <ClientFeatureFlag flag="anything">
        <FlaggedComponent />
      </ClientFeatureFlag>,
    );

    await expect.element(screen.getByText('Flag')).toBeInTheDocument();
  });

  test(`GIVEN that feature flags are enabled,
AND render is provided to ClientFeatureFlag
AND the flag is enabled
THEN the flagged component should render.`, async () => {
    localStorage.setItem('transform-feature-flags-enabled', 'true');
    localStorage.setItem('transform-feature-flags', JSON.stringify({ anything: true }));

    const screen = customRender(
      <ClientFeatureFlag
        flag="anything"
        render={(flagValue) => (flagValue ? <FlaggedComponent /> : <Fallback />)}
      />,
    );

    await expect.element(screen.getByText('Flag')).toBeInTheDocument();
    await expect.element(screen.getByText('Fallback')).not.toBeInTheDocument();
  });
});
