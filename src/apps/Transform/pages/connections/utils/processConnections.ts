import { ActiveConnection } from '../../../../../api/shared/types';

export const sortConnectionsByDate = (connections: ActiveConnection[]) => {
  return [...connections].sort((a, b) => {
    try {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    } catch (error) {
      console.error(error);
      return 0;
    }
  });
};

export const isStatusActive = (connection: ActiveConnection): boolean => {
  return !connection.connectionInfo?.status || connection.connectionInfo.status === 'active';
};

export const processActiveConnections = (
  connections: ActiveConnection[],
): Record<string, ActiveConnection[]> => {
  if (!connections || connections.length === 0) {
    return {};
  }

  // Step 1. Group by provider and filter for active status
  const groupedByProvider: Record<string, ActiveConnection[]> = {};

  connections.forEach((connection) => {
    const provider = connection.integrationSystem.toLowerCase();

    if (isStatusActive(connection)) {
      if (!groupedByProvider[provider]) {
        groupedByProvider[provider] = [];
      }
      groupedByProvider[provider].push(connection);
    }
  });

  // Step 2. Sort each group by date created (newest first)
  Object.keys(groupedByProvider).forEach((provider) => {
    groupedByProvider[provider] = sortConnectionsByDate(groupedByProvider[provider]);
  });

  return groupedByProvider;
};
