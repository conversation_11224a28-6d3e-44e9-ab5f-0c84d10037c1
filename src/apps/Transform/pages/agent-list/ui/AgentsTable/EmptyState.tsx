import { EmptyState as EmptyStateImg, Text } from '@floqastinc/flow-ui_core';
import styled from 'styled-components';
import { t } from '@/utils/i18n';
import { AGENTS } from '@/constants';

const StyledDiv = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
`;

type EmptyStateProps = {
  style?: React.CSSProperties;
};
export const EmptyState = (props: EmptyStateProps) => {
  return (
    <StyledDiv {...props}>
      <EmptyStateImg />
      <Text lineHeight={11} size={8} weight={5}>
        {t('generics.no')} {AGENTS.toLocaleLowerCase()} {t('generics.found')}
      </Text>
    </StyledDiv>
  );
};
