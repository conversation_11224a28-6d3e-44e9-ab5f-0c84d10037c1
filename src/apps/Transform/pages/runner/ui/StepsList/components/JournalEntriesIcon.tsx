type JournalEntriesIconProps = {
  className?: string;
};

export function JournalEntriesIcon({
  className = 'journalEntriesIcon',
}: JournalEntriesIconProps) {
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="40" height="40" fill="white" />
      <path
        d="M35.85 4.57056V32.7006C35.85 34.5306 34.77 36.2006 33.17 36.9006C32.78 37.0706 32.36 37.1805 31.92 37.2206C31.77 37.2306 31.63 37.2405 31.49 37.2405C29.08 37.2405 27.14 35.2906 27.14 32.8806H9.71002V4.57056H35.85Z"
        fill="url(#paint0_linear_567_8)"
      />
      <path
        d="M31.494 37.2407C29.0881 37.2407 27.1377 34.5544 27.1377 31.2407H1C1 34.5544 2.95035 37.2407 5.35626 37.2407H31.494Z"
        fill="url(#paint1_linear_567_8)"
      />
      <path
        d="M25.1097 20.4049L19.9384 21.4392L20.9727 16.2679L33.3838 3.85681C34.5262 2.7144 36.3784 2.7144 37.5208 3.85681C38.6632 4.99922 38.6632 6.85144 37.5208 7.99385L25.1097 20.4049Z"
        fill="#6B2F27"
      />
      <defs>
        <linearGradient
          id="paint0_linear_567_8"
          x1="9.60623"
          y1="32.7768"
          x2="36.8312"
          y2="5.55176"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E8A299" />
          <stop offset="0.27509" stopColor="#D88175" />
          <stop offset="0.57485" stopColor="#CA6455" />
          <stop offset="0.82727" stopColor="#C25242" />
          <stop offset="1" stopColor="#BF4C3B" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_567_8"
          x1="8.59405"
          y1="41.8937"
          x2="23.1924"
          y2="27.2953"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E8A299" />
          <stop offset="0.27509" stopColor="#D88175" />
          <stop offset="0.57485" stopColor="#CA6455" />
          <stop offset="0.82727" stopColor="#C25242" />
          <stop offset="1" stopColor="#BF4C3B" />
        </linearGradient>
      </defs>
    </svg>
  );
}
