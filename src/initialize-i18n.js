/* eslint-disable i18next/no-literal-string */
import i18next from 'i18next';
import { initializeForBackend, initializeForFrontend, getLanguage } from '@floqastinc/fq-intl';
import { init_i18next_flowui } from '@floqastinc/flow-ui_core';
import { useI18n } from '@floqastinc/fq-intl-react';

import enTranslation from './locales/en/translation.json';
import frTranslation from './locales/fr/translation.json';
import deTranslation from './locales/de/translation.json';
import jaTranslation from './locales/ja/translation.json';

// Initialize i18n at module level - wrap in a function to avoid React Hook usage issue
const setupI18n = () => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useI18n(i18next);
};

setupI18n();

const initializeI18N = async () => {
  if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'test') {
    // init fq-intl for test environment
    initializeForBackend({ language: 'en', formatRegion: 'US' });
  } else if (initializeForFrontend) {
    // init fq-intl here in standalone mode
    initializeForFrontend();
  } else {
    // integrated mode - no need to initialize since fq-intl-nfe takes care of that
  }

  // eslint-disable-next-line import-x/no-named-as-default-member
  await i18next.init({
    lng: getLanguage(),
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

  // WORKAROUND: Adding translations after initialization
  // Using the resources field in i18next.init() makes components show raw keys instead of translations
  i18next.addResourceBundle('en', 'translation', enTranslation, true, true);
  i18next.addResourceBundle('fr', 'translation', frTranslation, true, true);
  i18next.addResourceBundle('de', 'translation', deTranslation, true, true);
  i18next.addResourceBundle('ja', 'translation', jaTranslation, true, true);

  init_i18next_flowui(i18next);
};

export default initializeI18N();
