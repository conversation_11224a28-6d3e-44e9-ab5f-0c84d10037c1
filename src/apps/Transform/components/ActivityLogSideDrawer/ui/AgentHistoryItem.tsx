import { TableStatusBadge, Flex } from '@floqastinc/flow-ui_core';
import { RunStatus } from '@floqastinc/transform-v3';
import { forwardRef } from 'react';
import { FQIntl } from '@floqastinc/fq-intl';
import * as Styled from './AgentHistoryItem.styles';
import { t } from '@/utils/i18n';
import { getRunStatusDisplayProperties } from '@Transform/shared/lib/workflow-runs';

type AgentHistoryItemProps = {
  /** NOTE: In the event that the principals query fails, just don't render this
   instead of failing the entire component. */
  runBy?: string;
  status: RunStatus;
  createdAt: Date;
  runLink: string;
};

export const AgentHistoryItem = forwardRef<HTMLDivElement, AgentHistoryItemProps>(
  ({ runBy, status, createdAt, runLink }, ref) => {
    const dateFormatter = new FQIntl.DateTimeFormat({ dateStyle: 'short' });
    const timeFormatter = new FQIntl.DateTimeFormat({ timeStyle: 'short' });
    const formattedDate = dateFormatter.format(createdAt);
    const formattedTime = timeFormatter.format(createdAt);

    const { badgeColor, statusText } = getRunStatusDisplayProperties(status);

    return (
      <Styled.Link to={runLink}>
        <Styled.AgentHistoryButton
          color="dark"
          variant="outlined"
          onClick={() => {}}
          data-tracking-id="runner-agent-history-item"
        >
          <Flex
            direction="column"
            height="100%"
            justify="center"
            align="flex-start"
            gap="0px"
            width="100%"
            ref={ref}
          >
            <Styled.RunInfo>
              <div>
                <Styled.RunByContainer>
                  {runBy ? (
                    <>
                      {t('components.ActivityLog.ActvityHistoryItem.runBy')}{' '}
                      <strong>{runBy}</strong>
                    </>
                  ) : null}
                </Styled.RunByContainer>
                {badgeColor && statusText ? (
                  <TableStatusBadge hasIcon={false} color={badgeColor} size="sm">
                    {statusText}
                  </TableStatusBadge>
                ) : null}
              </div>
            </Styled.RunInfo>
            <Styled.RunTimeStamp>
              <Styled.SmallCalendarMonth />
              <Styled.RunDate>{formattedDate}</Styled.RunDate>
              <span>{formattedTime}</span>
            </Styled.RunTimeStamp>
          </Flex>
          <Styled.ArrowForwardNeutral />
        </Styled.AgentHistoryButton>
      </Styled.Link>
    );
  },
);
AgentHistoryItem.displayName = 'AgentHistoryItem';
