import v3, { ApiError } from '@/services/v3';

/**
 * Duplicates a JEM template.
 * @param taskId - The ID of the task.
 * @param exampleSetId - The ID of the example set.
 * @param workflowId - The ID of the workflow.
 * @returns A promise that resolves when the duplication is complete.
 */
export default async function duplicateJemTemplate(
  taskId: string,
  exampleSetId: string,
  workflowId: string,
) {
  try {
    if (!taskId || !exampleSetId) {
      console.error('Task ID or Example Set ID is missing.');
      return;
    }

    // This is not a simple get, it also duplicates the JEM template:
    // https://github.com/FloQastInc/transform_api/blob/v3.28.0/lambdas/api/src/v3/services/jem-template-service.ts#L56
    const jemTemplate = await v3.jemTemplate.getJemTemplate({
      workflowId,
      taskId,
      exampleSetId,
    });

    if (jemTemplate.errors?.length) {
      throw new ApiError(jemTemplate.errors);
    }
  } catch (error) {
    console.error('Error fetching JEM Template:', error);
  }
}
