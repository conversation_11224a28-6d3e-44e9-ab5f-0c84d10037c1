import { useEffect, useRef, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { t } from '@/utils/i18n';
import { useMutation, useMutationState, useQuery, useSuspenseQuery } from '@tanstack/react-query';
import { SplitButton } from '@floqastinc/flow-ui_composite';
import {
  But<PERSON>,
  Heading,
  Spinner,
  DropdownButton,
  DropdownPanel,
  TableStatusBadge,
  StatusDot,
  Tooltip,
} from '@floqastinc/flow-ui_core';
import ChevronLeft from '@floqastinc/flow-ui_icons/material/ChevronLeft';
import Save from '@floqastinc/flow-ui_icons/material/Save';
import {
  ImperativePanelHandle,
  Panel,
  PanelGroup,
  PanelResizeHandle,
} from 'react-resizable-panels';
import { useAtom, useAtomValue } from 'jotai';
import { ExampleSet, Task, WorkflowStatus } from '@floqastinc/transform-v3';
import { match } from 'ts-pattern';
import { SQLEditor } from './components/SQLEditor/SQLEditor';
import { DropdownOptions, InputValue } from './components/SQLEditor/types';
import * as Styled from './styled';
import { ChatWindow } from './components/ChatWindow/ChatWindow';
import { useExampleQueries, useTaskQueries, useUpdateWorkflow } from './BuilderPage.hooks';
import { ExpandButton, ResizeHandle } from './components/ResizeHandle/ResizeHandle';
import {
  activeSheetAtom,
  collapsedPanelAtom,
  fileAtom,
  selectedFileAtom,
  selectedRangeAtom,
  shouldRefetchTasksAtom,
} from './BuilderPage.store';
import { FileDropdown } from './components/FileDropdown/FileDropdown';
import { PanelViewDropdown } from './components/PanelViewDropdown/PanelViewDropdown';
import { VersionSlideout } from './components/VersionSlideout/VersionSlideout';
import { getStatus } from './components/VersionSlideout/StatusDropdown';
import { FilePreview } from '@BuilderV3/app-components/FilePreview';
import { StyledLinkButton } from '@/components/Link';
import { getWorkflowQuery } from '@BuilderV3/api/workflows';
import { PageTitle } from '@/components/PageTitle';
import { getExampleInputFileUriQuery } from '@BuilderV3/api/example-inputs';
import { getExampleOutputFileUriQuery } from '@BuilderV3/api/example-outputs';
import { getFileFromUri } from '@BuilderV3/api/files';
import { Steps } from '@BuilderV3/app-components/Steps/Steps';
import { createExample } from '@BuilderV3/api/examples';
import { queryKeys } from '@BuilderV3/api/query-keys';
import { queryClient } from '@/components/queryClient';
import v3, { ApiError } from '@/services/v3';
import { useModal } from '@/components/ModalContext';
import { getWorkflowTasksQuery, updateTask } from '@BuilderV3/api/tasks';
import { useFeatureFlags } from '@/components/FeatureFlag';
import { AGENTS, BUILDER, EXAMPLES, STEPS, V3 } from '@/constants';
import { getIsChatBasedStrategy } from '@/utils/strategy';
import { DetailsSlideout } from '@BuilderV3/routes/workflows/components/DetailsSlideout/DetailsSlideout';
import { Inputs } from '@BuilderV3/app-components/Inputs/Inputs';

export const BuilderPage = () => {
  const navigate = useNavigate();

  const { workflowId = '', taskId = '', exampleSetId = '' } = useParams();
  if (!workflowId || !taskId || !exampleSetId) {
    console.warn(t('components.Builder.Errors.noIDsprovided'));
    navigate(`/${BUILDER}`);
  }

  const { data: workflow } = useSuspenseQuery(getWorkflowQuery(workflowId));
  const { openModal } = useModal();
  const { getFlag } = useFeatureFlags();

  const [shouldRefetchTasksOverride, setRefetchTasksOverride] = useAtom(shouldRefetchTasksAtom);

  const [isTaskDetailsSlideoutOpen, setIsTaskDetailsSlideoutOpen] = useState(false);

  // Tasks
  const tasksQuery = useQuery({
    ...getWorkflowTasksQuery(workflowId),
    refetchInterval: (query) => {
      const tasks = query.state.data;
      if (tasks) {
        const currTask = tasks.find((task) => task.id === taskId);
        const buildStatus =
          currTask?.strategy.kind === 'SCRIPT' ? currTask.strategy.buildStatus : undefined;
        // reset refetch override when the build status is finally busy
        if (buildStatus === 'BUSY') {
          setRefetchTasksOverride(false);
        }
        // Optimistically refetch every 2 seconds until not busy
        if (shouldRefetchTasksOverride || buildStatus === 'BUSY') {
          return 2000;
        }
      }
      return undefined;
    },
  });
  const { currentTask, handleTaskNameEdit, createTaskMutation } = useTaskQueries();
  const lastTask = tasksQuery.data?.[tasksQuery.data.length - 1];
  const isLastTask = lastTask?.id === taskId;

  const taskStrategy = currentTask?.strategy;
  // Once a script is compiled, the strategy remains SCRIPT for all subsequent versions
  const isChatBasedStrategy = getIsChatBasedStrategy(taskStrategy);
  const isFlolakeStrategy = taskStrategy?.kind === 'FLOLAKE';
  const isJemBasedStrategy =
    taskStrategy?.kind === 'JEM_EXPORT' || taskStrategy?.kind === 'JEM_TEMPLATE_FETCH';
  const isReviewStrategy = taskStrategy?.kind === 'REVIEW';

  // Examples
  // TODO: Replace with task/:taskId/sources call
  const { examplesQuery, exampleInputsQuery, exampleOutputsQuery } = useExampleQueries();
  const exampleIsLoading =
    (exampleInputsQuery.isPending && exampleInputsQuery.fetchStatus !== 'idle') ||
    (exampleOutputsQuery.isPending && exampleOutputsQuery.fetchStatus !== 'idle');
  // NOTE/TEMP: This assumes a single output. This may not always
  //  be the case in the future if we support multi-output
  const firstOutputFile = exampleOutputsQuery.data?.filter(
    (output) => output.value?.kind === 'FILE',
  )[0];

  const createExampleMutation = useMutation({
    mutationFn: (copySelectedVersion: boolean) => {
      return createExample({
        workflowId,
        taskId,
        example: {
          status: 'DRAFT',
          copyFromExampleSetId: copySelectedVersion ? exampleSetId : undefined,
        },
      });
    },
    onSuccess: (newExample) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExamples({
          workflowId,
          taskId,
        }),
      });
      navigate(
        `/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${taskId}/${EXAMPLES}/${newExample.id}`,
      );
    },
  });

  const updateExampleMutation = useMutation({
    mutationFn: (example: Partial<ExampleSet>) => {
      return v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId,
        example,
      });
    },
    onSuccess: (exampleResponse) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExample({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExamples({
          workflowId,
          taskId,
        }),
      });
      if (isChatBasedStrategy && exampleResponse.data?.status === 'ACTIVE') {
        setRefetchTasksOverride(true);
        queryClient.invalidateQueries({
          queryKey: queryKeys.tasks.byWorkflow(workflowId),
        });
      }
    },
  });

  const updateTaskMutation = useMutation({
    mutationFn: async (currentExample: Partial<ExampleSet>) => {
      if (!currentTask) {
        throw new Error(t('components.Builder.Errors.currentTaskNotFound'));
      }

      const task = {
        ...currentTask,
        strategy: {
          ...currentExample.strategy,
        },
      };

      return updateTask({
        workflowId,
        taskId,
        task,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.byId(workflowId),
      });
    },
    onError: (data) => {
      console.error(t('components.Builder.Errors.errorUpdatingTask'), data);
    },
  });

  // Files
  const selectedFile = useAtomValue(selectedFileAtom);

  const exampleInputFileUri = useQuery({
    ...getExampleInputFileUriQuery({
      workflowId,
      taskId,
      exampleSetId,
      // @ts-expect-error TypeScript can't narrow this from the `enabled` field, but this should be correct based on the file logic.
      exampleInputId: selectedFile?.id,
    }),
    enabled: selectedFile?.type === 'input',
  });

  const exampleOutputFileUri = useQuery({
    ...getExampleOutputFileUriQuery({
      workflowId,
      taskId,
      exampleSetId,
      // @ts-expect-error TypeScript can't narrow this from the `enabled` field, but this should be correct based on the file logic.
      exampleOutputId: selectedFile?.id ?? firstOutputFile?.id,
    }),
    enabled: selectedFile?.type === 'output' || !!firstOutputFile,
  });

  const [inputValues, setInputValues] = useState<InputValue[]>([]);
  const [selectedQuery, setSelectedQuery] = useState<DropdownOptions>({
    id: '',
    name: '',
  });
  const [loading, setLoading] = useState(false);
  const [file, setFile] = useAtom(fileAtom);

  const [fileIsLoading, setFileIsLoading] = useState(false);
  useEffect(() => {
    const fileUri =
      selectedFile?.type === 'input'
        ? exampleInputFileUri.data?.data?.url
        : exampleOutputFileUri.data?.data?.url;

    if (fileUri) {
      setFileIsLoading(true);
      getFileFromUri(fileUri)
        .then((file) => {
          setFile(file);
        })
        .catch((error) => {
          console.error(t('components.Builder.Errors.fileFromURI'), error);
        })
        .finally(() => {
          setFileIsLoading(false);
        });
    }
  }, [exampleInputFileUri.data?.data?.url, exampleOutputFileUri.data?.data?.url]);

  const [selectedRange, setSelectedRange] = useAtom(selectedRangeAtom);
  const [activeSheet, setActiveSheet] = useAtom(activeSheetAtom);
  const [currentQuery, setCurrentQuery] = useState<string>('');

  const [collapsedPanel, setCollapsedPanel] = useAtom(collapsedPanelAtom);
  const chatPanelRef = useRef<ImperativePanelHandle>(null);
  const filePanelRef = useRef<ImperativePanelHandle>(null);

  const [selectedView, setSelectedView] = useState(sessionStorage.getItem('panelView') ?? 'File');

  const [isVersionSlideoutOpen, setIsVersionSlideoutOpen] = useState(false);
  const [isDropDownPanelOpen, setDropDownPanelOpen] = useState(false);
  const [workflowStatus, setWorkflowStatus] = useState(workflow?.status);
  const updateWorkflow = useUpdateWorkflow();
  const handleOnChange = async (value: WorkflowStatus) => {
    if (value !== workflowStatus) {
      workflow.status = value;
      await updateWorkflow.mutateAsync(workflow);
      setWorkflowStatus(value);
    }
    setDropDownPanelOpen(false);
  };
  const isAddingInput = useMutationState({
    filters: {
      mutationKey: [
        'addInput',
        {
          workflowId,
          taskId,
          exampleSetId,
        },
      ],
    },
    select: (mutation) => mutation.state.status,
  }).some((status) => status === 'pending');

  const resetPanel = () => {
    const panel = chatPanelRef.current;
    if (panel) {
      panel.resize(50);
    }
  };

  const expandFilePanel = (percentage?: number) => {
    const panel = filePanelRef.current;
    if (panel) {
      panel.resize(percentage ?? 33);
    }
  };

  const expandChatPanel = (percentage?: number) => {
    const panel = chatPanelRef.current;
    if (panel) {
      panel.resize(percentage ?? 20);
    }
  };

  const currentExample = examplesQuery.data?.find((example) => example.id === exampleSetId);
  const isActiveExample = currentExample?.status === 'ACTIVE';
  const hasActiveExample = examplesQuery.data?.some((example) => example.status === 'ACTIVE');

  const taskBuildStatus =
    currentTask?.strategy.kind === 'SCRIPT' ? currentTask.strategy.buildStatus : undefined;
  const hasCompiledScript = isActiveExample && taskBuildStatus === 'COMPILED';
  const hasFailedScript = isActiveExample && taskBuildStatus === 'FAILED';
  const hasBusyScript =
    isChatBasedStrategy && isActiveExample && !hasCompiledScript && !hasFailedScript;

  const getStatusTooltipCopy = () => {
    if (isChatBasedStrategy) {
      if (currentExample?.status === 'ARCHIVED') {
        return t('components.Builder.thisVersionIsArchived');
      } else if (currentExample?.status === 'DRAFT') {
        return t('components.Builder.thisVersionIsDraft');
      } else if (currentExample?.status === 'PUBLISHED') {
        return t('components.Builder.thisVersionIsPublished');
      } else if (isActiveExample) {
        if (hasCompiledScript) {
          return t('components.Builder.readyToRun');
        } else if (hasFailedScript) {
          return t('components.Builder.reachToSupport');
        }
        // Default to busy if not compiled or failed
        return t('components.Builder.pleaseWaitStep');
      }
    }
    return null;
  };

  const getIsPublishable = () => {
    // Add more cases here as needed
    if (isChatBasedStrategy || isFlolakeStrategy || taskStrategy?.kind === 'JEM_TEMPLATE_FETCH') {
      return exampleOutputsQuery.data?.some((output) => Boolean(output.value));
    } else if (isReviewStrategy || taskStrategy?.kind === 'JEM_EXPORT') {
      return exampleInputsQuery.data?.some((input) => Boolean(input.value));
    }
    return false;
  };

  const getResizePanel = () => {
    if (isJemBasedStrategy || isReviewStrategy) {
      return null;
    }
    return !collapsedPanel ? (
      <PanelResizeHandle
        style={{
          display: 'flex',
          flexDirection: 'column',
          width: collapsedPanel ? '24px' : '5px',
          padding: collapsedPanel ? '0 12px' : '0',
          alignItems: 'center',
          justifyContent: 'flex-start',
          borderTop: '1px solid var(--flo-base-color-neutral-300)',
        }}
        onDoubleClick={resetPanel}
      >
        <ResizeHandle />
      </PanelResizeHandle>
    ) : (
      <ExpandButton
        direction={collapsedPanel && collapsedPanel === 'chat' ? 'right' : 'left'}
        onClick={() => {
          if (collapsedPanel === 'chat') {
            expandChatPanel();
          } else if (collapsedPanel === 'file') {
            expandFilePanel();
          }
        }}
      />
    );
  };

  const fetchJemTemplate = async (
    taskStrategy: any,
    currentTask: any,
    currentExample: any,
    workflowId: string,
  ) => {
    if (taskStrategy?.kind === 'JEM_TEMPLATE_FETCH') {
      try {
        if (!currentTask?.id || !currentExample?.id) {
          console.error(t('components.Builder.Errors.taskExampleIDMissing'));
          return;
        }

        const jemTemplate = await v3.jemTemplate.getJemTemplate({
          workflowId,
          taskId: currentTask.id,
          exampleSetId: currentExample.id,
        });

        if (jemTemplate.errors?.length) {
          throw new ApiError(jemTemplate.errors);
        }
      } catch (error) {
        console.error(t('components.Builder.Errors.fetchJEMTemplate'), error);
      }
    }
  };

  const handleEdit = async (copySelectedVersion: boolean) => {
    setLoading(true);
    try {
      if (taskStrategy?.kind === 'JEM_TEMPLATE_FETCH') {
        await fetchJemTemplate(taskStrategy, currentTask, currentExample, workflowId);
      }
      createExampleMutation.mutate(copySelectedVersion);
    } catch (error) {
      console.error(t('components.Builder.Errors.fetchMutation'), error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Styled.Page>
      {createExampleMutation.isPending ? (
        <Styled.Centered>
          <Spinner color="success" size={48} />
        </Styled.Centered>
      ) : null}
      <PageTitle title={`Builder - ${'banana'}`} />
      <Styled.BackButton>
        <StyledLinkButton onClick={() => navigate(`/${BUILDER}/${V3}`)}>
          <ChevronLeft color="var(--flo-base-color-neutral-500)" size={16} />
          {t('components.Runner.allAgents')}
        </StyledLinkButton>
      </Styled.BackButton>
      <Styled.PageHeader>
        <Heading variant="h5" weight="regular">
          {workflow.name}
        </Heading>
        {currentTask?.name && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Heading variant="h2" weight="semibold">
              {t('components.Builder.stepTaskName', { currentTask: currentTask.name })}
            </Heading>
            <Styled.EditIcon
              color="var(--flo-base-color-neutral-500)"
              size={16}
              onClick={handleTaskNameEdit}
            />
            {currentExample?.status ? (
              <TableStatusBadge
                hasIcon={false}
                color={match(currentExample?.status)
                  .with('ACTIVE', () => {
                    if (hasCompiledScript || !isChatBasedStrategy) {
                      return 'success';
                    } else if (hasFailedScript) {
                      return 'danger';
                    }
                    // Default to busy if not compiled or failed
                    return 'info';
                  })
                  .with('DRAFT', () => 'default')
                  .with('PUBLISHED', () => 'info')
                  .with('ARCHIVED', () => 'warning')
                  .run()}
              >
                <div style={{ display: 'flex' }}>
                  {getStatus(currentExample?.status)}
                  {isActiveExample && (
                    <Tooltip>
                      <Tooltip.Trigger>
                        <StatusDot
                          style={{
                            animation: hasBusyScript ? 'blinker 1s linear infinite' : '',
                            marginLeft: '3px',
                          }}
                          color={match(currentExample?.status)
                            .with('ACTIVE', () => {
                              if (hasCompiledScript || !isChatBasedStrategy) {
                                return 'success';
                              } else if (hasFailedScript) {
                                return 'danger';
                              }
                              // Default to busy if not compiled or failed
                              return 'info';
                            })
                            .with('DRAFT', () => 'default')
                            .with('PUBLISHED', () => 'info')
                            .with('ARCHIVED', () => 'warning')
                            .run()}
                        />
                      </Tooltip.Trigger>
                      <Tooltip.Content
                        side
                        sideOffset={20}
                        align="end"
                        style={{ maxWidth: '175px' }}
                      >
                        {getStatusTooltipCopy()}
                      </Tooltip.Content>
                    </Tooltip>
                  )}
                </div>
              </TableStatusBadge>
            ) : null}
          </div>
        )}
      </Styled.PageHeader>
      <Styled.ButtonRow>
        <DropdownPanel
          isOpen={isDropDownPanelOpen}
          onOpenChange={setDropDownPanelOpen}
          onChange={handleOnChange}
          selectedValues={workflow.status}
          selectionMode="single"
          data-tracking-id="builder-agent-status-dropdown"
          disableFilter={true}
          disableClear={true}
        >
          <DropdownPanel.Trigger>
            <DropdownButton
              open={isDropDownPanelOpen}
            >{`${workflow.status[0]}${workflow.status.slice(1, workflow.status.length).toLowerCase()}`}</DropdownButton>
          </DropdownPanel.Trigger>
          <DropdownPanel.Content>
            <DropdownPanel.Option value="DRAFT">{t('generics.draft')}</DropdownPanel.Option>
            {workflow.humanInTheLoop ? (
              <DropdownPanel.Option value="ACTIVE">{t('generics.active')}</DropdownPanel.Option>
            ) : (
              <DropdownPanel.Option
                value="ACTIVE"
                isDisabled={true}
                tooltipProps={{
                  contentText: t('components.AgentList.oneHumanStep'),
                  contentProps: {
                    hasArrow: false,
                    style: {
                      whiteSpace: 'normal',
                      maxWidth: '150px',
                      wordBreak: 'break-word',
                    },
                  },
                }}
                style={{
                  fontWeight: 400,
                  color: 'var(--flo-base-color-neutral-400)',
                }}
              >
                {t('generics.active')}
              </DropdownPanel.Option>
            )}
            <DropdownPanel.Option value="ARCHIVED">{t('generics.archived')}</DropdownPanel.Option>
          </DropdownPanel.Content>
        </DropdownPanel>
        <Button
          color="dark"
          variant="outlined"
          disabled={!file}
          data-tracking-id="builder-download-file-button"
          onClick={() => {
            if (!file) return;
            const url = URL.createObjectURL(file);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${workflow.name} - ${file.name}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
          }}
        >
          {t('generics.downloadFile')}
        </Button>
        <DropdownButton
          data-tracking-id="builder-version-slideout"
          onClick={() => {
            setIsVersionSlideoutOpen(!isVersionSlideoutOpen);
          }}
        >
          {currentExample?.name ||
            `Version ${currentExample?.createdAt.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
              hour: 'numeric',
              minute: '2-digit',
              second: '2-digit',
            })}`}
        </DropdownButton>
        <VersionSlideout
          show={isVersionSlideoutOpen}
          onClose={() => setIsVersionSlideoutOpen(false)}
        />
        {currentExample?.status !== 'DRAFT' ? (
          <SplitButton
            actionText={t('components.Builder.edit')}
            disableClear
            disableFilter
            onActionClick={() => handleEdit(false)}
            onChange={() => handleEdit(true)}
            options={[{ name: 'Copy selected version', value: 'copy' }]}
            propOverrides={{
              dropdownPanelContent: {
                align: 'end',
                style: { width: '180px', minWidth: 'fit-content' },
              },
              dropdownButton: {
                'data-tracking-id': 'builder-edit-button-copy',
                disabled: !isChatBasedStrategy,
              },
              actionButton: {
                'data-tracking-id': 'builder-edit-button-new',
                disabled: isReviewStrategy,
              },
            }}
          />
        ) : null}
        {loading || createExampleMutation.isPending ? (
          <Styled.Centered>
            <Spinner color="success" size={48} />
          </Styled.Centered>
        ) : null}
        {currentExample?.status === 'DRAFT' ? (
          <Button
            variant="outlined"
            data-tracking-id="builder-publish-button"
            onClick={() => {
              // UPDATE If there is only one example, make it active for user
              if (examplesQuery.data?.length === 1) {
                updateExampleMutation.mutate({
                  status: 'ACTIVE',
                });
                // IF THIS IS FLOLAKE STRATEGY, update the task to match the example since this is active
                if (isFlolakeStrategy) {
                  updateTaskMutation.mutate(currentExample);
                }
              } else {
                updateExampleMutation.mutate({
                  status: 'PUBLISHED',
                });
              }
            }}
            // Only enable publishing if an output file is available
            disabled={!getIsPublishable()}
          >
            <Save color="currentColor" />
            {t('generics.publish')}
          </Button>
        ) : null}
        <Button
          data-tracking-id="builder-add-new-step-button"
          onClick={() => {
            openModal('AddTask', {
              onSave: (task: Task) => createTaskMutation.mutate(task),
            });
          }}
          // Only enable adding a new task if there is an active example and the current task is the last task
          disabled={!hasActiveExample || !isLastTask}
          style={{ width: 'max-content' }}
        >
          {t('components.Builder.addNewStep')}
        </Button>
        <Button
          data-tracking-id="builder-view-details-button"
          onClick={() => {
            setIsTaskDetailsSlideoutOpen(true);
          }}
        >
          {t('components.Builder.viewDetails')}
        </Button>
      </Styled.ButtonRow>
      <PanelGroup
        direction="horizontal"
        style={{
          width:
            process.env.REACT_APP_RUNTIME_MODE === 'standalone'
              ? 'calc(100vw - 16px)'
              : 'calc(100vw - 56px)',
        }}
      >
        {/* Only render file preview for jem strategies  */}
        {!(isJemBasedStrategy || isReviewStrategy) && (
          <Panel
            collapsible={true}
            collapsedSize={0}
            minSize={20}
            defaultSize={50}
            onCollapse={() => {
              setCollapsedPanel('chat');
            }}
            onExpand={() => {
              setCollapsedPanel(null);
            }}
            ref={chatPanelRef}
          >
            <Styled.ChatWrapper>
              {/*
                This is a temporary solution to hide the chat window for non-LLM_THREAD tasks.
                This will be replaced with a more robust solution in the future.
              */}
              {isChatBasedStrategy && (
                <ChatWindow selectedRange={selectedRange} activeSheet={activeSheet} />
              )}
              {isFlolakeStrategy && (
                <SQLEditor
                  selectedQuery={selectedQuery}
                  setSelectedQuery={setSelectedQuery}
                  setInputValues={setInputValues}
                  inputValues={inputValues}
                  currentQuery={currentQuery}
                  setCurrentQuery={setCurrentQuery}
                  setFileIsLoading={setFileIsLoading}
                  setSelectedView={setSelectedView}
                />
              )}
            </Styled.ChatWrapper>
          </Panel>
        )}
        {getResizePanel()}
        <Panel
          collapsible={true}
          collapsedSize={0}
          minSize={33}
          defaultSize={50}
          onCollapse={() => {
            setCollapsedPanel('file');
          }}
          onExpand={() => {
            setCollapsedPanel(null);
          }}
          ref={filePanelRef}
        >
          {collapsedPanel !== 'file' ? (
            <Styled.FilePreview>
              {tasksQuery.data?.length && (exampleIsLoading || isAddingInput || fileIsLoading) ? (
                <Styled.Centered>
                  <Spinner color="success" size={48} />
                  {getFlag('file-context-enabled') && (
                    <Styled.Text>{t('components.Builder.analyzingFiles')}</Styled.Text>
                  )}
                </Styled.Centered>
              ) : null}
              <Styled.RightPanelControls>
                <Styled.RightPanelButtonContainer>
                  <PanelViewDropdown
                    isFloLakeStrategy={isFlolakeStrategy}
                    selectedView={selectedView}
                    setSelectedView={setSelectedView}
                  />
                </Styled.RightPanelButtonContainer>
                <Styled.RightPanelButtonContainer>
                  {selectedView === 'File' ? <FileDropdown /> : null}
                </Styled.RightPanelButtonContainer>
              </Styled.RightPanelControls>
              {file && selectedView === 'File' ? (
                <Styled.FilePreviewContainer>
                  <FilePreview
                    file={file}
                    setSelectedRange={setSelectedRange}
                    setActiveSheet={setActiveSheet}
                  />
                </Styled.FilePreviewContainer>
              ) : null}
              {selectedView === 'Steps' ? <Steps /> : null}
              {selectedView === 'Inputs' && isFlolakeStrategy ? (
                <Inputs
                  inputValues={inputValues}
                  setInputValues={setInputValues}
                  isDraft={currentExample?.status === 'DRAFT'}
                />
              ) : null}
            </Styled.FilePreview>
          ) : null}
        </Panel>
      </PanelGroup>
      {isTaskDetailsSlideoutOpen ? (
        <DetailsSlideout
          workflowId={workflowId}
          taskId={taskId}
          exampleSetId={exampleSetId}
          isOpen={isTaskDetailsSlideoutOpen}
          setIsOpen={setIsTaskDetailsSlideoutOpen}
        ></DetailsSlideout>
      ) : null}
    </Styled.Page>
  );
};
