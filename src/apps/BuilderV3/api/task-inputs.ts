import v3, { ApiError } from '@/services/v3';
import { queryOptions } from '@tanstack/react-query';
import { t } from '@/utils/i18n';
import { queryKeys } from './query-keys';
import type { QueryContext, TaskId, WorkflowId } from './types';

const getTaskInputs = async ({
  queryKey: [
    {
      filter: { workflowId },
    },
    {
      filter: { taskId },
    },
  ],
}: QueryContext<(typeof queryKeys.taskInputs)['byTask']>) => {
  const { data, errors } = await v3.taskInputs.getTaskInputs({
    workflowId,
    taskId,
  });
  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!data) {
    throw new Error(t('components.Builder.Errors.failedWorkflowIn'));
  }
  return data;
};

export const getTaskInputsQuery = ({ workflowId, taskId }: WorkflowId & TaskId) => {
  return queryOptions({
    queryKey: queryKeys.taskInputs.byTask({ workflowId, taskId }),
    queryFn: getTaskInputs,
  });
};
