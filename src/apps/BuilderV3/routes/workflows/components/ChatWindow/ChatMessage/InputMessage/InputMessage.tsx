import type * as types from '@floqastinc/transform-v3';
import { useMemo, useRef, useState } from 'react';
import { t } from '@/utils/i18n';
import { useQuery } from '@tanstack/react-query';
import {
  Button,
  CalendarSelectBox,
  DropdownButton,
  Flex,
  TextArea,
  Text,
  Avatar,
  // @ts-ignore
} from '@floqastinc/flow-ui_core';
// @ts-ignore
import DateRange from '@floqastinc/flow-ui_icons/material/DateRange';
// @ts-ignore
import Download from '@floqastinc/flow-ui_icons/material/Download';
import { useNavigate, useParams } from 'react-router-dom';
import * as Styled from '../styled';
import { NumericInput } from '@/components/BootlegInputs';
import { getExampleInputFileUriQuery } from '@BuilderV3/api/example-inputs';
import { downloadFileFromUri } from '@/utils/browser';

const UserAvatar = () => <Avatar size="sm" />;

const NullAvatar = () => <div style={{ width: '28px' }} />;

type NumberInputProps = {
  value: number;
  disabled?: boolean;
  onSave: (value: number) => void;
};
export const NumberInput: React.FC<NumberInputProps> = ({ value, disabled, onSave }) => {
  const { workflowId, taskId, exampleSetId } = useParams();
  const navigate = useNavigate();
  if (!workflowId || !taskId || !exampleSetId) {
    console.warn(t('components.Builder.Errors.noIDsprovided'));
    navigate('/builder');
    return null;
  }

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const n = parseFloat(newValue);
    if (!isNaN(n)) {
      onSave(n);
    }
  };

  // TODO: Implement this similarly to the TextInput with a confirmation
  //  to prevent complications (and confusion) with inline editing
  return (
    <NumericInput
      value={value}
      onChange={handleChange}
      onMouseDownCapture={(e) => e.stopPropagation()}
      aria-label={t('components.Builder.textInput')}
      placeholder={t('components.Builder.addInput')}
      disabled={disabled}
    />
  );
};

type TextInputProps = {
  value: string;
  disabled?: boolean;
  onSave: (value: string) => void;
};
export const TextInput: React.FC<TextInputProps> = ({ value, disabled, onSave }) => {
  const [isEditing, setIsEditing] = useState(false);

  const [draftMessage, setDraftMessage] = useState(value);

  return isEditing ? (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'end',
        gap: '8px',
      }}
    >
      <TextArea
        label={t('components.Builder.draftMessage')}
        type="text"
        value={draftMessage}
        onChange={setDraftMessage}
      />
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Button variant="outlined" style={{ color: '#6B7280' }} onClick={() => setIsEditing(false)}>
          {t('generics.cancel')}
        </Button>
        <Button
          onClick={() => {
            onSave(draftMessage);
            setIsEditing(false);
          }}
        >
          {t('generics.save')}
        </Button>
      </div>
    </div>
  ) : (
    <Styled.TextInput $disabled={disabled}>
      <p
        aria-label={t('components.Builder.textInput')}
        onClick={() => {
          if (!disabled) setIsEditing(true);
        }}
        onMouseDownCapture={(e) => e.stopPropagation()}
      >
        {value}
      </p>
    </Styled.TextInput>
  );
};

type DatetimeInputProps = {
  value: Date | string;
  disabled?: boolean;
  onChange: (date: Date) => void;
};
export const DatetimeInput: React.FC<DatetimeInputProps> = ({ value, disabled, onChange }) => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const today = new Date();

  const valueAsDateString = asDayString(new Date(value) ?? today);
  const [activePeriod, setActivePeriod] = useState({
    month: today.getMonth() + 1,
    year: today.getFullYear(),
  });

  const handleChange = async (val: string) => {
    if (!disabled) {
      setIsCalendarOpen(false);

      const date = new Date(val);
      onChange(date);
    }
  };

  return (
    <CalendarSelectBox
      value={valueAsDateString}
      activePeriod={activePeriod}
      onChange={handleChange}
      onMonthChange={setActivePeriod}
      onOpenChange={setIsCalendarOpen}
      open={isCalendarOpen}
      trigger={
        <DropdownButton disabled={disabled} open={isCalendarOpen} onClick={() => {}}>
          <Styled.DatetimeButton>
            <DateRange
              color="var(--flo-sem-color-icon-primary)"
              style={{ opacity: 0.8 }}
              size={20}
            />
            {valueAsDateString || t('components.Builder.selectDate')}
          </Styled.DatetimeButton>
        </DropdownButton>
      }
      styleOverrides={{
        content: {
          zIndex: '9999',
        },
      }}
    />
  );
};

function asDayString(date: Date) {
  return date.toISOString().split('T')[0];
}

function asDate(value: string | Date) {
  return typeof value === 'string' ? new Date(value) : value;
}

function useDebounce<T extends (...args: any[]) => void>(
  handler: T,
  timeout: number,
): (...args: Parameters<T>) => void {
  const timeoutRef = useRef<ReturnType<typeof setTimeout>>(null);

  const debouncedFunction = useMemo(() => {
    return (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        handler(...args);
      }, timeout);
    };
  }, [handler, timeout]);

  return debouncedFunction;
}

type FileInputProps = {
  input: types.ExampleInput & { value: types.FileArgument };
};
export const FileInput: React.FC<FileInputProps> = ({ input }) => {
  const exampleInputFileUri = useQuery({
    ...getExampleInputFileUriQuery({
      workflowId: input.workflowId,
      taskId: input.taskId,
      exampleInputId: input.id,
      exampleSetId: input.exampleSetId,
    }),
  });

  return (
    <Flex direction={'row'} align="center" gap={12}>
      <Text style={{ maxWidth: '175px' }} title="">
        {input.value.name}
      </Text>
      <Button
        onClick={() => downloadFileFromUri(exampleInputFileUri.data?.data?.url)}
        size="md"
        variant="ghost"
        color="dark"
        data-tracking-id="builder-message-input-file-download"
        padding={false}
      >
        <Download color="currentColor" />
      </Button>
    </Flex>
  );
};

export const InputMessage = ({
  includeAvatar,
  style,
  children,
}: {
  includeAvatar?: boolean;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}) => {
  return (
    <Styled.MessageContainer $chatRole="user">
      <Styled.Message style={{ ...style, alignItems: 'center' }}>
        {<NullAvatar />}
        {children}
        {includeAvatar ? <UserAvatar /> : <NullAvatar />}
      </Styled.Message>
    </Styled.MessageContainer>
  );
};
