/* eslint-disable i18next/no-literal-string */
/* eslint-disable no-console */
const { createProxyMiddleware } = require('http-proxy-middleware');

const useProxy = process.env.REACT_APP_USE_PROXY === 'true';
const useCloseProxy =
    useProxy && process.env.REACT_APP_USE_CLOSE_PROXY === 'true';
const useLambdaProxy =
    useProxy && process.env.REACT_APP_USE_LAMBDA_PROXY === 'true';
const lambdaExcludeList = useProxy
    ? process.env.REACT_APP_PROXY_EXCLUDE?.split(',') ?? []
    : [];
const env = useProxy ? process.env.REACT_APP_PROXY_ENV : null;
const localLambdaHostname = process.env.REACT_APP_LOCAL_LAMBDA_HOSTNAME;

const liljwtyGateHeader = process.env.X_LILJWTY_GATE;
if (!liljwtyGateHeader) {
    console.error('Error: Missing X_LILJWTY_GATE environment variable.');
    console.error(
        'Please set this environment variable using the value given here: https://floqast.atlassian.net/wiki/spaces/SEC/pages/2914517800/LilJWTy'
    );
    console.error(
        '-- run `echo "export X_LILJWTY_GATE=[...]" >> ~/.zshrc` in your terminal, then restart your terminal or run `source ~/.zshrc`'
    );
    process.exit();
}

console.log('Using Proxy:', useProxy);
console.log('Using Close Proxy:', useCloseProxy);
console.log('Using Lambda Proxy:', useLambdaProxy);
console.log('Excluding Lambdas:', lambdaExcludeList);

const useLocalLambda = (url) => {
    if (lambdaExcludeList?.length) {
        try {
            const pathArray = url.split('?')[0].split('/');

            const lambdaName = `${pathArray[2]}_${pathArray[3]}`;

            if (lambdaExcludeList.includes(lambdaName)) {
                console.log(
                    `Forwarding "${lambdaName}" request to local-lambda.`
                );

                return true;
            }
        } catch {
            console.log('Error parsing lambda name for proxy exclude.');
        }
    }

    return false;
};

module.exports = (app) => {
    if (useProxy) {
        if (useCloseProxy) {
            // Example: http://localhost:3001/api/users/me >> https://fq4.floqast.engineering/api/users/me
            app.use(
                '/api',
                createProxyMiddleware({
                    target: `https://${env}.floqast.engineering`,
                    changeOrigin: true,
                    logLevel: 'debug',
                    secure: false,
                    cookieDomainRewrite: { '*': '' },
                    onProxyReq: (proxyReq) => {
                        proxyReq.setHeader('x-liljwty-gate', liljwtyGateHeader);
                    },
                    onProxyReqWs: (proxyReq) => {
                        proxyReq.setHeader('x-liljwty-gate', liljwtyGateHeader);
                    },
                })
            );
        }

        if (useLambdaProxy) {
            // Example: http://localhost:3001/client/sox/control-templates >> https://sox.fq4.floqast.engineering/control-templates
            app.use(
                '/client',
                createProxyMiddleware({
                    // this gets overwritten in the router, but it's required
                    target: `https://system.env.floqast.engineering`,
                    changeOrigin: true,
                    pathRewrite: (path) => {
                        if (useLocalLambda(path)) {
                            return path;
                        }

                        const pathEndpoint = path.split('/').slice(3).join('/');

                        return `/${pathEndpoint}`;
                    },
                    logLevel: 'debug',
                    router: (req) => {
                        if (useLocalLambda(req.path)) {
                            return localLambdaHostname;
                        }

                        const system = req.path.split('/')[2];
                        const proxyUrl = `https://${system}.${env}.floqast.engineering`;

                        return proxyUrl;
                    },
                    onProxyReq: (proxyReq) => {
                        proxyReq.setHeader('x-liljwty-gate', liljwtyGateHeader);
                    },
                    onProxyReqWs: (proxyReq) => {
                        proxyReq.setHeader('x-liljwty-gate', liljwtyGateHeader);
                    },
                })
            );
        }
    }
};
