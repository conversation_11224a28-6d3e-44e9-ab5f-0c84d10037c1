// @ts-ignore
import { <PERSON>lex, <PERSON><PERSON>, Button } from '@floqastinc/flow-ui_core';
import { t } from '@/utils/i18n';
import { Overlay, ModalWrapper, ModalHeader, Form } from './styled';
import { useModal } from '@/components';
import { AGENT } from '@/constants';

export interface DeleteWorkflowRunModalProps {
  onSubmit: () => void;
}
export const DeleteWorkflowRunModal: React.FC<DeleteWorkflowRunModalProps> = ({ onSubmit }) => {
  const { closeModal } = useModal();

  const handleSubmit: React.FormEventHandler<HTMLFormElement> = (event) => {
    event.preventDefault();
    onSubmit();
    closeModal();
  };

  return (
    <Overlay onClick={closeModal}>
      <ModalWrapper onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          {t('generics.confirm')} {AGENT} {t('generics.runDeletion')}
        </ModalHeader>
        <Heading variant="body-base" weight="medium">
          <Flex style={{ marginTop: '1em' }} direction="column" gap="8">
            <p>
              {t('components.Builder.areYouSure')} {AGENT.toLocaleLowerCase()} run?
            </p>
            <p>
              {t('components.Builder.runRemoved1')} {AGENT.toLocaleLowerCase()}
              {t('components.Builder.allFilesUnchanged')}
            </p>
            <p>{t('components.Builder.cannotUndo')}</p>
          </Flex>
        </Heading>
        <Form onSubmit={handleSubmit}>
          <Button type="submit" color="danger">
            {t('generics.confirm')}
          </Button>
          <Button type="button" onClick={closeModal} color="dark" variant="outlined">
            {t('generics.cancel')}
          </Button>
        </Form>
      </ModalWrapper>
    </Overlay>
  );
};
