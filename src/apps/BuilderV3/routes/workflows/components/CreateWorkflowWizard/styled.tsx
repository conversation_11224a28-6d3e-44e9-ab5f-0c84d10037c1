import styled from 'styled-components';
import { LinkButton as FqLinkButton } from '@floqastinc/flow-ui_core';

export const WorkflowForm = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 12px;
  text-align: center;
  gap: 40px;
  width: 100%;
  height: min(50vh, 620px);
  margin-left: auto;
  margin-right: auto;
  padding-bottom: 72px;
  overflow-y: auto;
`;

export const WorkFlowFormInput = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 24px;
  width: 400px;
`;

export const UseCaseForm = styled.div`
  width: 100%;
  height: min(50vh, 620px);
`;

export const UseCaseFormContent = styled.div`
  display: flex;
  justify-content: center;
  gap: 40px;
  width: 100%;
  height: 380px;
`;

export const Left = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 40%;
  width: 40%;
  height: 100%;
`;

export const Right = styled.div`
  display: flex;
  gap: 16px;
  flex-direction: column;
  max-width: 60%;
  width: 100%;
  height: 100%;
`;

export const RadioButton = styled.button<{ active: boolean }>`
  position: relative;
  width: 100%;
  height: 36px;
  padding: 1rem;
  display: flex;
  align-items: center;
  text-align: left;
  border-radius: 6px;
  border: 1px solid
    ${(props) =>
      props.active ? 'var(--flo-base-color-core-600)' : 'var(--flo-base-color-neutral-300)'};
  background-color: white;
  cursor: pointer;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: ${(props) =>
      !props.active ? 'var(--flo-base-color-neutral-400)' : 'var(--flo-base-color-core-600)'};
  }
`;

export const RadioButtonWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

export const CheckContainer = styled.span`
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
`;

export const MarkdownWrapper = styled.div`
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 6px;
  padding: 16px 16px 8px 16px;
`;

// Augmenting the styles of the built-in flo-ui component
//  which doesn't include disabled styles
//  adb2bb corresponds to the semantic neutral color
export const LinkButton = styled(FqLinkButton)`
  &:disabled {
    cursor: not-allowed;
    color: #adb2bb;
    span {
      color: #adb2bb;
      background-image: none;
    }
  }
  &:hover {
    &:disabled {
      color: #adb2bb;
      span {
        color: #adb2bb;
        background-image: none;
      }
    }
  }
`;

export const AdvancedSetup = styled.div`
  display: flex;
  flex-direction: column;
  width: 400px;
  gap: 16px;
`;

export const ExperimentSettings = styled.div`
  display: flex;
  flex-direction: column;
  width: 400px;
  margin: 0 auto;
  gap: 16px;
`;

export const ExperimentGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
`;

export const ExperimentRow = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
`;

export const ExperimentLabel = styled.div`
  min-width: 140px; // Adjust based on longest label
  text-align: left;
  flex-shrink: 0;
`;

export const InputSelectionForm = styled.div`
  height: min(50vh, 620px);
`;

export const InitialInputCard = styled.button<{ active: boolean }>`
  width: 100%;
  padding: 1rem;
  text-align: left;
  display: flex;
  gap: 36px;
  flex-direction: column;
  border-radius: 6px;
  height: 100%;
  border: 2px solid
    ${(props) =>
      props.active ? 'var(--flo-base-color-core-600)' : 'var(--flo-base-color-neutral-300)'};
  background-color: white;
  cursor: pointer;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: ${(props) =>
      !props.active ? 'var(--flo-base-color-neutral-400)' : 'var(--flo-base-color-core-600)'};
  }
`;

export const InitialInputCheckmark = styled.div`
  height: 20px;
`;
