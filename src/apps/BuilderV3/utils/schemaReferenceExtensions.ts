import { EditorView } from 'codemirror';
import { createSchemaCompletion } from './autocomplete';
import { createSystemKey } from './conversionFunctions';
import { createSchemaHighlighter } from './highlighting';
import { createSchemaToolTip } from './tooltip';
import { FlolakeConnectionData } from '@/api/shared/types';
/**
 * Theme for schema highlighting and tooltips
 */
const schemaReferenceTheme = EditorView.baseTheme({
  '.cm-schema-reference': {
    backgroundColor: 'rgba(65, 105, 225, 0.2)',
    padding: '0 2px',
    color: '#4169e1',
    fontWeight: 'bold',
  },

  '.cm-tooltip-info': {
    fontFamily: 'monospace',
    backgroundColor: '#f8f9fa',
    color: '#212529',
    padding: '8px',
    fontSize: '12px',
    maxWidth: '300px',
  },

  '.cm-tooltip-warning': {
    fontFamily: 'monospace',
    backgroundColor: '#fff3cd',
    color: '#856404',
    border: '1px solid #ffeeba',
    padding: '8px',
    fontSize: '12px',
  },
});

/**
 * Creates CodeMirror extensions for schema reference highlighting, tooltips, and autocompletion
 */
export const createSchemaReferenceExtensions = (flolakeData: FlolakeConnectionData[]) => {
  return [
    schemaReferenceTheme,
    createSchemaHighlighter(),
    createSchemaCompletion(flolakeData, createSystemKey),
    createSchemaToolTip(flolakeData),
  ];
};
