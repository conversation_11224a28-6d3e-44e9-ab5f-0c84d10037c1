import styled from 'styled-components';
import { Button } from '@floqastinc/flow-ui_core';

export const Styled_ButtonRegular = styled(Button)`
  font-size: var(--flo-base-font-size-4);
  font-family: var(--flo-base-font-family-1);
  padding: 12px 16px;
`;

export const Styled_ButtonSmall = styled(Button)`
  font-size: var(--flo-base-font-size-2);
  line-height: var(--flo-base-line-height-1);
  font-family: var(--flo-base-font-family-1);
  border-radius: 6px;
  padding: 6px;
`;

export const Styled_CollapseButton = styled.button`
  display: flex;
  flex-direction: row;
  gap: 12px;
  color: var(--flo-base-color-black);
`;

export const Styled_BackButton = styled(Button)`
  gap: 4px;
  svg {
    width: 16px;
    height: 16px;
  }
  span {
    font-size: var(--flo-base-font-size-4);
    line-height: var(--flo-base-line-height-3);
    font-family: var(--flo-base-font-family-2);
    font-weight: var(--flo-base-font-weight-6);
    border-bottom: 1px solid var(--flo-base-color-neutral-400);
    color: var(--flo-base-color-black);
  }
`;
