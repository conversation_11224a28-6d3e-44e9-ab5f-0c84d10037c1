import { styled } from "styled-components";

export const InputContainer = styled.div`
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 8px;
  display: flex;
  height: 32px;
  align-items: center;
  padding: 3px 8px;
  gap: 8px;
  width: 100%;
  max-width: fit-content;
  transition: width 0.3s ease-in-out;

  @media (max-width: 1300px) {
    width: 100%;
  }
`;

export const IconContainer = styled.div`
  width: 20px;
  height: 20px;
  flex-shrink: 0;
`;
