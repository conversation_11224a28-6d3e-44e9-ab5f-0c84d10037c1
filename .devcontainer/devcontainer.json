{"postCreateCommand": "wget -O - https://raw.githubusercontent.com/FloQastInc/fq-dotfiles-base/refs/heads/main/scripts/postCreateCommand.sh --header=\"Authorization: Token ${GITHUB_TOKEN}\" | bash", "hostRequirements": {"cpus": 8}, "customizations": {"codespaces": {"repositories": {"floqastinc/fq-ui": {"permissions": "read-all"}, "floqastinc/fq-ui-events": {"permissions": "read-all"}, "floqastinc/eslint-config-close-base": {"permissions": "read-all"}, "floqastinc/eslint-config-platform-base": {"permissions": "read-all"}, "floqastinc/local-lambda-cli": {"permissions": "read-all"}, "floqastinc/fq-aws-cli": {"permissions": "read-all"}, "floqastinc/fq-dotfiles-base": {"permissions": "read-all"}, "floqastinc/client-hub": {"permissions": "read-all"}, "floqastinc/client-proxy": {"permissions": "read-all"}, "floqastinc/fq-intl-react": {"permissions": "read-all"}}}}}