import { styled } from 'styled-components';
import { BuilderSectionContent } from '../../index.styles';

export const GlobalInputsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--flo-sem-color-border);
`;

export const GlobalInputsSectionContent = styled(BuilderSectionContent)`
  padding: 12px;
`;

export const InputFormWrapper = styled.div`
  position: relative;
`;

export const InputFormOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--flo-sem-color-surface-neutral-base);
  opacity: 0.8;
  z-index: 1;
`;
