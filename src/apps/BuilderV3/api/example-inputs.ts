import v3, { ApiError } from '@/services/v3';
import { createQueryFunction } from '@/utils/query';
import {
  CreateExampleInputParams,
  DeleteExampleInputParams,
  GetExampleInputParams,
  GetExampleInputsParams,
  GetTaskInputExampleUriParams,
  SetExampleInputDatetimeParams,
  SetExampleInputFileValueParams,
  SetExampleInputNumberParams,
  SetExampleInputTextParams,
} from '@floqastinc/transform-v3';
import { queryOptions } from '@tanstack/react-query';
import { t } from '@/utils/i18n';
import { match, P } from 'ts-pattern';
import { queryKeys } from './query-keys';

const getExampleInputs = createQueryFunction(
  queryKeys.exampleInputs.getExampleInputs,
  async (params) => {
    const result = await v3.exampleInputs.getExampleInputs(params as GetExampleInputParams);
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
    if (!result.data) {
      throw new Error(t('components.Builder.Errors.failedExampleInputs'));
    }
    return result.data;
  },
);
export const getExampleInputsQuery = (params: GetExampleInputsParams) => {
  return queryOptions({
    queryKey: queryKeys.exampleInputs.getExampleInputs(params),
    queryFn: getExampleInputs,
  });
};

const getExampleInput = createQueryFunction(
  queryKeys.exampleInputs.getExampleInput,
  async (params) => {
    const result = await v3.exampleInputs.getExampleInput(params as GetExampleInputParams);
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
    if (!result.data) {
      throw new Error(t('components.Builder.Errors.failedExampleInputs'));
    }
    return result;
  },
);

export const getExampleInputQuery = (params: GetExampleInputParams) => {
  return queryOptions({
    queryKey: queryKeys.exampleInputs.getExampleInput(params),
    queryFn: getExampleInput,
  });
};

export const createExampleInput = async (params: CreateExampleInputParams) => {
  const result = await v3.exampleInputs.createExampleInput(params);
  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (!result.data) {
    throw new Error(t('components.Builder.Errors.failedCreateInputs'));
  }
  return result;
};

export const deleteExampleInput = async (params: DeleteExampleInputParams) => {
  const result = await v3.exampleInputs.deleteExampleInput(params);
  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  if (!result.data) {
    throw new Error(t('components.Builder.Errors.failedDeleteInputs'));
  }
  return result;
};

const getExampleInputFileUri = createQueryFunction(
  queryKeys.exampleInputs.getTaskInputExampleUri,
  async (params) => {
    const result = await v3.exampleInputs.getTaskInputExampleUri(
      params as GetTaskInputExampleUriParams,
    );
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
    if (!result.data) {
      throw new Error(t('components.Builder.Errors.failedInputFile'));
    }
    return result;
  },
);

export const getExampleInputFileUriQuery = (params: GetTaskInputExampleUriParams) => {
  return queryOptions({
    queryKey: queryKeys.exampleInputs.getTaskInputExampleUri(params),
    queryFn: getExampleInputFileUri,
  });
};

type SetExampleInputValueParams =
  | SetExampleInputFileValueParams
  | SetExampleInputTextParams
  | SetExampleInputNumberParams
  | SetExampleInputDatetimeParams;
export const setExampleInputValue = async (params: SetExampleInputValueParams) => {
  return match(params.value)
    .with({ kind: 'FILE' }, (_value) => null)
    .with({ workflowInputId: P.any }, (_value) => null)
    .with({ kind: 'TEXT' }, (value) => v3.exampleInputs.setExampleInputText({ ...params, value }))
    .with({ kind: 'NUMBER' }, (value) =>
      v3.exampleInputs.setExampleInputNumber({ ...params, value }),
    )
    .with({ kind: 'DATETIME' }, (value) =>
      v3.exampleInputs.setExampleInputDatetime({ ...params, value }),
    )
    .exhaustive();
};
