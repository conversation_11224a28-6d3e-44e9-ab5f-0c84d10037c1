import { t } from '@/utils/i18n';
import { useCreateOrUpdateExperimentAssignment } from '@BuilderV3/api/experiments';
import { getWorkflowQuery } from '@BuilderV3/api/workflows';
import { <PERSON><PERSON>, CloseButt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spinner, useToast } from '@floqastinc/flow-ui_core';
import { Workflow } from '@floqastinc/transform-v3';
import { useQuery } from '@tanstack/react-query';
import { useUpdateWorkflow } from '@v3/workflows';
import { useState } from 'react';
import { Experiments } from '../Experiments';
import * as Styled from './SettingsSideDrawer.styles';
import { Modes } from './ui/Modes';

interface DrawerContentProps {
  workflow: Workflow;
  onClose: (show: boolean) => void;
}
function DrawerContent({ workflow: oldWorkflow, onClose }: DrawerContentProps) {
  const { showToast, Toast } = useToast();
  const updateWorkflow = useUpdateWorkflow();
  const updateWorkflowExperiments = useCreateOrUpdateExperimentAssignment(oldWorkflow.id);

  const [workflow, setWorkflow] = useState<Workflow>(oldWorkflow);
  const [experimentChanges, setExperimentChanges] = useState<Record<string, string>>({});
  const setSettings = (name: keyof Workflow["settings"], value: boolean) => {
    setWorkflow({
      ...workflow,
      settings: {
        ...workflow.settings,
        [name]: value,
      },
    });
  };

  const handleUpdate = async () => {
    try {
      await Promise.all([
        updateWorkflow.mutateAsync({ workflow: workflow, workflowId: workflow.id }),
        updateWorkflowExperiments.mutateAsync(
          Object.entries(experimentChanges).map(([experimentName, variantId]) => ({
            experimentName,
            variantId,
          })),
        ),
      ]);
      showToast(
        <Toast type="success">
          <Toast.Title>{t("components.Builder.CreateAgent.settingsSaved")}</Toast.Title>
        </Toast>,
        { position: "bottom-right" },
      );
      onClose(false);
    } catch (error) {
      console.error(error);
      showToast(
        <Toast type="error">
          <Toast.Title>{t("components.Builder.CreateAgent.settingsError")}</Toast.Title>
        </Toast>,
        { position: "bottom-right" },
      );
    }
  };

  const handleExperimentUpdates = (experimentName: string, variantId: string) => {
    setExperimentChanges((prev) => ({
      ...prev,
      [experimentName]: variantId,
    }));
  };

  return (
    <>
      <SideDrawer.Header>
        <SideDrawer.Title>{t("generics.settings")}</SideDrawer.Title>
        <SideDrawer.TopRight>
          <CloseButton onClick={() => onClose(false)} aria-label={t("generics.close")} />
        </SideDrawer.TopRight>
      </SideDrawer.Header>
      <Styled.SideDrawerBody>
        <Modes settings={workflow.settings} setSettings={setSettings} />
        <Experiments onChange={handleExperimentUpdates} workflowId={workflow.id} />
      </Styled.SideDrawerBody>
      <Styled.SideDrawerFooter>
        <Button onClick={handleUpdate}>
          {updateWorkflow.isPending || updateWorkflowExperiments.isPending
            ? t("generics.saving")
            : t("generics.save")}
        </Button>
      </Styled.SideDrawerFooter>
    </>
  );
}

interface SettingsSideDrawerProps {
  workflowId: string;
  show: boolean;
  onClose: (show: boolean) => void;
}
export function SettingsSideDrawer({ workflowId, show, onClose }: SettingsSideDrawerProps) {
  const { data, isLoading } = useQuery(getWorkflowQuery(workflowId));

  return (
    <SideDrawer aria-label="" show={show} renderOverlay={show} onCancel={onClose}>
      <SideDrawer.Overlay>
        {isLoading && (
          <Styled.LoadingPage>
            <Spinner color="success" size={48} />
          </Styled.LoadingPage>
        )}
        {data && <DrawerContent workflow={data} onClose={onClose} />}
      </SideDrawer.Overlay>
    </SideDrawer>
  );
}
