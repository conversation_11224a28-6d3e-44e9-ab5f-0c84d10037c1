import styled from 'styled-components';
import { Button } from '@floqastinc/flow-ui_core';
import CheckCircle from '@floqastinc/flow-ui_icons/material/CheckCircle';
import Edit from '@floqastinc/flow-ui_icons/material/Edit';

export const Page = styled.div`
  display: grid;
  grid:
    'backbutton backbutton ' auto
    'header     buttonrow  ' auto
    'chat filepreview' 1fr
    'chat       filepreview'
    / minmax(0, 1fr) minmax(0, 1fr);
  height: 100%;
`;

export const BackButton = styled.div`
  display: grid;
  grid-area: backbutton;
  width: fit-content;
  gap: 8px;
  grid-auto-flow: column;
  padding-left: 24px;
  padding-top: 16px;
  margin-bottom: 8px;
`;
// DummyBackButtonIcon is used to maintain the same layout when a back button does not have an icon
export const DummyBackButtonIcon = styled.div`
  height: 16px;
`;

export const PageHeader = styled.div`
  grid-area: header;
  justify-self: start;
  align-self: center;
  padding-left: 24px;
  margin-bottom: 16px;
`;

export const ButtonRow = styled.div`
  display: flex;
  grid-area: buttonrow;
  flex-direction: row;
  justify-self: end;
  align-self: center;
  align-items: flex-end;
  margin-right: 24px;
  margin-bottom: 24px;
  gap: 8px;
`;

export const ExpandableArea = styled.div`
  display: grid;
  grid-area: expandable;
`;

type TaskWindowProps = {
  $numRows: number;
};
// position: relative for allowing progress loader overlay as absolute
export const TaskWindow = styled.div<TaskWindowProps>`
  position: relative;
  display: grid;
  grid-area: chat;
  grid-template-columns: 1fr;
  grid-template-rows: ${(props) => `repeat(${props.$numRows}, 40px)`};
  align-items: start;
  gap: 12px;
  padding: 24px;
  border-top: solid var(--flo-base-color-neutral-300);
`;

export const TaskWindowHeader = styled.div`
  font-size: 18px;
  font-weight: 400;
`;

export const RunMessages = styled.div`
  display: grid;
  gap: 12px;
  overflow-y: scroll;
`;

export const FilePreview = styled.div`
  position: relative;
  height: 100%;
  grid-area: filepreview;
  border-top: solid var(--flo-base-color-neutral-300);
  overflow-y: scroll;
`;

export const RightPanelControls = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
  padding-top: 24px;
  padding-left: 24px;
  padding-bottom: 16px;
  padding-right: 10%;
`;

export const RightPanelButtonContainer = styled.div`
  max-width: 400px;
  flex-basis: 50%;
  flex-grow: 1;
`;

// Height:
// - 3px - border
// - 102px - filepreview controls
export const FilePreviewContainer = styled.div`
  position: relative;
  z-index: 0;
  height: calc(100% - 102px - 3px);
`;

export const ChatWrapper = styled.div`
  grid-area: chat;
  height: 100%;
`;

// TODO: Remove after runner page is updated for the SideDrawer refactor
export const SideDrawerBody = styled.div`
  display: grid;
  grid-template-areas: 'steps options';
  grid-auto-flow: column;
  grid-template-rows: 1fr 1fr;
`;

export const StepListItems = styled.div`
  display: grid;
  grid-area: steps;
  width: fit-content;
  gap: 8px;
`;

export const Step = styled.div`
  display: grid;
  grid-auto-flow: column;
  align-items: center;
  justify-content: start;
  gap: 16px;
`;

export const StepInfo = styled.div`
  display: flex;
  justify-content: space-between;
  flex-direction: column;
`;

export const StepOptions = styled.div`
  grid-area: options;
  display: grid;
  justify-self: end;
  gap: 8px;
`;

export const Circle = styled(Button)`
  z-index: 1;
  &:hover {
    background-color: var(--flo-sem-color-white);
  }
  padding: 0px;
  background-color: var(--flo-sem-color-white);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16px;
  width: 16px;
  box-sizing: border-box;

  border-radius: 50%;
  border: 2px solid;
  cursor: pointer;

  ${({ $status, $isActive, $isStepEnabled }) => {
    const hoverGreenColor = 'var(--flo-sem-color-primary-hover)';
    const greenColor = 'var(--flo-sem-color-primary-default)';
    let newCss = ``;

    if ($status === 'completed') {
      newCss += `
                    background-color: ${greenColor};
                    border-color: ${greenColor};

                    &:hover {
                        background-color: ${hoverGreenColor};
                        border-color: ${hoverGreenColor};
                    }
                `;
    } else {
      newCss += `
                    border-color: var(--flo-sem-color-border);

                    &:hover {
                        border-color: #D2D6DB; 
                    }
                `;
    }

    if ($status === 'error') {
      newCss += `
                    border-color: var(--flo-sem-color-danger);

                    &:hover {
                        border-color: var(--flo-sem-color-danger-hover);
                    }
                `;
    }
    if ($isActive) {
      newCss += `
                    box-shadow: 0px 0px 0px 2.66667px rgba(var(--flo-base-color-core-600-rgb), 0.2);
                `;
    }
    if ($isActive && $status === 'incomplete') {
      newCss += `border-color: ${greenColor};

                    &:hover {
                        border-color: ${hoverGreenColor};
                    }
                `;
    }
    if (!$isStepEnabled) {
      newCss += `
                    pointer-events: none;
                `;
    }

    return newCss;
  }}
`;

export const Centered = styled.div`
  display: grid;
  place-items: center;
  width: 100%;
  height: 100%;
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(240, 240, 240, 0.5);
`;

export const InputHelperText = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

export const UploadCheck = styled(CheckCircle)`
  fill: var(--flo-sem-color-primary-default);
  color: var(--flo-sem-color-primary-default);
`;

export const Well = styled.div`
  display: grid;
  gap: 16px;
  padding: 24px;
  border: 1px solid var(--flo-base-color-neutral-200);
  border-radius: 8px;
  box-shadow: inset 0 1px 1px 0 rgba(0, 0, 0, 0.1);
  background-color: rgba(210, 71, 71, 0.1);
`;

export const EditIcon = styled(Edit)`
  cursor: pointer;
  marginleft: 3px;
`;

export const LoadingCentered = styled.div`
  display: grid;
  place-items: center;
  width: 100%;
  height: 100%;
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(240, 240, 240, 0.5);
`;
export const Text = styled.p`
  margin-top: 10px;
  font-size: 16px;
  color: #333;
`;
