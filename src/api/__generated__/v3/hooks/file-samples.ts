/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type { CreateFileSampleParams, EmptyResponseData, Error } from "@floqastinc/transform-v3";
import { useMutation, type UseMutationOptions, useQueryClient } from "@tanstack/react-query";
import { useFileSampleService } from "./context";
import { assert, guard, type QueryError } from "./runtime";

/**
 * Create an input file sample
 */
export function useCreateFileSample(
  options?: Omit<
    UseMutationOptions<EmptyResponseData, QueryError<Error[]>, CreateFileSampleParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const fileSampleService = useFileSampleService();
  return useMutation({
    mutationFn: async (params: CreateFileSampleParams) => {
      const res = await guard(fileSampleService.createFileSample(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({
        queryKey: [
          `/workflows/${params.workflowId}/tasks/${params.taskId}/examples/${params.exampleSetId}/inputs/${params.exampleInputId}/value/file/sample`,
        ],
      });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}
