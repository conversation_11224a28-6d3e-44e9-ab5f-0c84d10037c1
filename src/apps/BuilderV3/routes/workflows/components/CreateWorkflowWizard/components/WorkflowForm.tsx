import {
  Accordion,
  DropdownButton,
  DropdownPanel,
  Heading,
  Input,
  Text,
  TextArea,
} from '@floqastinc/flow-ui_core';
import { Entity, Experiment, Workflow } from '@floqastinc/transform-v3';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { match } from 'ts-pattern';
import * as Styled from '../styled';
import { t } from '@/utils/i18n';
import { v3, ApiError } from '@/services/v3';
import { useFeatureFlags } from '@/components/FeatureFlag';
import { Modes } from '@Transform/components/SettingsSideDrawer/ui/Modes';

type WorkflowFormProps = {
  workflow: Pick<Workflow, 'name' | 'description' | 'entityId' | 'settings'>;
  setWorkflow: (workflow: Pick<Workflow, 'name' | 'description' | 'entityId' | 'settings'>) => void;
  experimentAssignments: Record<string, string>;
  setExperimentAssignments: React.Dispatch<React.SetStateAction<Record<string, string>>>;
};

export const WorkflowForm = ({
  workflow,
  setWorkflow,
  experimentAssignments,
  setExperimentAssignments,
}: WorkflowFormProps) => {
  const [workflowNameError, setWorkflowNameError] = useState('');
  const [selectedEntity, setSelectedEntity] = useState<string | null>(null);
  const [entityDropdownIsOpen, setEntityDropdownIsOpen] = useState(false);
  const [selectedExperimentVariants] = useState<Record<string, string>>({}); // didn't need the setter
  const [openExperimentDropdown, setOpenExperimentDropdown] = useState<string | null>(null);

  // Feature flags
  const { getFlag } = useFeatureFlags();
  const transformExperimentsEnabled = getFlag('enable-experiments', false);

  const { data: experimentOptions = [] } = useQuery<Experiment[], Error>({
    queryKey: ['experiments', 'list'],
    queryFn: async () => {
      const response = await v3.experiments.getExperiments();
      if (response.errors.length) {
        throw new ApiError(response.errors);
      }
      return response.data;
    },
    retry: false,
    enabled: transformExperimentsEnabled, // only fetch if experiments flag is enabled
  });
  const activeExperiments = experimentOptions.filter((experiment) => experiment.active);
  const inactiveExperiments = experimentOptions.filter((experiment) => !experiment.active);

  const {
    isLoading,
    isError,
    error,
    data: entityOptions = [],
  } = useQuery<Entity[], Error>({
    queryKey: ['entities', 'list'],
    queryFn: async () => {
      const response = await v3.entities.getEntities();
      if (response.errors.length) {
        throw new ApiError(response.errors);
      }
      return response.data;
    },
    retry: false,
  });

  const handleEntityChange = (value: string) => {
    setSelectedEntity(value);
    setWorkflow({ ...workflow, entityId: value });
    setEntityDropdownIsOpen(false);
  };

  const handleExperimentVariantDropdownOpenChange = (experimentLabel: string, open: boolean) => {
    setOpenExperimentDropdown(open ? experimentLabel : null);
  };

  const handleExperimentChange = (
    experimentName: string,
    variantLabel: string,
    variants: Experiment['variants'],
  ) => {
    const selectedVariant = variants.find((v) => v.label === variantLabel);
    if (selectedVariant) {
      setExperimentAssignments((prev) => ({
        ...prev,
        [experimentName]: selectedVariant.id,
      }));
      setOpenExperimentDropdown(null);
    }
  };

  const handleSettings = (name: keyof Workflow['settings'], value: boolean) => {
    setWorkflow({
      ...workflow,
      settings: {
        ...workflow.settings,
        [name]: value,
      },
    });
  };

  return (
    <Styled.WorkflowForm>
      <Heading variant="h5">{t('components.Builder.nameAgent')}</Heading>
      <Styled.WorkFlowFormInput>
        <Input
          label={t('components.Builder.agentName')}
          isRequired
          data-testid="agent-name-input"
          onChange={(name: string) => {
            if (name) setWorkflowNameError('');
            setWorkflow({ ...workflow, name, entityId: selectedEntity ?? '' });
          }}
          placeholder={t('components.Builder.agentName')}
          value={workflow.name}
          onBlur={() => {
            if (!workflow.name) {
              setWorkflowNameError(t('components.Builder.Errors.agentNameRequired'));
            }
          }}
          isInvalid={workflowNameError}
        />
        <DropdownPanel
          onChange={handleEntityChange}
          onOpenChange={setEntityDropdownIsOpen}
          isOpen={entityDropdownIsOpen}
          selectedValue={selectedEntity}
          disableClear={true}
          data-testid="entity-dropdown-panel"
        >
          <DropdownPanel.Trigger>
            <DropdownButton
              isRequired
              label={t('components.Builder.entity')}
              variant="default"
              open={entityDropdownIsOpen}
              data-testid="entity-dropdown-button"
            >
              {match({ isLoading, isError, data: entityOptions })
                .with({ isLoading: true }, () => t('components.Builder.loading'))
                .with({ data: [] }, () => t('components.Builder.noDataAvailable'))
                .with({ isError: true }, () =>
                  t('components.Builder.Errors.errorFetchingEntities', { error: error?.message }),
                )
                .otherwise(() =>
                  selectedEntity
                    ? entityOptions.find((option) => option.id === selectedEntity)?.name
                    : t('components.Builder.selectEntity'),
                )}
            </DropdownButton>
          </DropdownPanel.Trigger>

          <DropdownPanel.Content
            portal={false}
            style={{ maxHeight: '300px', overflowY: 'auto', textAlign: 'left' }}
            data-testid="entity-dropdown-content"
          >
            {match({ isLoading, isError, data: entityOptions })
              .with({ isLoading: true }, () => (
                <DropdownPanel.Option value="loading" disabled data-testid="entity-loading-option">
                  {t('components.Builder.loading')}
                </DropdownPanel.Option>
              ))
              .with({ isError: true }, () => (
                <DropdownPanel.Option value="error" disabled data-testid="entity-error-option">
                  {t('components.Builder.Errors.errorFetchingEntities')} {error?.message}
                </DropdownPanel.Option>
              ))
              .otherwise(() =>
                entityOptions.length > 0 ? (
                  entityOptions.map((entity) => (
                    <DropdownPanel.Option
                      key={entity.id}
                      value={entity.id}
                      data-testid={`entity-option-${entity.id}`}
                    >
                      {entity.name}
                    </DropdownPanel.Option>
                  ))
                ) : (
                  <DropdownPanel.Option
                    value="no-data"
                    disabled
                    data-testid="entity-no-data-option"
                  >
                    {t('components.Builder.noEntityAvailable')}
                  </DropdownPanel.Option>
                ),
              )}
          </DropdownPanel.Content>
        </DropdownPanel>
        <TextArea
          styleOverrides={{
            root: {
              width: '100%',
            },
            textarea: {
              width: '100%',
            },
          }}
          data-testid="workflow-description-textarea"
          isLabelVisible
          label={t('components.Builder.optionalDescription')}
          onChange={(description: string) => {
            setWorkflow({ ...workflow, description });
          }}
          placeholder={t('generics.description')}
          value={workflow.description}
        />
      </Styled.WorkFlowFormInput>
      <Styled.AdvancedSetup>
        <Accordion collapsible>
          <Accordion.Item value="advancedSetup">
            <Accordion.Trigger>
              {t('components.Builder.CreateAgent.advancedSetup')}
            </Accordion.Trigger>
            <Accordion.Content>
              <Modes settings={workflow.settings} setSettings={handleSettings} />
            </Accordion.Content>
          </Accordion.Item>
        </Accordion>
      </Styled.AdvancedSetup>

      {transformExperimentsEnabled && (
        <>
          <Heading variant="h5">{t('generics.experiments')}</Heading>
          <Styled.ExperimentSettings>
            <Styled.ExperimentGroup>
              <Heading variant="h6">{t('generics.active')}</Heading>
              {activeExperiments.map((experiment) => {
                const selectedVariantId = experimentAssignments[experiment.experimentName];
                const selectedVariantLabel =
                  experiment.variants.find((v) => v.id === selectedVariantId)?.label ??
                  t('components.Builder.selectVariant');

                return (
                  <Styled.ExperimentRow key={experiment.label}>
                    <Styled.ExperimentLabel>
                      <Text>{experiment.label}</Text>
                    </Styled.ExperimentLabel>
                    <DropdownPanel
                      selectedValue={
                        selectedExperimentVariants[experiment.label] ?? experiment.variants[0].label
                      }
                      onChange={(value: string) =>
                        handleExperimentChange(
                          experiment.experimentName,
                          value,
                          experiment.variants,
                        )
                      }
                      onOpenChange={(open: boolean) =>
                        handleExperimentVariantDropdownOpenChange(experiment.label, open)
                      }
                      isOpen={openExperimentDropdown === experiment.label}
                      disableClear={true}
                      data-testid={`experiment-dropdown-panel`}
                    >
                      <DropdownPanel.Trigger>
                        <DropdownButton
                          variant="default"
                          open={openExperimentDropdown === experiment.label}
                          style={{ minWidth: '180px' }}
                        >
                          {selectedVariantLabel}
                        </DropdownButton>
                      </DropdownPanel.Trigger>

                      <DropdownPanel.Content
                        portal={false}
                        style={{
                          maxHeight: '300px',
                          minWidth: '180px',
                          overflowY: 'auto',
                          textAlign: 'left',
                          zIndex: 1,
                        }}
                      >
                        {experiment.variants.map((variant) => (
                          <DropdownPanel.Option key={variant.id} value={variant.label}>
                            {variant.label}
                          </DropdownPanel.Option>
                        ))}
                      </DropdownPanel.Content>
                    </DropdownPanel>
                  </Styled.ExperimentRow>
                );
              })}
            </Styled.ExperimentGroup>

            {inactiveExperiments.length > 0 && (
              <Styled.ExperimentGroup>
                <Heading variant="h6">{t('components.Builder.inactive')}</Heading>
                {inactiveExperiments.map((experiment) => {
                  const selectedVariantId = experimentAssignments[experiment.experimentName];
                  const selectedVariantLabel =
                    experiment.variants.find((v) => v.id === selectedVariantId)?.label ??
                    t('components.Builder.selectVariant');

                  return (
                    <Styled.ExperimentRow key={experiment.label}>
                      <Styled.ExperimentLabel>
                        <Text>{experiment.label}</Text>
                      </Styled.ExperimentLabel>
                      <DropdownPanel
                        selectedValue={
                          selectedExperimentVariants[experiment.label] ??
                          experiment.variants[0].label
                        }
                        onChange={(value: string) =>
                          handleExperimentChange(
                            experiment.experimentName,
                            value,
                            experiment.variants,
                          )
                        }
                        isOpen={openExperimentDropdown === experiment.label}
                        onOpenChange={(open: boolean) => {
                          setOpenExperimentDropdown(open ? experiment.label : null);
                        }}
                        disableClear={true}
                        data-testid="experiment-variant-dropdown-panel"
                      >
                        <DropdownPanel.Trigger>
                          <DropdownButton
                            variant="default"
                            open={openExperimentDropdown === experiment.label}
                            style={{ minWidth: '180px' }}
                          >
                            {selectedVariantLabel}
                          </DropdownButton>
                        </DropdownPanel.Trigger>
                        <DropdownPanel.Content
                          portal={false}
                          style={{
                            maxHeight: '300px',
                            minWidth: '180px',
                            overflowY: 'auto',
                            textAlign: 'left',
                            zIndex: 1,
                          }}
                          data-testid="experiment-variant-dropdown-content"
                        >
                          {experiment.variants.map((variant) => (
                            <DropdownPanel.Option key={variant.id} value={variant.label}>
                              {variant.label}
                            </DropdownPanel.Option>
                          ))}
                        </DropdownPanel.Content>
                      </DropdownPanel>
                    </Styled.ExperimentRow>
                  );
                })}
              </Styled.ExperimentGroup>
            )}
          </Styled.ExperimentSettings>
        </>
      )}
    </Styled.WorkflowForm>
  );
};
