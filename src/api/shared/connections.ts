import { lambdaGet } from './connections-request';
import { ActiveConnection, FlolakeConnectionData, ProvidersData } from './types';

const LAMBDA_ENDPOINTS = {
  V1_CONNECTIONS: {
    system: 'connections',
    path: 'api/v1/connections',
  },
  V2_PROVIDERS: {
    system: 'connections',
    path: 'api/v2/categories/providers',
  },
  V2_FLOLAKE: {
    system: 'connections',
    path: 'api/v2/connections/flolake',
  },
};

interface AvailableConnectionsResponse {
  data: ProvidersData[];
}

interface ActiveConnectionsResponse {
  data: ActiveConnection[];
}

interface FlolakeResponse {
  data: {
    totalConnections: number;
    connections: FlolakeConnectionData[];
  };
}

interface FlolakeResponse {
  data: {
    totalConnections: number;
    connections: FlolakeConnectionData[];
  };
}

const ConnectionsAPI = {
  async getAvailableConnections(): Promise<ProvidersData[]> {
    try {
      const response = await lambdaGet<AvailableConnectionsResponse>(
        LAMBDA_ENDPOINTS.V2_PROVIDERS,
        {
          'filter[onlyTransform]': true,
        },
      );

      return response.data;
    } catch (error) {
      console.error('Available Connections Error:', error);
      throw error;
    }
  },

  async getActiveConnections(): Promise<ActiveConnection[]> {
    try {
      const response = await lambdaGet<ActiveConnectionsResponse>(LAMBDA_ENDPOINTS.V1_CONNECTIONS, {
        includeFivetranStatus: false,
      });

      return response.data;
    } catch (error) {
      console.error('Active Connections Error:', error);
      throw error;
    }
  },

  async getFlolakeData(): Promise<FlolakeResponse> {
    try {
      const response = await lambdaGet<FlolakeResponse>(LAMBDA_ENDPOINTS.V2_FLOLAKE);

      return response;
    } catch (error) {
      console.error('Flolake Data Error:', error);
      throw error;
    }
  },
};

export default ConnectionsAPI;
