import React, { useState } from 'react';
import { t } from '@/utils/i18n';
import { TextArea } from '@floqastinc/flow-ui_core';
import { Overlay, ModalWrapper, ModalHeader, Form, Button } from './styled';
import { useModal } from '@/components';
import { AGENT } from '@/constants';

export interface WorkflowRunNotesModalProps {
  name: string;
  notes: string | undefined;
  onSubmit: (editedNotes: string) => void;
}

export const WorkflowRunNotesModal: React.FC<WorkflowRunNotesModalProps> = ({
  onSubmit,
  name,
  notes,
}) => {
  const [editedNotes, setEditedNotes] = useState(notes ?? '');
  const { closeModal } = useModal();

  const handleSubmit: React.FormEventHandler<HTMLFormElement> = (event) => {
    event.preventDefault();
    onSubmit(editedNotes);
    closeModal();
  };

  return (
    <Overlay onClick={closeModal}>
      <ModalWrapper onClick={(e) => e.stopPropagation()}>
        <ModalHeader>{name}</ModalHeader>
        <Form onSubmit={handleSubmit}>
          <TextArea
            type="text"
            value={editedNotes}
            onChange={setEditedNotes}
            placeholder={t('components.Builder.agentDescriptionOptional')}
            aria-label={t('components.Builder.agentDescriptionOptional')}
            styleOverrides={{ textarea: { width: '100%' } }}
          />
          <Button type="submit" $primary>
            {t('generics.save')}
          </Button>
          <Button type="button" onClick={closeModal}>
            {t('generics.cancel')}
          </Button>
        </Form>
      </ModalWrapper>
    </Overlay>
  );
};
