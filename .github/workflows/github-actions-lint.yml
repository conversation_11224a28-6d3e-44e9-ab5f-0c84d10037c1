name: Run Linting on PRs
run-name: Lint branch ${{ github.ref_name }}

# **What it does**: Runs `npm run lint`.
# **Why we have it**: We want linting to pass before merging code.
# **Who does it impact**: Engineers who contribute to the repo.

on:
  workflow_dispatch:
  pull_request:

concurrency:
  # Cancel when a new commit is pushed to the same PR
  group: lint-${{ github.head_ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  unit-testing:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: webfactory/ssh-agent@dc588b651fe13675774614f8e6a936a468676387
        with:
          ssh-private-key: ${{ secrets.actions_ssh_pem_emu }}
  
      - uses: actions/checkout@v4

      - uses: volta-cli/action@2d68418f32546fd191eb666e232b321d5726484d

      - name: set-package-manager
        id: set-package-manager
        uses: FloQastInc/.github/.github/actions/set-package-manager@main
        with:
          token: ${{ secrets.GH_REGISTRY_PACKAGES }}

      - name: Install dependencies
        run: npm ci --production=false

      - name: Run lint
        run: npm run lint
