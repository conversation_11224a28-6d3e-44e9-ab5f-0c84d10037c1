// @ts-ignore
import { Heading } from '@floqastinc/flow-ui_core';
import PropTypes from 'prop-types';

export const WrappedHeading = ({ children, ...props }) => {
  return (
    <Heading
      style={{
        width: '100%',
        wordWrap: 'break-word',
        textAlign: 'center',
      }}
      {...props}
    >
      {children}
    </Heading>
  );
};

WrappedHeading.propTypes = {
  children: PropTypes.node,
};
