import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Strategy } from '@floqastinc/transform-v3';
import { t } from '@/utils/i18n';
import v3, { ApiError } from '@/services/v3';
import { tempAssistantId } from '@/utils/assistant';

type StrategyKind = Strategy['kind'];
// TODO: Need to figure out how to make this more generic
export type CreateableStrategyKind = Extract<
  StrategyKind,
  'JEM_EXPORT' | 'LLM_THREAD' | 'FLOLAKE' | 'JEM_TEMPLATE_FETCH' | 'REVIEW'
>;

export type CreateableStrategy = Extract<Strategy, { kind: CreateableStrategyKind }>;

export type StrategyDefinition = {
  kind: CreateableStrategyKind;
  label: string;
  defaultStrategy: CreateableStrategy;
};

export function useStrategies(): StrategyDefinition[] {
  const { data: taskStrategies = [] } = useQuery({
    queryKey: ['strategies', 'list'],
    queryFn: async () => {
      const response = await v3.strategies.getWorkflowTaskStrategies({});
      if (response.errors.length) {
        throw new ApiError(response.errors);
      }
      if (!response.data) {
        throw new Error(t('components.Builder.Errors.noData'));
      }
      return response.data;
    },
  });

  return useMemo(() => {
    const strategies: StrategyDefinition[] = [];

    for (const taskStrategy of taskStrategies) {
      const kind = taskStrategy.kind as CreateableStrategyKind;
      let defaultStrategy: CreateableStrategy;

      // TODO: Need to figure out how to handle the default strategy for each kind
      switch (kind) {
        case 'LLM_THREAD':
          defaultStrategy = {
            kind,
            messages: [],
            status: 'READY',
            assistantId: tempAssistantId(),
          };
          break;
        case 'FLOLAKE':
          defaultStrategy = { kind, statement: '', sources: [] };
          break;
        default:
          defaultStrategy = { kind };
      }

      strategies.push({
        kind,
        label: taskStrategy.name,
        defaultStrategy,
      });
    }

    return strategies;
  }, [taskStrategies]);
}
