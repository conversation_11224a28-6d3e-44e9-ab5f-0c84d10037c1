import { RunStatus } from '@floqastinc/transform-v3';
import { describe, expect, test } from 'vitest';
import { customRender } from '../../../../../../../utils/testing';
import { StepResult } from './StepResults';

describe('StepResult', () => {
  const mockFile = null;
  const mockDate = new Date('2024-03-20T12:00:00Z');
  const mockUser = '<EMAIL>';

  test('renders JEM export links correctly', async () => {
    const outputValues = [
      {
        value: 'https://example.com/jem1',
        name: 'JEM Export 1',
        stepNumber: 1,
        kind: 'JEM_EXPORT' as const,
      },
      {
        value: 'https://example.com/jem2',
        name: 'JEM Export 2',
        stepNumber: 2,
        kind: 'JEM_EXPORT' as const,
      },
    ];

    const screen = customRender(
      <StepResult
        file={mockFile}
        workflowstatus="COMPLETED"
        runBy={mockUser}
        createdAt={mockDate}
        outputValues={outputValues}
      />,
    );

    // Check if JEM links are rendered
    await expect
      .element(screen.getByTestId('runner-stepList-result-jemLink-https://example.com/jem1'))
      .toBeInTheDocument();
    await expect
      .element(screen.getByTestId('runner-stepList-result-jemLink-https://example.com/jem2'))
      .toBeInTheDocument();

    // Check if step numbers and names are displayed
    await expect.element(screen.getByText('Step 1: JEM Export 1')).toBeInTheDocument();
    await expect.element(screen.getByText('Step 2: JEM Export 2')).toBeInTheDocument();
  });
  // TODO: Fix this test (Providers are throwing errors (useContext) when we try to make a query to fetch URI)
  // test('renders file download when present', async () => {
  //   const outputValues = [
  //     {
  //       value: 'https://example.com/jem1',
  //       name: 'JEM Export 1',
  //       stepNumber: 1,
  //       kind: 'JEM_EXPORT' as const,
  //     },
  //   ];

  //   const screen = customRender(
  //     <StepResult
  //       file={mockFile}
  //       workflowstatus="COMPLETED"
  //       runBy={mockUser}
  //       createdAt={mockDate}
  //       outputValues={outputValues}
  //     />,
  //   );

  //   await expect.element(screen.getByTestId('runner-stepList-result-file')).toBeInTheDocument();
  // });

  test('displays correct run date and time', async () => {
    const outputValues = [
      {
        value: 'https://example.com/jem1',
        name: 'JEM Export 1',
        stepNumber: 1,
        kind: 'JEM_EXPORT' as const,
      },
    ];

    const screen = customRender(
      <StepResult
        file={mockFile}
        workflowstatus="COMPLETED"
        runBy={mockUser}
        createdAt={mockDate}
        outputValues={outputValues}
      />,
    );

    // Check date format (Mar 20, 2024)
    await expect.element(screen.getByText('Mar 20, 2024')).toBeInTheDocument();

    await expect
      .element(
        screen.getByText(
          mockDate.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
          }),
        ),
      )
      .toBeInTheDocument();
  });

  test('displays correct user who ran the agent', async () => {
    const outputValues = [
      {
        value: 'https://example.com/jem1',
        name: 'JEM Export 1',
        stepNumber: 1,
        kind: 'JEM_EXPORT' as const,
      },
    ];

    const screen = customRender(
      <StepResult
        file={mockFile}
        workflowstatus="COMPLETED"
        runBy={mockUser}
        createdAt={mockDate}
        outputValues={outputValues}
      />,
    );

    await expect.element(screen.getByTestId('runBy')).toBeInTheDocument();
    await expect.element(screen.getByText(mockUser)).toBeInTheDocument();
  });

  test('displays correct status badge', async () => {
    const outputValues = [
      {
        value: 'https://example.com/jem1',
        name: 'JEM Export 1',
        stepNumber: 1,
        kind: 'JEM_EXPORT' as const,
      },
    ];

    const statuses: RunStatus[] = ['COMPLETED', 'FAILED', 'REJECTED', 'REVIEW'];

    statuses.forEach(async (status) => {
      const screen = customRender(
        <StepResult
          file={mockFile}
          workflowstatus={status}
          runBy={mockUser}
          createdAt={mockDate}
          outputValues={outputValues}
        />,
      );

      await expect.element(screen.getByTestId('status-badge')).toBeInTheDocument();
    });
  });

  test('handles missing data gracefully', async () => {
    const screen = customRender(
      <StepResult
        file={null}
        workflowstatus="COMPLETED"
        runBy={undefined}
        createdAt={undefined}
        outputValues={[]}
      />,
    );

    // Should still render the basic structure
    await expect.element(screen.getByTestId('results')).toBeInTheDocument();
  });
});
