import { styled } from 'styled-components';
import { <PERSON><PERSON>, TextA<PERSON>, Tooltip } from '@floqastinc/flow-ui_core';

interface UserChatProps {
  isEditing?: boolean;
}

export const ChatContent = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  align-items: stretch;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  background-color: #fafbf9;
  scrollbar-width: thin;
`;

// TODO: Check if this background-color exists: looks like the weakest
//  is #f8f which is a bit darker
export const ChatLog = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding: 8px;
  justify-content: space-between;
  flex: 1;
`;

export const ChatLogMessages = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 8px;
  background-color: #fafbf9;
  gap: 24px;
  flex: 1;
`;

export const ChatMessage = styled.div`
  font-family: var(--flo-base-font-family-2);
  font-size: var(--flo-base-font-size-4);
  font-weight: var(--flo-base-font-weight-4);
  display: flex;
  flex-direction: column;
  max-width: 300px;
  border-radius: 6px;
  padding: 12px;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: bold;
    font-size: revert;
  }

  & ul,
  & ol {
    list-style: disc inside;
  }

  & p {
    margin: 8px 0;
  }
  *:first-child {
    margin-top: 0;
  }
  *:last-child {
    margin-bottom: 0;
  }
`;

export const UserChatContainer = styled.div`
  display: flex
  flex-direction: column;
  gap: 6px;
  align-self: flex-end;
  width: 100%;
`;

// TODO: find where this background-color
//  is defined in flow-ui (or if it is)

export const UserChat = styled(ChatMessage)<UserChatProps>`
  display: flex;
  background-color: #efedea;
  justify-self: end;
  width: 100%;
  max-width: ${({ isEditing }) => (isEditing ? '100%' : '80%')};
`;

export const UserChatActions = styled.div`
  display: flex;
  flex-direction: row;
  justify-self: end;
`;

export const AssistantChat = styled(ChatMessage)`
  display: flex;
  align-self: flex-start;
  border: 1px solid var(--flo-sem-color-border);
`;

export const ChatBox = styled.div`
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.07);
  position: sticky;
  bottom: 0;
  width: 100%;
  border-radius: 8px;
`;

export const TextAreaActionBar = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid var(--flo-sem-color-border);
  border-top: none;
  background-color: var(--flo-sem-color-light-background);
`;

export const SubmitChatButton = styled(Button)`
  height: 32px;
  width: 32px;
  padding: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
`;

export const OptimizePromptButton = styled(Button)`
  height: 32px;
  width: 32px;
  padding: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
`;

export const UndoButton = styled(Button)`
  height: 32px;
  width: 32px;
  padding: 8px;
`;

export const OptimizationStatusFrame = styled.div<{ isVisible: boolean }>`
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(${(props) => (props.isVisible ? '0' : '100%')});
  width: 90%;
  height: 32px;
  background-color: #f1f3f9;
  border: 1px solid var(--flo-sem-color-border);
  border-bottom: none;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: clamp(12px, 2vw, var(--flo-base-font-size-3));
  font-weight: var(--flo-base-font-weight-4);
  color: var(--flo-sem-color-text);
  opacity: ${(props) => (props.isVisible ? '1' : '0')};
  transition:
    transform 0.2s ease-out,
    opacity 0.2s ease-out;
  pointer-events: ${(props) => (props.isVisible ? 'auto' : 'none')};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: -1px;
`;

export const StatusText = styled.span`
  font-weight: var(--flo-base-font-weight-3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const EnhancingText = styled.span`
  font-weight: var(--flo-base-font-weight-3.5);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const GradientOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: sweep 1.5s infinite linear;
  pointer-events: none;

  @keyframes sweep {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
`;

export const StyledTextArea = styled(TextArea)`
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  border: 1px solid var(--flo-sem-color-border);
  border-bottom: none;
  & textarea {
    resize: none;
  }
`;

export const TooltipContent = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
`;

export const StyledTooltipContent = styled(Tooltip.Content)`
  padding: 4px 6px;
`;

export const LoadingOverlay = styled.div`
  position: absolute;
  top: 97px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  height: 65.5%;
  width: 98%;
`;
