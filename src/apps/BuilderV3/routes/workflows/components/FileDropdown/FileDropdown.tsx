// @ts-nocheck
import { DropdownPanel, DropdownButton } from '@floqastinc/flow-ui_core';
import { useAtom } from 'jotai';
import { t } from '@/utils/i18n';
import { useEffect, useState } from 'react';
import { useExampleQueries } from '../../BuilderPage.hooks';
import { selectedFileAtom } from '../../BuilderPage.store';

export const FileDropdown = () => {
  const [isFileDropdownOpen, setIsFileDropdownOpen] = useState(false);

  const [selectedFile, setSelectedFile] = useAtom(selectedFileAtom);

  const handleFileDropdownChange = async (dropdownOptionId: string) => {
    const file = allFiles.find((file) => file.id === dropdownOptionId);
    if (!file) {
      console.error(t('components.Builder.Errors.noFileinDropdown') + `${dropdownOptionId}`);
      return;
    }
    setSelectedFile(file);
    setIsFileDropdownOpen(false);

    // TODO: Rehandle "Current Output" if we don't section the dropdown
  };

  // Examples
  // TODO: Replace with task/:taskId/sources call
  const { exampleInputsQuery, exampleOutputsQuery } = useExampleQueries();

  const inputFiles =
    exampleInputsQuery.data
      // @ts-ignore
      ?.map((input) => {
        if (input.value?.kind === 'FILE') {
          return {
            id: input.id,
            type: 'input',
            name: input.value.name,
          };
        }
        return undefined;
      })
      .filter(Boolean) ?? [];

  const outputFiles =
    exampleOutputsQuery.data
      ?.map((output) => {
        if (output.value?.kind === 'FILE') {
          return {
            id: output.id,
            type: 'output',
            // NOTE/TEMP: This assumes a single output. This may not always
            //  be the case in the future if we support multi-output
            name: 'Current Output',
            // name: output.value.name,
          };
        }
        return undefined;
      })
      .filter(Boolean) ?? [];
  const allFiles = [...inputFiles, ...outputFiles];

  // This block serves 2 purposes:
  // 1. Selecting a default file when no file has been selected
  //    --> default to example input
  // 2. Selecting the output file for the example when one is generated
  //     from a message sent to the LLM.
  useEffect(() => {
    const invalidFileIsSelected = allFiles.every((file) => file.id !== selectedFile?.id);
    if (
      outputFiles.length > 0 &&
      (invalidFileIsSelected || !selectedFile || selectedFile?.type === 'input')
    ) {
      setSelectedFile(outputFiles[0]);
    } else if (inputFiles.length > 0 && (invalidFileIsSelected || !selectedFile)) {
      setSelectedFile(inputFiles[0]);
    }
    // NOTE: Using these values since they don't change on every render,
    // the other ones (outputFiles, inputFiles) do.
  }, [exampleOutputsQuery.data, exampleInputsQuery.data]);

  return (
    <>
      <DropdownPanel
        isOpen={isFileDropdownOpen}
        disableClear
        disableFilter
        onOpenChange={setIsFileDropdownOpen}
        onChange={handleFileDropdownChange}
        selectionMode="single"
        selectedValues={selectedFile?.name}
      >
        <DropdownPanel.Trigger>
          <DropdownButton label="File" isOpen={isFileDropdownOpen}>
            {selectedFile?.name || t('components.Builder.selectFile')}
          </DropdownButton>
        </DropdownPanel.Trigger>
        <DropdownPanel.Content>
          <DropdownPanel.Group title="Inputs">
            {inputFiles.map((val) => (
              <DropdownPanel.Option value={val.id} key={val.id}>
                {val.name}
              </DropdownPanel.Option>
            ))}
          </DropdownPanel.Group>
          <DropdownPanel.Group title="Outputs">
            {outputFiles.map((val) => (
              <DropdownPanel.Option value={val.id} key={val.id}>
                {t('components.Builder.currentOutput')}
              </DropdownPanel.Option>
            ))}
          </DropdownPanel.Group>
        </DropdownPanel.Content>
      </DropdownPanel>
    </>
  );
};
