import TableOutlined from '@floqastinc/flow-ui_icons/material/TableOutlined';
import type * as types from '@floqastinc/transform-v3';
import * as Styled from './FileInput.styled';
import { FileDownload } from './FileDownload';

type FileInputProps = {
  workflowRunInputId: string;
  file: types.FileArgument;
  taskId?: string;
  exampleInputId?: string;
  exampleSetId?: string;
};

export const FileInput = ({ workflowRunInputId, file }: FileInputProps) => {
  return (
    <Styled.InputContainer>
      <Styled.IconContainer>
        <TableOutlined size="20" />
      </Styled.IconContainer>
      <FileDownload
        workflowRunInputId={workflowRunInputId}
        file={file}
        data-tracking-id="runner-step-download-file-input"
      />
    </Styled.InputContainer>
  );
};
