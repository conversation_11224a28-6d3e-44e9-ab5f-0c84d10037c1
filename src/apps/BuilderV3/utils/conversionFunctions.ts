import { FlolakeConnectionData } from '@/api/shared/types';

export const createSystemKey = (system: FlolakeConnectionData): string => {
  return `${system.transformSystem} - ${system.connectionName}`;
};

export const convertTokenToSchema = (
  query: string,
  flolakeData: FlolakeConnectionData[],
): string => {
  if (!query) return '';

  return query.replace(/\{([^}]+)\}/g, (match, tokenContent) => {
    // Try to match system+connection
    const sysMatch = flolakeData.find((sys) => {
      const systemKey = createSystemKey(sys);
      return systemKey === tokenContent || systemKey.toLowerCase() === tokenContent.toLowerCase();
    });

    if (sysMatch) {
      return sysMatch.schemaName;
    }
    // Otherwise, just remove curly braces for table/column
    return tokenContent;
  });
};

export const convertSchemaToToken = (
  query: string,
  flolakeData: FlolakeConnectionData[],
): string => {
  if (!query) return '';

  // Sort by schemaName length descending to avoid partial matches
  const sortedData = [...flolakeData].sort((a, b) => b.schemaName.length - a.schemaName.length);

  // Build a regex that matches any system at the start of a reference
  const systemNames = sortedData.map(sys => sys.schemaName.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'));
  if (systemNames.length === 0) return query;
  const systemPattern = systemNames.join('|');

  // Regex: match system[.table][.column], but only if it starts with a system
  // e.g. NETSUITE.ACCOUNT._FIVETRAN_DELETED or NETSUITE.ACCOUNT or NETSUITE
  const regex = new RegExp(`\\b(${systemPattern})(?:\\.([A-Za-z0-9_]+))?(?:\\.([A-Za-z0-9_]+))?\\b`, 'gi');

  return query.replace(regex, (match, sys, table, column) => {
    // Find the system in flolakeData (case-insensitive)
    const systemObj = sortedData.find(s => s.schemaName.toLowerCase() === sys.toLowerCase());
    if (!systemObj) return match; // Shouldn't happen, but fallback

    const systemKey = createSystemKey(systemObj);
    let result = `{${systemKey}}`;
    if (table) result += `.{${table.toUpperCase()}}`;
    if (column) result += `.{${column.toUpperCase()}}`;
    return result;
  });
};