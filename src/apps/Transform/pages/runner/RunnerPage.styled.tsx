import { styled } from 'styled-components';
import { InlineAlert } from '@floqastinc/flow-ui_core';

export const Page = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

export const AlertBanner = styled(InlineAlert)`
  padding: 8px 16px;
  margin: 8px 16px;
  width: calc(100% - 32px);
`;

export const RunnerBody = styled.div`
  background-color: #f8f8f7;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-height: 0;
  overflow: hidden;
`;

export const RunnerBodyContent = styled.div`
  display: flex;
  min-height: 0;
  flex-grow: 1;
`;

export const StepListContainer = styled.div<{
  $isPreview: boolean;
  $isCollapsed: boolean;
}>`
  border-right: ${({ $isPreview }) => ($isPreview ? 'none' : `1px solid #e3e1de`)};
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
  min-height: 0;
  padding: 0 16px;
  position: relative;
  transition: width 0.3s ease-in-out;
  overflow: visible;
  width: ${({ $isCollapsed }) => ($isCollapsed ? '242px' : '440px')};
  flex-shrink: 0;
`;

export const CollapseButton = styled.div`
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  height: 28px;
  width: 28px;
  right: -14px;
  border-radius: 50%;
  border: 1px solid #e3e1de;
  background-color: white;
  z-index: var(--fq-z-index-toast);
`;

export const FilePreviewContainer = styled.div`
  background-color: var(--flo-base-color-neutral-0);
  flex-grow: 1;
  overflow: hidden;
  width: 100%;
`;
