import { styled } from 'styled-components';

export const StepContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

export const StepButton = styled.button<{ $isActive?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 6px;
  border: 2px solid
    ${(props) =>
    props.$isActive ? 'var(--flo-base-color-neutral-500)' : 'var(--flo-sem-color-stroke-forms)'};
  position: relative;
  background-color: var(--flo-sem-color-white);
  color: var(--flo-sem-color-text-body-tertiary);
  transition: border 0.2s ease-in-out;

  &:hover {
    border: 2px solid var(--flo-base-color-neutral-500);
  }
  &:active {
    border: 2px solid var(--flo-base-color-neutral-500);
  }
`;

export const SkeletonStepButton = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 6px;
  border: 2px solid var(--flo-sem-color-stroke-forms);
  position: relative;
  background-color: var(--flo-sem-color-white);
  color: var(--flo-sem-color-text-body-tertiary);
`
