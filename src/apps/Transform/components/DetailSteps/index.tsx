import { Children, isValidElement } from 'react';
import Markdown from 'react-markdown';
import { Heading, Text, Skeleton } from '@floqastinc/flow-ui_core';
import { useMutation, useQuery } from '@tanstack/react-query';
import * as Styled from './styles';
import { getExamplesQuery } from '@BuilderV3/api/examples';
import v3, { ApiError } from '@/services/v3';
import { queryClient } from '@/components';

const SkeletonStepDetails = () =>
  [1, 2, 3, 4, 5].map((i) => (
    <Styled.DetailStep key={i}>
      <Styled.DetailStepName>
        <Skeleton lines={1} width="50%" />
      </Styled.DetailStepName>
      <Styled.DetailStepDescription>
        <Skeleton lines={4} width="100%" />
      </Styled.DetailStepDescription>
    </Styled.DetailStep>
  ));

const Li = ({
  children,
  ...rest
}: React.DetailedHTMLProps<React.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>) => {
  type ParagraphProps = { children: React.ReactNode };
  const children_: React.ReactNode[] = [];
  Children.forEach(children, (child) => {
    if (isValidElement(child) && child.type === 'p') {
      children_.push((child as React.ReactElement<ParagraphProps>).props.children);
    } else {
      children_.push(child);
    }
  });
  return <li {...rest}>{children_}</li>;
};

type DetailStepsProps = {
  workflowId: string;
  taskId: string;
  isOpen: boolean;
};
export const DetailSteps = ({ workflowId, taskId, isOpen }: DetailStepsProps) => {
  const examplesQuery = useQuery(getExamplesQuery({ workflowId, taskId }));
  const activeExampleSetQuery = useQuery({
    ...getExamplesQuery({ workflowId, taskId }),
    select: (data) => data.find((exampleSet) => exampleSet.status === 'ACTIVE'),
    enabled: isOpen,
  });
  const createTaskDescriptionMutation = useMutation({
    mutationKey: ['taskDescription', 'create', { workflowId, taskId }],
    mutationFn: async ({ exampleSetId }: { exampleSetId: string }) => {
      const response = await v3.taskDescriptions.createTaskDescription({
        workflowId,
        taskId,
        exampleSetId,
        description: { generate: true },
      });
      if (response.errors.length > 0) throw new ApiError(response.errors);
      if (!response.data)
        throw new Error('Unexpected data error: no task description body returned.');
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['taskDescription', workflowId, taskId] });
    },
  });
  const getDescriptionQuery = useQuery({
    queryKey: ['taskDescription', workflowId, taskId],
    queryFn: async () => {
      c3
      const response = await v3.taskDescriptions.getTaskActiveDescription({ workflowId, taskId });
      if (response.errors.length > 0) {
        if (response.errors.length === 1 && response.errors[0].code === 'NOT_FOUND') {
          const activeExampleSet = activeExampleSetQuery.data;
          if (!activeExampleSet) throw new Error('No active example set found');
          createTaskDescriptionMutation.mutate({ exampleSetId: activeExampleSet.id });
        }
        throw new ApiError(response.errors);
      }
      if (!response.data)
        throw new Error('Unexpected data error: no task description body returned.');
      if (!response.data.id) {
        const activeExampleSet = activeExampleSetQuery.data;
        if (!activeExampleSet) throw new Error('No active example set found');
        createTaskDescriptionMutation.mutate({ exampleSetId: activeExampleSet.id });
      }
      return response.data;
    },
    enabled: isOpen && !!activeExampleSetQuery.data,
    retry: false,
  });
  const isLoading =
    getDescriptionQuery.isPending ||
    getDescriptionQuery.isFetching ||
    createTaskDescriptionMutation.isPending;
  return (
    <Styled.DetailSteps>
      {examplesQuery.data?.[0] && examplesQuery.data?.[0].status === 'DRAFT' && isLoading ? (
        <Styled.DetailStep key={''}>
          <Styled.DetailStepName>
            <Heading variant="h5" weight="medium">
              Unpublished Step
            </Heading>
          </Styled.DetailStepName>
          <Styled.DetailStepDescription>
            <Text truncate={false}>
              This step is currently unpublished. Please go to the version tab and publish it to
              generate a description.
            </Text>
          </Styled.DetailStepDescription>
        </Styled.DetailStep>
      ) : isLoading ? (
        <SkeletonStepDetails />
      ) : (
        getDescriptionQuery?.data?.description.map((subStep, i) => (
          <Styled.DetailStep key={subStep.heading}>
            <Styled.DetailStepName>
              <Heading variant="h5" weight="medium">
                {i + 1}. {subStep.heading}
              </Heading>
            </Styled.DetailStepName>
            <Styled.DetailStepDescription>
              <Markdown
                components={{
                  li({ node: _node, ...rest }) {
                    return <Li {...rest} />;
                  },
                }}
              >
                {subStep.details}
              </Markdown>
            </Styled.DetailStepDescription>
          </Styled.DetailStep>
        ))
      )}
    </Styled.DetailSteps>
  );
};
