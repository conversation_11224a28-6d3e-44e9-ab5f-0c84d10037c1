import { styled } from 'styled-components';
import MaterialAddIcon from '@floqastinc/flow-ui_icons/material/AddCircleOutline';

export const Wrapper = styled.div`
  background-color: var(--flo-base-color-neutral-0);
  height: 100%;
  width: 100%;
`;

export const Header = styled.div`
  width: 100%;
  box-sizing: border-box;
  padding: 1rem 1.5rem;
`;

export const TabGroup = styled.div`
  display: flex;
  padding: 0 1.5rem;
`;

export const TabContent = styled.div`
  padding: 1.5rem 1.5rem;
`;

export const AddIcon = styled(MaterialAddIcon)`
  fill: white;
`;
