import { AppSyncService } from '@/services/appsync-service';
import v3, { ApiError } from '@/services/v3';
import { Subset } from '@/types';
import { poll } from '@/utils/api';
import type {
  ExampleLlmScriptStrategy,
  ExampleLlmThreadStrategy,
  ExampleScriptStrategy,
  ExampleStrategy,
  WorkflowTaskStrategyKind,
} from '@floqastinc/transform-v3';
import { useMutationState } from '@tanstack/react-query';
import { useExample } from '@v3/examples';
import { useCallback, useEffect, useMemo, useRef } from 'react';

type ConversationBasedStrategy =
  | ExampleLlmThreadStrategy
  | ExampleLlmScriptStrategy
  | ExampleScriptStrategy;
export const isConversationBasedStrategy = (
  strategy: ExampleStrategy,
): strategy is ConversationBasedStrategy => {
  const kinds: Subset<WorkflowTaskStrategyKind, 'LLM_THREAD' | 'SCRIPT'>[] = [
    'LLM_THREAD',
    'SCRIPT',
  ] as const;
  return kinds.includes(strategy?.kind);
};

type SendMessageArgs = {
  workflowId: string;
  taskId: string;
  message: string;
  exampleSetId: string;
};

type EditMessageArgs = {
  workflowId: string;
  taskId: string;
  message: { content: string; id: string };
  exampleSetId: string;
};

type RegenerateMessageArgs = {
  workflowId: string;
  taskId: string;
  messageId: string;
  exampleSetId: string;
};

type OptimizeMessageArgs = {
  workflowId: string;
  taskId: string;
  message: { content: string };
  exampleSetId: string;
};

export const createMessage = async ({
  workflowId,
  taskId,
  message,
  exampleSetId,
}: SendMessageArgs) => {
  const result = await v3.messages.createMessage({
    workflowId,
    taskId,
    message: {
      content: message,
    },
    exampleSetId,
  });

  if (result.errors.length) {
    throw new ApiError(result.errors);
  }
  const pollResult = await poll(
    () =>
      // TODO: likely need to check status on message/example set
      v3.examples.getExample({
        workflowId,
        taskId,
        exampleSetId,
      }),
    {
      succeed: (result) => {
        const strategy = result.data?.strategy;
        if (!strategy) return false;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === 'READY';
        }

        return false;
      },
      fail: (result) => {
        const strategy = result.data?.strategy;

        if (!strategy) return true;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === 'FAILED';
        }

        return false;
      },
    },
  );

  return pollResult;
};

export const editMessage = async ({
  workflowId,
  taskId,
  exampleSetId,
  message,
}: EditMessageArgs) => {
  const { content, id } = message;
  try {
    const result = await v3.messages.editMessage({
      workflowId,
      taskId,
      exampleSetId,
      messageId: id,
      message: {
        content,
      },
    });
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
  } catch (e: any) {
    throw new ApiError([e]);
  }

  return await poll(
    () =>
      // TODO: likely need to check status on message/example set
      v3.examples.getExample({
        workflowId,
        taskId,
        exampleSetId,
      }),
    {
      succeed: (result) => {
        const strategy = result.data?.strategy;
        if (!strategy) return false;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === 'READY';
        }

        return false;
      },
      fail: (result) => {
        const strategy = result.data?.strategy;

        if (!strategy) return true;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === 'FAILED';
        }

        return false;
      },
    },
  );
};

export const regenerateMessage = async ({
  workflowId,
  taskId,
  exampleSetId,
  messageId,
}: RegenerateMessageArgs) => {
  try {
    const result = await v3.messages.regenerateMessage({
      workflowId,
      taskId,
      exampleSetId,
      messageId,
    });
    if (result.errors.length) {
      throw new ApiError(result.errors);
    }
  } catch (e: any) {
    throw new ApiError([e]);
  }

  return await poll(
    () =>
      // TODO: likely need to check status on message/example set
      v3.examples.getExample({
        workflowId,
        taskId,
        exampleSetId,
      }),
    {
      succeed: (result) => {
        const strategy = result.data?.strategy;
        if (!strategy) return false;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === 'READY';
        }

        return false;
      },
      fail: (result) => {
        const strategy = result.data?.strategy;

        if (!strategy) return true;

        if (isConversationBasedStrategy(strategy)) {
          return strategy?.conversationStatus === 'FAILED';
        }

        return false;
      },
    },
  );
};

export const optimizeMessage = async ({
  workflowId,
  taskId,
  message,
  exampleSetId,
}: OptimizeMessageArgs) => {
  try {
    const result = await v3.prompts.createPrompt({
      exampleSetId,
      body: {
        prompt: message.content,
      },
    });

    if (result.errors.length) {
      throw new ApiError(result.errors);
    }

    const pollResult = await poll(
      () =>
        v3.examples.getExample({
          workflowId,
          taskId,
          exampleSetId,
        }),
      {
        succeed: (result) => {
          const strategy = result.data?.strategy;
          if (!strategy) return false;

          if (isConversationBasedStrategy(strategy)) {
            return strategy?.conversationStatus === 'READY';
          }

          return false;
        },
        fail: (result) => {
          const strategy = result.data?.strategy;

          if (!strategy) return true;

          if (isConversationBasedStrategy(strategy)) {
            return strategy?.conversationStatus === 'FAILED';
          }

          return false;
        },
      },
    );

    return pollResult;
  } catch (e: any) {
    throw new ApiError([e]);
  }
};

const useIsMutationPending = (
  mutationKey: (string | { workflowId: string; taskId: string; exampleSetId: string })[],
) => {
  return useMutationState({
    filters: { mutationKey },
    select: (mutation) => mutation.state.status,
  }).some((status) => status === 'pending');
};

type UseMessageStreamingArgs = {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
  onMessageEvent: (message: string) => void;
  enabled?: boolean;
};
// TODO: Implement some sort of token buffering to smooth output here.
export const useMessageStreaming = ({
  workflowId,
  taskId,
  exampleSetId,
  onMessageEvent,
  enabled = true,
}: UseMessageStreamingArgs) => {
  const createMessageIsPending = useIsMutationPending([
    'createMessage',
    { workflowId, taskId, exampleSetId },
  ]);
  const optimizeMessageIsPending = useIsMutationPending([
    'optimizeMessage',
    { workflowId, taskId, exampleSetId },
  ]);

  const editIsPending = useIsMutationPending(['editMessage', { workflowId, taskId, exampleSetId }]);
  const deletionIsPending = useIsMutationPending([
    'deleteMessage',
    { workflowId, taskId, exampleSetId },
  ]);
  const regenerationIsPending = useIsMutationPending([
    'regenerateMessage',
    { workflowId, taskId, exampleSetId },
  ]);

  const exampleSetQuery = useExample(
    {
      workflowId,
      taskId,
      exampleSetId,
    },
    {
      refetchInterval: (query) => {
        const isMessageMutationPending =
          createMessageIsPending ||
          editIsPending ||
          deletionIsPending ||
          regenerationIsPending ||
          optimizeMessageIsPending;
        const isThreadRunBusy =
          query.state.data?.data &&
          isConversationBasedStrategy(query.state.data.data.strategy) &&
          query.state.data.data.strategy.conversationStatus === 'BUSY';
        if (!isMessageMutationPending && isThreadRunBusy) {
          return 1500;
        }
        return undefined;
      },
    },
  );

  const isThreadRunBusy =
    exampleSetQuery.data && isConversationBasedStrategy(exampleSetQuery.data.strategy)
      ? exampleSetQuery.data.strategy.conversationStatus === 'BUSY'
      : false;

  const assistantMessageIsPending =
    createMessageIsPending ||
    editIsPending ||
    regenerationIsPending ||
    isThreadRunBusy ||
    optimizeMessageIsPending;

  // Stable reference to the service instance - only recreate when connection params change
  const appsyncService = useMemo(() => {
    if (!workflowId || !taskId || !exampleSetId) {
      return null;
    }
    return new AppSyncService<string>({
      workflowId,
      taskId,
      exampleSetId,
    });
  }, [workflowId, taskId, exampleSetId]);

  // Stable message handler to prevent unnecessary reconnections
  const stableOnMessageEvent = useCallback(
    (event: any) => {
      try {
        onMessageEvent(event);
      } catch (error) {
        console.error('Error handling AppSync message:', error);
      }
    },
    [onMessageEvent],
  );

  // Store disconnect function to clean up properly
  const disconnectRef = useRef<(() => void) | null>(null);

  // Effect for managing connection based on pending states
  useEffect(() => {
    if (!appsyncService || !enabled) {
      return;
    }

    const shouldBeConnected = assistantMessageIsPending || deletionIsPending;

    if (shouldBeConnected) {
      // Only connect if not already connected/authenticated
      if (!appsyncService.isAuthenticated) {
        const handleConnection = async () => {
          try {
            disconnectRef.current = await appsyncService.connect({
              handleMessageData: stableOnMessageEvent,
            });
          } catch (error) {
            console.error('Failed to connect to AppSync:', error);
          }
        };
        handleConnection();
      } else {
        // If already authenticated, just update the message handler
        appsyncService.handleMessageData = stableOnMessageEvent;
      }
    } else {
      // Disconnect when no longer needed
      if (appsyncService.isConnected && disconnectRef.current) {
        disconnectRef.current();
        disconnectRef.current = null;
      }
    }
  }, [appsyncService, assistantMessageIsPending, deletionIsPending, enabled, stableOnMessageEvent]);

  // Cleanup effect when component unmounts or service changes
  useEffect(() => {
    return () => {
      if (disconnectRef.current) {
        disconnectRef.current();
      }
    };
  }, [appsyncService]);

  return { assistantMessageIsPending, deletionIsPending };
};
// User messages will be displayed as:
/*
**INPUTS**:
{Inputs}

**MESSAGE**:
${Message}
*/
// We just want to display the message to the user
export const cleanMessage = (message: string) => {
  return message.replace(/\*\*INPUTS\*\*:[\s\S]*?\*\*MESSAGE\*\*:/, '').trim();
};
