import { styled } from 'styled-components';
import { SectionHeader } from '@floqastinc/flow-ui_core';

export const StepListContainer = styled.div`
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

export const SectionHeaderContainer = styled.div`
  flex: 0 0 auto;
  padding-top: 16px;
`;

export const SectionHeaderHeading = styled(SectionHeader.Heading)`
  margin: 0;
`;

export const StepsContainer = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  padding: 16px 0px 8px;
  overflow-y: auto;

  /* Hide scrollbar on overflow */
  /* Chrome, Safari Opera and Firefox */
  scrollbar-width: none;
  /* IE and Edge */
  -ms-overflow-style: none;
`;

export const Line = styled.div`
  border: 1px solid var(--flo-base-color-neutral-300);
  flex-shrink: 0;
  height: 16px;
  min-height: 16px;
  width: 0px;
`;
