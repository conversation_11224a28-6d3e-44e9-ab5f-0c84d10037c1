const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

type Check<T> = (result: T) => boolean;
export const poll = async <T extends (...args: any[]) => any>(
	fn: T,
	{
		succeed,
		fail,
	}: {
		succeed: Check<Awaited<ReturnType<T>>>;
		fail: Check<Awaited<ReturnType<T>>>;
		pass?: Check<Awaited<ReturnType<T>>>;
	},
	interval = 5000,
): Promise<ReturnType<T>> => {
	return new Promise(async (resolve, reject) => {
		let retries = 0;
		while (true) {
			if (retries > 25) {
				reject(new Error("Error polling task status: Max retries exceeded"));
				return;
			}
			const result = await fn();
			if (fail(result)) {
				reject(result);
				return;
			}
			if (succeed(result)) {
				resolve(result);
				return;
			}
			retries++;
			await wait(interval);
		}
	});
};
