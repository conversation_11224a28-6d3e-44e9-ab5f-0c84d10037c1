import { t as i18T, TOptions } from 'i18next';
import en from '../locales/en/translation.json';
import fr from '../locales/fr/translation.json';
import de from '../locales/de/translation.json';
import ja from '../locales/ja/translation.json';

/**
 * Gets the leaf keys of a nested object as a discriminated union
 *  of the dotted object structure.
 *
 * @example
 * ```ts
 * type Nested = {
 *   a: {
 *     b: {
 *       c: string;
 *     };
 *     d: string;
 *   };
 *   e: string;
 * };
 *
 * type Keys = DotLeafKeysOf<Nested>;
 * // --> 'a.b.c' | 'a.d' | 'e';
 * ```
 */
type DottedLeafKeysOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? ObjectType[Key] extends readonly any[]
      ? `${Key}` // Arrays are treated as leaf values, included as keys, no recursion
      : `${Key}.${DottedLeafKeysOf<ObjectType[Key]>}` // Recurse, don't include intermediate values as keys
    : `${Key}`; // Primitive values are leafs, included as keys
}[keyof ObjectType & (string | number)];

// Static type-check validator to ensure all translations have the same nested key structure
type ValidateTranslationStructure<T extends Record<string, Record<string, any>>> = {
  [K in keyof T]: T[K] extends Record<string, any>
    ? DottedLeafKeysOf<T[K]> extends DottedLeafKeysOf<T['en']>
      ? DottedLeafKeysOf<T['en']> extends DottedLeafKeysOf<T[K]>
        ? T[K]
        : never
      : never
    : never;
};

type ValidatedTranslations = ValidateTranslationStructure<typeof translations>;

// Static type-check to ensure all translations contain the same keys
type AllTranslationsMatch = ValidatedTranslations extends never
  ? false
  : keyof ValidatedTranslations extends keyof typeof translations
    ? keyof typeof translations extends keyof ValidatedTranslations
      ? true
      : false
    : false;

// Helper to give better hover/compiler error messages
type _AssertValidTranslations = AllTranslationsMatch extends true
  ? true
  : {
      error: 'Translation files have mismatched key structures';
      hint: 'Check that all translation.json files have identical nested key structures';
    };

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const translations = {
  en,
  fr,
  de,
  ja,
} as const;

// Only use English translation keys for type checking
type TranslationKeys = DottedLeafKeysOf<typeof en>;

export type I18Key = TranslationKeys;
export type SupportedLanguages = keyof typeof translations;

// Runtime validation of translation keys
const validateTranslationKey = (key: string) => {
  if (process.env.NODE_ENV === 'development') {
    const keys = key.split('.');
    let current: any = en;
    
    for (const k of keys) {
      if (!(k in current)) {
        console.error(`Missing translation key: ${key}`);
        return false;
      }
      current = current[k];
    }
    
    if (typeof current !== 'string') {
      console.error(`Translation key ${key} does not resolve to a string`);
      return false;
    }
  }
  return true;
};

export const t = (key: I18Key, options?: TOptions) => {
  validateTranslationKey(key);
  return i18T(key, options);
};
