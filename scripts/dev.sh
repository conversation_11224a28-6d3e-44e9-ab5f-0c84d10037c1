#!/bin/sh
REACT_APP_NODE_ENV=development \
NODE_ENV=development \
REACT_APP_RUNTIME_MODE=standalone \
REACT_APP_USE_PROXY=true \
REACT_APP_USE_CLOSE_PROXY=$npm_config_close \
REACT_APP_USE_LAMBDA_PROXY=$npm_config_lambdas \
REACT_APP_PROXY_ENV=${npm_config_env:-$FQ_ENV} \
REACT_APP_PROXY_EXCLUDE=$npm_config_exclude \
PORT=${PORT:-${npm_config_port:-3001}} \
WDS_SOCKET_PORT=${PORT:-${npm_config_port:-3001}} \
REACT_APP_CLOSE_API_PORT=${PORT:-${npm_config_port:-3001}} \
FORCE_COLOR=true \
vite
