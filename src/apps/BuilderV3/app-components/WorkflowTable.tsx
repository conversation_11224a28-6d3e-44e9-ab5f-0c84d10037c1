import { useState } from 'react';
import { styled } from 'styled-components';
import { t } from '@/utils/i18n';
import { array } from 'prop-types';
import MoreVert from '@floqastinc/flow-ui_icons/material/MoreVert';
import MaterialDeleteIcon from '@floqastinc/flow-ui_icons/material/Delete';
import MaterialEditIcon from '@floqastinc/flow-ui_icons/material/Edit';
import MagicAiStar from '@floqastinc/flow-ui_icons/fq/MagicAIStar';
import KeyboardReturn from '@floqastinc/flow-ui_icons/material/Replay';
import { useMutation } from '@tanstack/react-query';
import { Link as ReactRouterLink, useNavigate } from 'react-router-dom';
import {
  Link,
  IconButton,
  Table,
  THead,
  TBody,
  TH,
  TR,
  TD,
  DropdownPanel,
} from '@floqastinc/flow-ui_core';
import KeyboardArrowUp from '@floqastinc/flow-ui_icons/material/KeyboardArrowUp';
import KeyboardArrowDown from '@floqastinc/flow-ui_icons/material/KeyboardArrowDown';
import UnfoldMore from '@floqastinc/flow-ui_icons/material/UnfoldMore';
import _ from 'lodash';
import { Workflow } from '@floqastinc/transform-v3';
import { Principal } from '@floqastinc/transform-v0';
import { Audit } from './Audit';
import { Status, statusMap } from './Status';
import { queryKeys } from '@BuilderV3/api/query-keys';
import v3 from '@/services/v3';
import { Time } from '@/components/Time';
import { AGENTS, BUILDER, V3, RUNNER, AGENT } from '@/constants';
import { useModal } from '@/components/ModalContext';
import { queryClient } from '@/components/queryClient';
import { useUpdateWorkflow } from '@BuilderV3/routes/workflows/BuilderPage.hooks';

type WorkflowTableProps = {
  workflows: Workflow[];
  principals: Principal[];
  nameSortOrder: string;
  updatedAtSortOrder: string;
  lastRunSortOrder: string;
  handleNameSort: (sortOrder: string) => void;
  handleUpdatedAtSort: (sortOrder: string) => void;
  handleLastRunSort: (sortOrder: string) => void;
};

const IconWrap = styled.div`
  float: right;
  display: flex;
`;

const DropdownPanelText = styled.span`
  margin-top: 3px;
`;

const ActionLinks = styled.div`
  a {
    display: inline-flex;
    padding: 4px 0;
    margin-right: 7px;
  }
  svg {
    margin-left: 3px;
  }
  min-width: 200px;
`;

const iconProps = {
  color: 'var(--flo-sem-color-icon-primary)',
  size: 20,
};

const DescTD = styled(TD)`
  width: 45%;
`;

const WorkflowTable = ({
  workflows,
  principals,
  nameSortOrder,
  updatedAtSortOrder,
  lastRunSortOrder,
  handleNameSort,
  handleUpdatedAtSort,
  handleLastRunSort,
}: WorkflowTableProps) => {
  const { openModal } = useModal();
  const navigate = useNavigate();

  // const role = usePrincipal().role;
  // TODO: obviously get rid of this before hitting prod...
  const role = 'OMNI';

  const [isMoreOptionsOpenForWorkflowId, setIsMoreOptionsOpenForWorkflowId] = useState<
    string | null
  >(null);
  const updateWorkflow = useUpdateWorkflow();
  const deleteWorkflow = useMutation({
    mutationFn: (id: string) => {
      return v3.workflows.deleteWorkflow({ workflowId: id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.all(),
      });
    },
  });
  const flipSortOrder = (sortOrder: string, onSort: (newOrder: string) => void) => {
    let nextSortOrder = 'asc';
    if (sortOrder === 'asc') nextSortOrder = 'desc';
    else nextSortOrder = 'asc';
    onSort(nextSortOrder);
  };
  const setSortIcon = (sortOrder: string) => {
    if (sortOrder === 'none') return <UnfoldMore {...iconProps} />;

    if (sortOrder === 'asc') return <KeyboardArrowUp {...iconProps} />;

    if (sortOrder === 'desc') return <KeyboardArrowDown {...iconProps} />;
  };
  return (
    <Table topBorder middleBorders outerRoundedBorder>
      <THead>
        <TR>
          <TH
            onClick={() => {
              flipSortOrder(nameSortOrder, handleNameSort);
            }}
            style={{ cursor: 'pointer' }}
          >
            {AGENT}
            {setSortIcon(nameSortOrder)}
          </TH>
          <TH>{t('generics.description')}</TH>
          <TH
            onClick={() => {
              flipSortOrder(updatedAtSortOrder, handleUpdatedAtSort);
            }}
            style={{ cursor: 'pointer' }}
          >
            {t('generics.lastModified')}
            {setSortIcon(updatedAtSortOrder)}
          </TH>
          <TH
            onClick={() => {
              flipSortOrder(lastRunSortOrder, handleLastRunSort);
            }}
            style={{ cursor: 'pointer' }}
          >
            {t('generics.lastRun')}
            {setSortIcon(lastRunSortOrder)}
          </TH>
          <TH>{t('generics.createdBy')}</TH>
          <TH>{t('generics.status')}</TH>
          <TH></TH>
        </TR>
      </THead>
      <TBody>
        {workflows?.map((workflow) => (
          <TR key={workflow.id}>
            <TD>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'start',
                  flexDirection: 'column',
                }}
              >
                {workflow.name}
                <ActionLinks>
                  <Link as={ReactRouterLink} to={`/${BUILDER}/${V3}/${AGENTS}/${workflow.id}`}>
                    <IconWrap>
                      <MagicAiStar color="action" size={15} />
                    </IconWrap>
                    <span>{t('generics.build')}</span>
                  </Link>
                  <Link as={ReactRouterLink} to={`/${RUNNER}/${V3}/${AGENTS}/${workflow.id}`}>
                    <IconWrap>
                      <KeyboardReturn color="action" size={15} />
                    </IconWrap>
                    <span>{t('generics.run')}</span>
                  </Link>
                </ActionLinks>
              </div>
            </TD>
            <DescTD>
              <TD.Description>{workflow.description}</TD.Description>
            </DescTD>
            <TD>
              <Time value={workflow.updatedAt} relative />
            </TD>
            <TD>
              <Time value={workflow.latestRun?.date} relative />
            </TD>
            <TD>
              <Audit
                principalId={workflow.createdBy ?? workflow.createdById}
                principals={principals}
              />
            </TD>
            <TD>
              <Status status={statusMap[workflow.status as keyof typeof statusMap] || ''} />
            </TD>
            <TD>
              <DropdownPanel
                disableFilter
                disableClear
                isOpen={isMoreOptionsOpenForWorkflowId === workflow.id}
                onOpenChange={(isOpen: boolean) => {
                  if (isOpen) {
                    setIsMoreOptionsOpenForWorkflowId(workflow.id);
                  } else {
                    setIsMoreOptionsOpenForWorkflowId(null);
                  }
                }}
                onChange={(value: string) => {
                  if (value === 'delete-workflow') {
                    openModal('DeleteWorkflow', {
                      workflowName: workflow.name,
                      onSubmit: () => deleteWorkflow.mutate(workflow.id),
                    });
                  }

                  if (value === 'edit-workflow') {
                    openModal('EditWorkflow', {
                      workflow: workflow,
                      onSave: async (workflow: Workflow) => {
                        await updateWorkflow.mutateAsync(workflow);
                      },
                    });
                  }
                  if (value === 'run-workflow') {
                    navigate(`/${RUNNER}/${V3}/${AGENTS}/${workflow.id}`);
                  }

                  if (value === 'build-workflow') {
                    navigate(`/${BUILDER}/${V3}/${AGENTS}/${workflow.id}`);
                  }
                  setIsMoreOptionsOpenForWorkflowId(null);
                }}
              >
                <DropdownPanel.Trigger>
                  <IconButton
                    size="md"
                    onClick={() => {
                      setIsMoreOptionsOpenForWorkflowId(workflow.id);
                    }}
                  >
                    <MoreVert color="#1D2433" />
                  </IconButton>
                </DropdownPanel.Trigger>
                <DropdownPanel.Content>
                  {role === 'OMNI' && (
                    <DropdownPanel.Option
                      value={t('components.Builder.deleteWorkflow')}
                      key="delete-workflow"
                    >
                      <IconWrap>
                        <MaterialDeleteIcon color="action" />
                      </IconWrap>
                      <DropdownPanelText>
                        {t('generics.delete')} {AGENT}
                      </DropdownPanelText>
                    </DropdownPanel.Option>
                  )}
                  <DropdownPanel.Option
                    value={t('components.Builder.editWorkflow')}
                    key="edit-workflow"
                  >
                    <IconWrap>
                      <MaterialEditIcon color="action" />
                    </IconWrap>
                    <DropdownPanelText>
                      {t('components.Builder.edit')} {AGENT}
                    </DropdownPanelText>
                  </DropdownPanel.Option>
                  <DropdownPanel.Option
                    value={t('components.Builder.buildWorkflow')}
                    key="build-workflow"
                  >
                    <IconWrap>
                      <MagicAiStar color="action" />
                    </IconWrap>
                    <DropdownPanelText>
                      {t('generics.build')} {AGENT}
                    </DropdownPanelText>
                  </DropdownPanel.Option>
                  <DropdownPanel.Option
                    value={t('components.Builder.runWorkflow')}
                    key="run-workflow"
                  >
                    <IconWrap>
                      <KeyboardReturn color="action" />
                    </IconWrap>
                    <DropdownPanelText>
                      {t('generics.run')} {AGENT}
                    </DropdownPanelText>
                  </DropdownPanel.Option>
                </DropdownPanel.Content>
              </DropdownPanel>
            </TD>
          </TR>
        ))}
      </TBody>
    </Table>
  );
};

WorkflowTable.propTypes = {
  workflows: array.isRequired,
};

export default WorkflowTable;
