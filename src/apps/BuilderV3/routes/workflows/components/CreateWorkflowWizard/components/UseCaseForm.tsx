import { Heading, Text, TextArea } from '@floqastinc/flow-ui_core';
import { t } from 'i18next';
import { useState } from 'react';
import Markdown from 'react-markdown';
import { match } from 'ts-pattern';
import CheckCircle from '@floqastinc/flow-ui_icons/material/CheckCircle';
import { DEFAULT_USE_CASES } from '../default-use-cases';
import * as Styled from '../styled';

type UseCase = {
  name: string;
  content: string;
};

type UseCaseFormProps = {
  useCase: UseCase;
  setUseCase: (useCase: UseCase) => void;
};

type RadioButtonProps = {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
};

const RadioButton = ({ active, onClick, children }: RadioButtonProps) => {
  return (
    <Styled.RadioButton active={active} onClick={onClick}>
      <Text>{children}</Text>
      {active && (
        <Styled.CheckContainer>
          <CheckCircle color="var(--flo-base-color-core-600)" size="20" />
        </Styled.CheckContainer>
      )}
    </Styled.RadioButton>
  );
};

export const UseCaseForm = ({ useCase, setUseCase }: UseCaseFormProps) => {
  const [selectedUseCase, setSelectedUseCase] = useState('Custom');

  const handleUseCaseChange = (newUseCase: string) => {
    setSelectedUseCase(newUseCase);

    match(newUseCase)
      .with('Custom', () => {
        setUseCase({
          name: 'Custom',
          content: useCase.name === 'Custom' ? useCase.content : '',
        });
      })
      .otherwise(() => {
        const selectedCase = DEFAULT_USE_CASES.find((uc) => uc.name === newUseCase);
        if (selectedCase) {
          setUseCase({
            name: selectedCase.name,
            content: selectedCase.content,
          });
        }
      });
  };

  return (
    <Styled.UseCaseForm>
      <Styled.UseCaseFormContent>
        <Styled.Left>
          <Heading variant="h5" weight={4}>
            {t('generics.templates')}
          </Heading>
          <Styled.RadioButtonWrapper>
            <RadioButton
              active={selectedUseCase === 'Custom'}
              onClick={() => handleUseCaseChange('Custom')}
            >
              {t('generics.custom')}
            </RadioButton>
            {DEFAULT_USE_CASES.map((defaultUseCase) => (
              <RadioButton
                key={defaultUseCase.name}
                active={selectedUseCase === defaultUseCase.name}
                onClick={() => handleUseCaseChange(defaultUseCase.name)}
                // eslint-disable-next-line no-restricted-syntax
                data-testid={`radio-button-${defaultUseCase.name.toLowerCase().replace(/\s+/g, '-')}`}
              >
                {defaultUseCase.name}
              </RadioButton>
            ))}
          </Styled.RadioButtonWrapper>
        </Styled.Left>
        <Styled.Right>
          <Heading variant="h5" weight={4}>
            {t('generics.details')}
          </Heading>
          {match(selectedUseCase)
            // eslint-disable-next-line i18next/no-literal-string
            .with('Custom', () => (
              <TextArea
                styleOverrides={{
                  root: {
                    width: '100%',
                  },
                  textarea: {
                    width: '100%',
                    height: '100%',
                    padding: '16px',
                  },
                }}
                onChange={(useCaseContent: string) => {
                  setUseCase({ name: 'Custom', content: useCaseContent });
                }}
                placeholder={t('components.Builder.addCustomPrompt')}
                value={useCase.content}
              />
            ))
            .otherwise(() => (
              <Styled.MarkdownWrapper>
                <Markdown
                  components={{
                    ol(props) {
                      return (
                        <ol style={{ listStyle: 'decimal', paddingLeft: '1rem' }} {...props} />
                      );
                    },
                    p(props) {
                      return <p style={{ paddingBottom: '8px' }} {...props} />;
                    },
                  }}
                >
                  {useCase.content}
                </Markdown>
              </Styled.MarkdownWrapper>
            ))}
        </Styled.Right>
      </Styled.UseCaseFormContent>
    </Styled.UseCaseForm>
  );
};
