import { Skeleton } from '@floqastinc/flow-ui_core';

import {
  Styled_ContentContainer,
  Styled_List,
} from '../../styles/container.styles';

export default function ConnectionCardSkeleton() {
  return (
    <Styled_ContentContainer>
      <Skeleton variant="rectangle" height={30} width={150} />
      <div style={{ height: '16px' }} />
      <Styled_List style={{ margin: '24px 0' }}>
        <Skeleton variant="rectangle" height={238} />
        <Skeleton variant="rectangle" height={238} />
        <Skeleton variant="rectangle" height={238} />
        <Skeleton variant="rectangle" height={238} />
        <Skeleton variant="rectangle" height={238} />
      </Styled_List>
    </Styled_ContentContainer>
  );
}
