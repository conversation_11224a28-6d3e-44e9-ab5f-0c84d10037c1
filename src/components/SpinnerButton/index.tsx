import { Button, Spinner } from '@floqastinc/flow-ui_core';
import { ComponentProps } from 'react';

type SpinnerButtonProps = {
  children: React.ReactNode;
  /** determines whether the spinner is shown, button will be disabled */
  isPending: boolean;
  /** determines whether the button is disabled. 
   * This is most useful if another mutation should disable this button, 
   * but not cause a loading state */
  disabled: boolean;
} & Omit<ComponentProps<typeof Button>, 'children'>;
export const SpinnerButton = ({ children, isPending, disabled, ...restProps }: SpinnerButtonProps) => {
  return (
    <Button disabled={isPending || disabled} {...restProps}>
      {isPending ? <Spinner style={{ position: 'absolute' }} color="success" size={32} /> : null}
      {children}
    </Button>
  );
};
