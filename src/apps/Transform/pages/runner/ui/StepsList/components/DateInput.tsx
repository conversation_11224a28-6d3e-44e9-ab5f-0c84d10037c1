import { Text } from '@floqastinc/flow-ui_core';
import { Time } from '@/components/Time';

type DateInputProps = {
  label: string;
  date: Date;
};

export function DateInput({ label, date }: DateInputProps) {
  return (
    <>
      <Text color={'readonly'} weight={6} size={3}>
        {label}
      </Text>
      <Text size={3} truncate={true}>
        <Time value={new Date(date)} />
      </Text>
    </>
  );
}
