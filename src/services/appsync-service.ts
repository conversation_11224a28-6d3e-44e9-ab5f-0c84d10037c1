 
import v3 from '@/services/v3';
import { AppsyncAuthentication } from '@floqastinc/transform-v3';

export interface AppService<T> {
  subscriptionId: string | null;
  handleMessageData: (message: T) => void;
  connect(params: { handleMessageData: (message: T) => void }): Promise<() => void>;
  disconnect(): void;
}
export class AppSyncService<T> implements AppService<T> {
  private ws: WebSocket | null = null;
  subscriptionId: string | null = null;
  private connectInterval: NodeJS.Timeout | null = null;
  private appsyncAuthentication: AppsyncAuthentication | null = null;
  private isConnecting: boolean = false;
  private connectionState: 'disconnected' | 'connecting' | 'connected' | 'authenticated' =
    'disconnected';

  handleMessageData: (message: T) => void = () => {
    throw new Error('handleMessageData not defined');
  };

  constructor(
    private params: {
      workflowId: string;
      taskId: string;
      exampleSetId: string;
    },
    private disconnectBeforeReconnect: boolean = false,
  ) {}

  get isConnected(): boolean {
    return this.connectionState === 'connected' || this.connectionState === 'authenticated';
  }

  get isAuthenticated(): boolean {
    return this.connectionState === 'authenticated';
  }

  private async getAppsyncAuthenticationData() {
    const data = (
      await v3.appsyncAuthentication.getAppsyncAuthentication({
        workflowId: this.params.workflowId,
        taskId: this.params.taskId,
        exampleSetId: this.params.exampleSetId,
      })
    ).data;
    if (!data) {
      throw new Error(
        JSON.stringify({
          event: 'AppSync.getWebSocketHeaders',
          details: {
            message: 'No authentication data fetched',
          },
        }),
      );
    }
    this.appsyncAuthentication = data;
  }

  private async getWebsocket(): Promise<WebSocket> {
    if (!this.appsyncAuthentication) {
      throw new Error(
        JSON.stringify({
          event: 'AppSync.getWebSocketHeaders',
          details: {
            message: 'No authentication data fetched',
          },
        }),
      );
    }
    const { webSocketUrl, base64EncodedHeaders } = this.appsyncAuthentication;
    const ws = new WebSocket(webSocketUrl, [
      'aws-appsync-event-ws',
      `header-${base64EncodedHeaders}`,
    ]);
    return ws;
  }

  async connect(params: {
    handleMessageData: (message: T) => void;
    reconnectIntervalMs?: number;
  }): Promise<() => void> {
    // Prevent multiple simultaneous connection attempts
    if (this.isConnecting) {
      return () => this.disconnect();
    }

    // Don't reconnect if already authenticated
    if (this.isAuthenticated) {
      this.handleMessageData = params.handleMessageData;
      return () => this.disconnect();
    }

    this.handleMessageData = params.handleMessageData;
    await this.handleConnect();

    if (params.reconnectIntervalMs) {
      this.connectInterval = setInterval(
        async () => await this.handleConnect(),
        params.reconnectIntervalMs,
      );
    }
    return () => this.disconnect();
  }

  private async handleConnect() {
    // Prevent multiple connection attempts if one is in-flight
    if (this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    this.connectionState = 'connecting';

    try {
      await this.getAppsyncAuthenticationData();
      if (!this.appsyncAuthentication) {
        throw new Error(
          JSON.stringify({
            event: 'AppSync.handleConnect',
            details: {
              message: 'No authentication data fetched',
            },
          }),
        );
      }

      if (this.disconnectBeforeReconnect && this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.handleDisconnect();
      }

      this.ws = await this.getWebsocket();
      this.ws.onopen = () => this.handleOpen();
      this.ws.onclose = () => this.handleClose();
      this.ws.onerror = (error) => this.handleError(error);
      this.ws.onmessage = (data) => this.handleMessage(data);

      this.connectionState = 'connected';
    } catch (error) {
      this.connectionState = 'disconnected';
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  disconnect() {
    if (this.connectInterval) {
      clearInterval(this.connectInterval);
      this.connectInterval = null;
    }
    this.handleDisconnect();
  }

  private handleDisconnect() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.subscriptionId = null;
      this.handleMessageData = () => {
        throw new Error('handleMessageData not defined');
      };
      this.appsyncAuthentication = null;
      this.ws.close(1000, 'Closing connection.');
      this.ws = null;
      this.connectionState = 'disconnected';
    } else {
      console.trace('No active WebSocket connection to close.');
    }
  }

  private handleMessage(data: any) {
    const message = JSON.parse(data.data) as {
      type: string;
      [key: string]: any;
    };

    switch (message.type) {
      case 'connection_ack':
        this.handleMessageConnectionAcknowledge(message);
        break;
      case 'data':
      this.handleAppSyncMessageData(message);
        break;
      case 'error':
        this.handleMessageError(message);
        break;
      case 'connection_error':
        console.error('connection error:', message);
        break;
      case 'subscribe_success':
        console.log('Subscription success:', message);
        this.connectionState = 'authenticated'; // Mark as fully authenticated
        break;
      case 'unsubscribe_success':
        console.trace('Unsubscription success:', message);
        break;
      case 'ka':
        console.trace('Keep-alive message received:', message);
        break;
      default:
        console.warn('Unknown message type:', message);
    }
  }

  private handleOpen() {
    this.ws?.send(JSON.stringify({ type: 'connection_init' }));
    console.trace('WebSocket connection established.');
  }

  private handleClose() {
    this.handleDisconnect();
    console.trace('WebSocket connection closed.');
  }

  private handleAppSyncMessageData(message: any) {
    const messageData: T = (JSON.parse(message.event) as any).data;
    console.trace('Message Data: ', messageData);
    this.handleMessageData(messageData);
  }

  private handleError(error: any) {
    console.error('WebSocket error:', error);
    this.connectionState = 'disconnected';
  }

  private async subscribe() {
    if (!this.appsyncAuthentication) {
      throw new Error(
        JSON.stringify({
          event: 'AppSyncService.subscribe',
          details: {
            message: 'No authentication data fetched',
          },
        }),
      );
    }
    const { channel, headers } = this.appsyncAuthentication;
    if (this.subscriptionId) {
      console.trace('Already subscribed.');
    }
    if (!this.ws) {
      throw new Error(
        JSON.stringify({
          event: 'AppSyncService.subscribe',
          details: {
            message: 'Web Socket is not defined',
          },
        }),
      );
    }
    this.subscriptionId = window.crypto.randomUUID();
    this.ws.send(
      JSON.stringify({
        type: 'subscribe',
        id: this.subscriptionId,
        channel,
        authorization: {
          ...{
            host: headers.host,
            Authorization: headers.authorization,
          },
          'x-amz-user-agent': 'aws-amplify/6.9.0 framework/0',
        },
      }),
    );
    console.trace('Subscription message sent.');
  }

  private unsubscribe() {
    if (!this.subscriptionId) {
      throw new Error('Not subscribed.');
    }
    this.ws?.send(
      JSON.stringify({
        type: 'unsubscribe',
        id: this.subscriptionId,
      }),
    );
    console.trace('Unsubscription message sent.');
    this.subscriptionId = null;
  }

  private handleMessageConnectionAcknowledge(message: any) {
    console.trace('Connection acknowledged by server:', message);
    this.subscribe();
  }

  private handleMessageError(message: any) {
    console.error('Message error:', message);
  }
}
