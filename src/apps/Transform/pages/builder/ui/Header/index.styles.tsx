import { Button, DropdownButton as BaseDropdownButton } from "@floqastinc/flow-ui_core";
import styled from "styled-components";

export const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

export const HeaderLeft = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

export const HeaderRight = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const HeaderIconButton = styled(Button)`
  height: 40px;
  min-width: 40px;
  max-width: 40px;
  min-height: 40px;
  max-height: 40px;
  width: 40px;
  padding: unset;
`;

export const VersionSelectorButton = styled(Button)`
  height: 40px;
  min-height: 40px;
  max-height: 40px;
  min-width: 40px;
`;

export const VersionSelector = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

export const VersionIdentifier = styled.span`
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--flo-sem-color-text-body-tertiary);
  background-color: var(--flo-base-color-neutral-200);
  padding: 4px 6px;
  border-radius: 8px;
`;

export const DropdownButton = styled(BaseDropdownButton)`
  background-color: var(--flo-sem-color-primary-default);
  box-shadow: none;
  boarder-color: transparent;
  color: var(--flo-sem-color-surface-neutral-base);

  /* Override the ButtonText component's color */
  & > div {
    color: var(--flo-sem-color-surface-neutral-base);
    text-transform: lowercase;
  }

  & > div::first-letter {
    text-transform: uppercase;
  }

  /* Override the dropdown arrow color */
  & svg {
    color: var(--flo-sem-color-surface-neutral-base);
    fill: var(--flo-sem-color-surface-neutral-base);
  }
`;
