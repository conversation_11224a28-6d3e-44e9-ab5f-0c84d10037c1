import { useEffect, useRef } from 'react';

/**
 * Custom hook for debouncing a function.
 *
 * @param callback The function to debounce.
 * @param delay The delay in milliseconds before invoking the callback.
 * @returns A debounced function.
 */
export const useDebounce = () => {
  const timeoutRef = useRef<number | undefined>(null);

  // Debounce function
  const debounce = <T extends (...args: any[]) => any>(
    callback: T,
    delay: number,
  ) => {
    return (...args: Parameters<T>) => {
      // Clear the existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set a new timeout
      timeoutRef.current = window.setTimeout(() => {
        callback(...args);
      }, delay);
    };
  };

  // Cleanup the timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debounce;
};