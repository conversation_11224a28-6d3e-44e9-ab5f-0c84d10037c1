import { useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { t } from '@/utils/i18n';
import styled from 'styled-components';
import {
  Button,
  DropdownPanel,
  DropdownButton,
  CalendarSelectBox,
  Input,
} from '@floqastinc/flow-ui_core';
import DateRange from '@floqastinc/flow-ui_icons/material/DateRange';
import { useModal } from '@/components';
import { Overlay, ModalWrapper, ModalHeader, Form, TextInput } from './styled';
import { ALLOWED_FILE_TYPES } from '@/constants';

const ButtonText = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--flo-base-font-family-1);
  font-size: var(--flo-base-font-size-3);
`;

export const AddInputModal = ({ onSave }) => {
  const [name, setName] = useState('');
  const [type, setType] = useState('');
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const today = new Date();
  const [value, setValue] = useState('');
  const [activePeriod, setActivePeriod] = useState({
    month: today.getMonth() + 1,
    year: today.getFullYear(),
  });

  const { closeModal } = useModal();

  const clearPropsAndClose = () => {
    setName('');
    setType('');
    setValue('');
    closeModal();
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    onSave({
      name,
      type,
      value,
    });
    clearPropsAndClose();
  };

  const typesByValue = {
    FILE: 'File',
    DATETIME: 'Date',
    TEXT: 'Text',
  };

  const UploadFileButton = () => {
    const fileInputRef = useRef(null);

    const handleButtonClick = () => {
      fileInputRef.current.click();
    };

    const handleFileChange = (event) => {
      const file = event.target.files[0];
      if (file) {
        onSave({ description: file.name, name, value: file, type });
      }
      clearPropsAndClose();
    };

    return (
      <>
        <input
          type="file"
          ref={fileInputRef}
          style={{ display: 'none' }}
          onChange={handleFileChange}
          accept={ALLOWED_FILE_TYPES.join(',')}
        />
        <Button type="button" disabled={!name} onClick={handleButtonClick}>
          {t('builder.uploadFile')}
        </Button>
      </>
    );
  };

  return (
    <Overlay onClick={closeModal}>
      <ModalWrapper onClick={(e) => e.stopPropagation()}>
        <ModalHeader>{t('components.Builder.addInput')}</ModalHeader>
        <Form onSubmit={handleSubmit}>
          <Input
            isRequired
            onChange={(e) => setName(e)}
            placeholder={type === 'FILE' ? 'File Description' : 'Input Name'}
            value={name}
          />
          <DropdownPanel
            isOpen={isTypeDropdownOpen}
            disableClear
            disableFilter
            onOpenChange={setIsTypeDropdownOpen}
            onChange={(val) => {
              setIsTypeDropdownOpen(false);
              setValue('');
              setType(val);
            }}
            selectionMode="single"
            selectedValues={type}
          >
            <DropdownPanel.Trigger>
              <DropdownButton isOpen={isTypeDropdownOpen}>
                {typesByValue[type] || t('components.Builder.selectType')}
              </DropdownButton>
            </DropdownPanel.Trigger>
            <DropdownPanel.Content style={{ zIndex: '9999' }}>
              <DropdownPanel.Option value="FILE">{typesByValue['FILE']}</DropdownPanel.Option>
              <DropdownPanel.Option value="DATETIME">
                {typesByValue['DATETIME']}
              </DropdownPanel.Option>
              <DropdownPanel.Option value="TEXT">{typesByValue['TEXT']}</DropdownPanel.Option>
            </DropdownPanel.Content>
          </DropdownPanel>
          {type === 'DATETIME' && (
            <CalendarSelectBox
              value={value}
              activePeriod={activePeriod}
              onChange={(val) => {
                setValue(val);
                setIsCalendarOpen(false);
              }}
              onMonthChange={setActivePeriod}
              onOpenChange={setIsCalendarOpen}
              open={isCalendarOpen}
              trigger={
                <DropdownButton open={open} onClick={() => {}}>
                  <ButtonText>
                    <DateRange
                      color="var(--flo-sem-color-icon-primary)"
                      style={{ opacity: 0.8 }}
                      size={20}
                    />
                    {value || 'Select a date'}
                  </ButtonText>
                </DropdownButton>
              }
              styleOverrides={{
                content: {
                  zIndex: '9999',
                },
              }}
            />
          )}
          {type === 'TEXT' && (
            <Input
              aria-label="text-input"
              placeholder="Add Input"
              value={value}
              onChange={setValue}
            />
          )}
          {type === 'FILE' ? (
            <UploadFileButton />
          ) : (
            <Button type="submit" disabled={!name || !type || (type && !value)}>
              {t('generics.save')}
            </Button>
          )}
          <Button type="button" color="dark" variant="outlined" onClick={closeModal}>
            {t('generics.cancel')}
          </Button>
        </Form>
      </ModalWrapper>
    </Overlay>
  );
};

AddInputModal.propTypes = {
  onSave: PropTypes.func.isRequired,
};
