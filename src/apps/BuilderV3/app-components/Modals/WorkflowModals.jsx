import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { t } from '@/utils/i18n';
import { useModal } from '@/components';
import {
  Overlay,
  ModalWrapper,
  ModalHeader,
  Form,
  TextInput,
  Button,
  ExperimentGroup,
  ExperimentRow,
} from './styled';
import { TextArea, Text, Heading, DropdownPanel, DropdownButton } from '@floqastinc/flow-ui_core';
import { AGENT } from '@/constants';
import { EntityInputDropdown } from '../EntityInputDropdown/EntityInputDropdown';
import v3 from '@/services/v3';
import { useFeatureFlags } from '@/components/FeatureFlag';
import { Modes } from '@Transform/components/SettingsSideDrawer/ui/Modes';
import { Experiments } from '@Transform/components/Experiments';
import {
  useCreateOrUpdateExperimentAssignment,
  useExperimentsForWorkflow,
} from '@BuilderV3/api/experiments';

export const AddWorkflowModal = ({ onSave }) => {
  return <WorkflowFormModal title={t('components.Builder.createNewWorkflow')} onSave={onSave} />;
};

export const EditWorkflowModal = ({ onSave, workflow }) => {
  return (
    <WorkflowFormModal
      title={t('components.Builder.editAgent', { AGENT })}
      onSave={onSave}
      workflow={workflow}
    />
  );
};

const WorkflowFormModal = ({ onSave, title, workflow }) => {
  const [name, setName] = useState(workflow?.name || '');
  const [entity, setEntity] = useState(workflow?.entity || '');
  const [description, setdescription] = useState(workflow?.description || '');
  const [experimentAssignments, setExperimentAssignments] = useState({});
  const [fullAssignments, setFullAssignments] = useState({});
  const [initialAssignments, setInitialAssignments] = useState({});
  const [settings, setSettings] = useState(workflow?.settings);
  const { closeModal } = useModal();

  // Feature flags
  const { getFlag } = useFeatureFlags();
  const transformExperimentsEnabled = getFlag('enable-experiments', false);
  const { data: currentExperimentAssignments } = useExperimentsForWorkflow(
    transformExperimentsEnabled,
    workflow?.id,
  );
  useEffect(() => {
    // Get available experiments and display currently selected experiment assignments for workflow
    if (!transformExperimentsEnabled) return; // Do nothing if we don't have experiments enabled

    const assignments = {};
    const fullAssignmentMap = {};

    if (Array.isArray(currentExperimentAssignments)) {
      currentExperimentAssignments.forEach((assignment) => {
        assignments[assignment.experimentName] = assignment.variantId;
        fullAssignmentMap[assignment.experimentName] = assignment; // includes assignment.id --> variantId for PATCH request
      });
    }

    setInitialAssignments(assignments);
    setExperimentAssignments(assignments);
    setFullAssignments(fullAssignmentMap);
  }, [workflow?.id, currentExperimentAssignments, transformExperimentsEnabled]);

  // Diffing function used for tracking if a user edited their existing experiments
  const getChangedAssignments = () => {
    const changes = [];

    for (const [experimentName, variantId] of Object.entries(experimentAssignments)) {
      const originalVariant = initialAssignments[experimentName];
      const assignment = fullAssignments[experimentName];

      // if original is undefined, we still want to send the assignment to the API
      if (variantId !== originalVariant) {
        changes.push({
          assignmentId: assignment?.id,
          experimentName,
          variantId,
        });
      }
    }

    return changes;
  };

  const workflowExperimentMutation = useCreateOrUpdateExperimentAssignment(workflow.id);

  const handleSubmit = async (event) => {
    event.preventDefault();

    const updatedWorkflow = workflow
      ? {
          name,
          description,
          entityId: entity.id,
          id: workflow.id,
          type: 'BUILDER',
          status: workflow.status || 'DRAFT',
          settings,
        }
      : {
          name,
          description,
          type: 'BUILDER',
          settings,
        };

    await onSave(updatedWorkflow);

    // Diff existing assignments to see if we should call API
    if (transformExperimentsEnabled) {
      workflowExperimentMutation.mutate(getChangedAssignments());
    }

    setName('');
    setdescription('');
    closeModal();
  };

  const handleSettings = (name, value) => {
    setSettings({ ...settings, [name]: value });
  };

  const onExperimentAssignmentsChange = (experimentName, variantId) => {
    setExperimentAssignments((prev) => ({
      ...prev,
      [experimentName]: variantId,
    }));
  };

  return (
    <Overlay onClick={closeModal}>
      <ModalWrapper onClick={(e) => e.stopPropagation()}>
        <ModalHeader>{title}</ModalHeader>
        <Form onSubmit={handleSubmit}>
          <TextInput
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder={t('components.Builder.agentNamePlaceholder', { AGENT })}
            required
            autoFocus
          />
          <EntityInputDropdown entityId={workflow.entityId} setEntity={setEntity} />
          <TextArea
            type="text"
            value={description}
            onChange={(e) => setdescription(e)}
            placeholder={t('components.Builder.agentDescriptionOptional', { AGENT })}
            aria-label={t('components.Builder.agentDescriptionOptional', { AGENT })}
            styleOverrides={{ textarea: { width: '100%' } }}
          />
          <Modes settings={settings} setSettings={handleSettings} />
          <Experiments onChange={onExperimentAssignmentsChange} workflowId={workflow.id} />
          <Button type="submit" $primary>
            {t('generics.save')}
          </Button>
          <Button type="button" onClick={closeModal}>
            {t('generics.cancel')}
          </Button>
        </Form>
      </ModalWrapper>
    </Overlay>
  );
};

WorkflowFormModal.propTypes = {
  title: PropTypes.string.isRequired,
  onSave: PropTypes.func.isRequired,
  workflow: PropTypes.object,
};

AddWorkflowModal.propTypes = {
  onSave: PropTypes.func.isRequired,
};

EditWorkflowModal.propTypes = {
  onSave: PropTypes.func.isRequired,
  workflow: PropTypes.object.isRequired,
};
