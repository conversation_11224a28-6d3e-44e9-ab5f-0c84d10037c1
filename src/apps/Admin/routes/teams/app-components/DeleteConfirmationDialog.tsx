import React from 'react';
import { Dialog } from '@floqastinc/flow-ui_core';
import { t } from '@/utils/i18n';

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose} type="danger">
      <Dialog.Header>{t('components.Admin.deleteTeam')}</Dialog.Header>
      <Dialog.Body>
        <p>{t('components.Admin.actionPermanentDeleteTeam')}</p>
      </Dialog.Body>
      <Dialog.Footer>
        <Dialog.FooterCancelBtn onClick={onClose}>{t('generics.cancel')}</Dialog.FooterCancelBtn>
        <Dialog.FooterActionBtn color="danger" onClick={onConfirm}>
          {t('generics.delete')}
        </Dialog.FooterActionBtn>
      </Dialog.Footer>
    </Dialog>
  );
};

export default DeleteConfirmationDialog;
