import { styled } from 'styled-components';

export const BuilderContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  height: 100%;
  max-height: 100%;
  background-color: var(--flo-sem-color-white);
`;

export const BuilderHeader = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--flo-sem-color-border);
  padding: 12px 24px;
  height: 64px;
`;

export const BuilderContent = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  gap: 0;
  overflow: auto;
  scrollbar-width: none;
`;

export const StepsSidebar = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: auto;
  min-width: 64px;
  height: 100%;
  background-color: var(--flo-base-color-neutral-200);
  border-right: 1px solid var(--flo-sem-color-border);
  padding: 8px;
  overflow-y: auto;
  scrollbar-width: none;
`;

export const BuilderSection = styled.div`
  display: flex;
  flex-direction: column;
  width: 28%;
  height: 100%;
  border-right: 1px solid var(--flo-sem-color-border);
  flex-grow: 1;
`;

export const BuilderSectionContainer = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
`;

export const TopSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  border-bottom: 1px solid var(--flo-sem-color-border);
  padding: 12px 16px 0 16px;
`;

export const BuilderSectionHeader = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

export const BuilderSectionActions = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
`;

export const BuilderSectionContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  width: 100%;
  overflow: auto;
`;

export const AddStepButton = styled.button`
  display: flex;
  flex-direction: row;
  align-items: center;
  color: var(--flo-sem-color-muted);
  border-radius: 50%;

  &:hover {
    color: var(--flo-base-color-neutral-500);
    background-color: var(--flo-base-color-neutral-100);
  }
`;

export const Output = styled.div`
  display: flex;
  flex-direction: column;
  width: 67.5%;
  height: 100%;
`;

export const Half = styled.div`
  flex: 1;
`;
