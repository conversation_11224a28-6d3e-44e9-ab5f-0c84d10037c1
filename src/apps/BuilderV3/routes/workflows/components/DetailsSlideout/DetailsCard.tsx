import { t } from '@/utils/i18n';
import { Button, TextArea, Input, Card } from '@floqastinc/flow-ui_core';
import Add from '@floqastinc/flow-ui_icons/material/Add';
import { TaskDescriptionDescription } from '@floqastinc/transform-v3';

export default function DetailsCard({
  taskDescription,
  editable,
  onChange,
}: {
  taskDescription: TaskDescriptionDescription[];
  editable?: boolean;
  onChange: (updatedItems: TaskDescriptionDescription[]) => void;
}) {
  const updateItemsState = (updatedItems: TaskDescriptionDescription[]) => {
    onChange(updatedItems);
  };

  const updateItem = (index: number, field: 'heading' | 'details', value: string) => {
    const updatedItems = [...taskDescription];
    if (!updatedItems[index]) {
      console.error(t('components.Builder.Errors.itemNotFoundIndex'), index);
      return;
    }
    if (!updatedItems[index][field]) {
      console.error(
        t('components.Builder.Errors.forItem1') +
          `${index}` +
          t('components.Builder.Errors.forItem2') +
          `${field}`,
      );
      return;
    }
    updatedItems[index][field] = value || ' ';
    updateItemsState(updatedItems);
  };

  const addItem = () => {
    const updatedItems = [
      ...taskDescription,
      { heading: t('components.Builder.newItem'), details: t('components.Builder.detailsHere') },
    ];
    updateItemsState(updatedItems);
  };

  const deleteItem = (index: number) => {
    const updatedItems = taskDescription.filter((_, i) => i !== index);
    updateItemsState(updatedItems);
  };

  return (
    <div>
      {taskDescription.map((item, index) => (
        <Card className="w-full" key={index} style={{ marginBottom: '1em' }}>
          <Card.Header>
            <Card.Title>
              {editable ? (
                <>
                  <Input
                    label="Title"
                    value={item.heading}
                    onChange={(value: string) => updateItem(index, 'heading', value)}
                  />
                </>
              ) : (
                <div>{item.heading}</div>
              )}
            </Card.Title>
          </Card.Header>
          <Card.Description>
            {editable ? (
              <TextArea
                label="Description"
                isLabelVisible
                value={item.details}
                onChange={(value: string) => updateItem(index, 'details', value)}
                resizable={true}
                styleOverrides={{ textarea: { width: '100%' } }}
              />
            ) : (
              <div>{item.details}</div>
            )}
            {editable && (
              <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  color="danger"
                  variant="outlined"
                  style={{ marginTop: '1em' }}
                  onClick={() => deleteItem(index)}
                >
                  {t('generics.delete')}
                </Button>
              </div>
            )}
          </Card.Description>
        </Card>
      ))}
      {editable && (
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Button color="dark" variant="outlined" onClick={addItem}>
            <Add color="currentColor" />
            {t('components.Builder.addSection')}
          </Button>
        </div>
      )}
    </div>
  );
}
