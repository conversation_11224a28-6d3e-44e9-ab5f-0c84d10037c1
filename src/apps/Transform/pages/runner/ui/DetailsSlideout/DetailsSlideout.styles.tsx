import { Card } from '@floqastinc/flow-ui_core';
import { styled } from 'styled-components';

export const DetailCard = styled(Card)`
  box-sizing: border-box;
  height: 100%;
  overflow: scroll;
  min-width: 500px;
`;

export const DetailHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
`;

export const DetailSteps = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2rem;
`;

export const DetailStep = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

export const DetailStepName = styled.div`
  display: flex;
  background-color: var(--flo-base-color-neutral-200);
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  gap: 0.5rem;
`;

export const DetailStepDescription = styled.div`
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  margin: 0 0 0 1rem;
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 6px;

  font-size: 0.75rem;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: bold;
    font-size: revert;
  }

  & ul,
  & ol {
    list-style: disc inside;
  }

  & p {
    margin: 20px 0;
  }
  *:first-child {
    margin-top: 0;
  }
  *:last-child {
    margin-bottom: 0;
  }
`;

export const GeneratingText = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;
