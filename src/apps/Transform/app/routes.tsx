import { RouteObject } from 'react-router-dom';
import { Suspense } from 'react';
import { ScopeProvider } from 'jotai-scope';
import { ErrorBoundary, ModalProvider } from '@/components';
import { AGENTS, RUNNER, RUNS, V3, CONNECTIONS } from '@/constants';
import { RunnerPage } from '@Transform/pages/runner/RunnerPage';
// TODO: This is a generic root, although there needs to be some cleanup
// with the modals
import { Root } from '@BuilderV3/Root';
import { Loading } from '@/components/Loading';
import { DevPage } from '@Transform/pages/runner/DevPage';
import { ConnectionsPage } from '@Transform/pages/connections/ConnectionsPage';
import { ConnectionsProvider } from '@Transform/pages/connections/ConnectionsProvider';
import { fileAtom, isFileLoadingAtom } from '@Transform/pages/builder/store';

export const routes: RouteObject[] = [
  {
    path: `/${RUNNER}/${V3}`,
    element: (
      <ModalProvider>
        <Root />
      </ModalProvider>
    ),
    errorElement: <ErrorBoundary />,
    children: [
      {
        path: `${AGENTS}/:agentId`,
        element: (
          <ScopeProvider atoms={[fileAtom, isFileLoadingAtom]}>
            <Suspense fallback={<Loading />}>
              <RunnerPage />
            </Suspense>
          </ScopeProvider>
        ),
        errorElement: <ErrorBoundary />,
      },
      {
        path: `${AGENTS}/:agentId/${RUNS}/:runId`,
        element: (
          <ScopeProvider atoms={[fileAtom, isFileLoadingAtom]}>
            <Suspense fallback={<Loading />}>
              <RunnerPage />
            </Suspense>
          </ScopeProvider>
        ),
        errorElement: <ErrorBoundary />,
      },
    ],
  },
  {
    path: `/${CONNECTIONS}`,
    element: (
      <ModalProvider>
        <Root />
      </ModalProvider>
    ),
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: (
          <Suspense fallback={<Loading />}>
            <ConnectionsProvider>
              <ConnectionsPage />
            </ConnectionsProvider>
          </Suspense>
        ),
        errorElement: <ErrorBoundary />,
      },
    ],
  },
  {
    path: '/dev',
    element: (
      <ModalProvider>
        <DevPage />
      </ModalProvider>
    ),
  },
];
