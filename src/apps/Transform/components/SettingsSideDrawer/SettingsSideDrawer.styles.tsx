 
import styled from "styled-components";
import { SideDrawer } from "@floqastinc/flow-ui_core";

export const SideDrawerBody = styled(SideDrawer.Body)`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
  height: 100%;
`;

export const SideDrawerFooter = styled(SideDrawer.Footer)`
  justify-content: flex-end;
`;

export const SettingsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
`;

export const LoadingPage = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
  height: 100%;
  align-items: center;
  justify-content: center;
`;
