import {
  RunStatus as TaskRunStatus,
  Task,
  TaskStrategy,
} from '@floqastinc/transform-v3';

type StrategyKind = { kind: TaskStrategy['kind'] };
export type TaskStep = Pick<Task, 'id' | 'name'> & {
  integrations?: string[];
  strategy: StrategyKind;
};
export type TaskRunStep = {
  taskRunId: string;
  status: TaskRunStatus;
} & TaskStep;

export const isTaskRunStep = (step: StepData): step is TaskRunStep => {
  return 'taskRunId' in step && 'status' in step;
};

export type StepData = TaskStep | TaskRunStep;
