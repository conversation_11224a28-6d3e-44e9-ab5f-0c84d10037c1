import { DropdownPanel, DropdownButton } from '@floqastinc/flow-ui_core';
import { useState } from 'react';
import { PanelView } from '../../BuilderPage.store';

interface PanelViewDropdownProps {
  isFloLakeStrategy: boolean;
  selectedView: string;
  setSelectedView: (view: string) => void;
}

export const PanelViewDropdown = ({
  isFloLakeStrategy,
  selectedView,
  setSelectedView,
}: PanelViewDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const handlePanelDropdownChange = (dropdownOptionId: PanelView) => {
    setSelectedView(dropdownOptionId);
    sessionStorage.setItem('panelView', dropdownOptionId);
    setIsOpen(false);
  };

  const panelViewDropdownOptions: { id: string; name: PanelView }[] = [
    { id: 'file-preview', name: 'File' },
    { id: 'steps', name: 'Steps' },
    ...(isFloLakeStrategy ? [{ id: 'inputs', name: 'Inputs' as PanelView }] : []),
  ];

  return (
    <DropdownPanel
      isOpen={isOpen}
      disableClear
      disableFilter
      onOpenChange={setIsOpen}
      onChange={handlePanelDropdownChange}
      selectionMode="single"
      selectedValues={selectedView}
      data-tracking-id="builder-panel-view-dropdown"
    >
      <DropdownPanel.Trigger>
        <DropdownButton label="View" isOpen={isOpen}>
          {selectedView}
        </DropdownButton>
      </DropdownPanel.Trigger>
      <DropdownPanel.Content>
        {panelViewDropdownOptions.map((option) => {
          return (
            <DropdownPanel.Option key={option.id} value={option.name}>
              {option.name}
            </DropdownPanel.Option>
          );
        })}
      </DropdownPanel.Content>
    </DropdownPanel>
  );
};
