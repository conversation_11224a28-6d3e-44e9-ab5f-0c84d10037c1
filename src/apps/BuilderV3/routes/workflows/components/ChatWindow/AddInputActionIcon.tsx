import React from 'react';
import { NewWorkflowInput } from '@floqastinc/transform-v2';
import { IconButton } from '@floqastinc/flow-ui_core';
import Add from '@floqastinc/flow-ui_icons/material/Add';
import { useModal } from '@/components/ModalContext';

type WorkflowInputData = NewWorkflowInput & {
  value: any;
};

type AddInputActionIconProps = {
  disabled?: boolean;
  addInput: (...args: any) => any;
};
export const AddInputActionIcon = ({ disabled, addInput }: AddInputActionIconProps) => {
  const { openModal } = useModal();

  return (
    <IconButton
      onClick={() => {
        openModal('AddInput', {
          onSave: (input: WorkflowInputData) => {
            addInput(input);
          },
        });
      }}
      size="md"
      disabled={disabled}
      data-tracking-id="builder-chat-add-input-action-button"
    >
      <Add />
    </IconButton>
  );
};
