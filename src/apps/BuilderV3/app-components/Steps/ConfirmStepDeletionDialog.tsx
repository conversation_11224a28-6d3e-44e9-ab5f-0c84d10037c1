import { Dialog, Spinner } from '@floqastinc/flow-ui_core';
import { useMutation, useQuery } from '@tanstack/react-query';
import { t } from '@/utils/i18n';
import { useNavigate, useParams } from 'react-router';
import { Task } from '@floqastinc/transform-v3';
import { queryClient } from '@/components/queryClient';
import { deleteTaskMutation, getWorkflowTasksQuery } from '@BuilderV3/api/tasks';
import { formatTaskDate } from '@/utils/date';
import { BUILDER, STEPS, V3, AGENTS } from '@/constants';

type ConfirmStepDeletionDialogProps = {
  isOpen: boolean;
  task: Task;
  onConfirm: () => void;
  onOpenChange: (isOpen: boolean) => void;
};
export const ConfirmStepDeletionDialog = ({
  isOpen,
  task,
  onConfirm,
  onOpenChange,
}: ConfirmStepDeletionDialogProps) => {
  const { workflowId, taskId, exampleSetId } = useParams();
  if (!workflowId || !taskId || !exampleSetId) {
    console.error(t('components.Builder.Errors.noIDsprovided'));
    throw new Error(t('components.Builder.Errors.noIDsprovided'));
  }
  const navigate = useNavigate();

  const tasksQuery = useQuery(getWorkflowTasksQuery(workflowId));
  const tasks = tasksQuery.data ?? [];

  const deleteTask = useMutation({
    mutationKey: ['deleteTask', { workflowId, taskId }],
    mutationFn: deleteTaskMutation,
    onSuccess: (newLastTask) => {
      queryClient.invalidateQueries({
        queryKey: [{ scope: 'workflows', filter: { workflowId } }],
      });

      onOpenChange(false);
      onConfirm();

      if (newLastTask) {
        navigate(`/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${newLastTask.id}`, {
          replace: true,
        });
      }
    },
    onError: (error) => {
      console.error('error', error);
    },
  });

  return (
    <Dialog type="warning" open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Header>{t('components.Builder.deleteStep')}</Dialog.Header>
      <Dialog.Body>
        {t('components.Builder.aboutToDeleteStep', {
          taskName: `${task.name ? ` "${task.name}" ` : ''}`,
          taskCreatedAt: task.createdAt ? formatTaskDate(task.createdAt) : '',
        })}
        {task.createdAt ? '' : t('components.Builder.aPreviousStep')}
        {t('components.Builder.willRemoveAllStepsAfter')}
      </Dialog.Body>
      <Dialog.Footer>
        <Dialog.FooterCancelBtn onClick={() => onOpenChange(false)}>
          {t('generics.cancel')}
        </Dialog.FooterCancelBtn>
        <Dialog.FooterActionBtn
          disabled={deleteTask.isPending || tasksQuery.isPending}
          onClick={() =>
            deleteTask.mutate({
              workflowId,
              taskId: task.id,
              tasks,
            })
          }
        >
          {deleteTask.isPending || tasksQuery.isPending ? (
            <Spinner style={{ position: 'absolute' }} color="success" size={32} />
          ) : null}
          {t('components.Builder.deleteStep')}
        </Dialog.FooterActionBtn>
      </Dialog.Footer>
    </Dialog>
  );
};
