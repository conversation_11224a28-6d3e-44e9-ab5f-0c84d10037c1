/* eslint-disable no-restricted-syntax, i18next/no-literal-string */
const { PROXY_ENV, isDevelopment, isStandalone } = require("./env");

const extLibs = [
  "react-18",
  "react-dom-18",
  "react-router-dom-6",
  "react-is-18",
  "pusher-js",
];
const fqFEs = ["@floqast/client-data-hub", "@floqast/header-nav"];
const fqLibraryNfes = { "@floqastinc/fq-intl": "@floqast/fq-intl-nfe" };

const getLocalDevPath = (mfeName) => {
  if (!isDevelopment) return null;

  const envKey = `REACT_APP_${mfeName.replace(/-/g, "_").toUpperCase()}_URL`;
  const assetsUrl = process.env[envKey];
  return assetsUrl ? `${assetsUrl}/main.hub.js` : null;
};

const getFqEnvUrl = (fqMfeName) => {
  const mfeName = fqMfeName.replace("@floqast/", "");
  return new URL(
    getLocalDevPath(mfeName) ||
      `https://services-${PROXY_ENV}.floqast.engineering/${mfeName}/main.hub.js`,
  );
};

const getExternals = () => {
  const externals = [...extLibs, ...fqFEs].reduce((acc, val) => {
    acc[val] = val;
    return acc;
  }, {});

  if (!isStandalone) {
    Object.assign(externals, fqLibraryNfes);
  }
  return externals;
};

const getStandaloneImportMap = () => {
  return {
    imports: Object.fromEntries(fqFEs.map((val) => [val, getFqEnvUrl(val)])),
  };
};

module.exports = {
  getExternals,
  getStandaloneImportMap,
};
