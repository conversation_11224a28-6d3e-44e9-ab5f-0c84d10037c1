import { TabGroup, Tab, Spinner } from '@floqastinc/flow-ui_core';
import { useParams } from 'react-router-dom';
import { t } from '@/utils/i18n';
import { match, P } from 'ts-pattern';
import { TaskStrategy } from '@floqastinc/transform-v3';
import { useTaskInputs } from '@v3/task-inputs';

export type BuilderSectionTab = 'build' | 'inputs' | 'versions' | 'details';

type BuilderSectionTabsProps = {
  value: BuilderSectionTab;
  onValueChange: (value: BuilderSectionTab) => void;
  strategy: TaskStrategy['kind'] | undefined;
};
export const BuilderSectionTabs = ({ value, onValueChange, strategy }: BuilderSectionTabsProps) => {
  const { workflowId = '', taskId = '' } = useParams();

  const { data: taskInputs, isPending } = useTaskInputs({
    workflowId,
    taskId,
  });

  // Filter out previous task output which has name ending in "Output"
  const filteredTaskInputs = taskInputs?.filter((input) => !input.name?.endsWith('Output')) || [];

  const inputsTab = match({ isPending, taskInputs })
    .with({ isPending: true }, () => (
      <Tab
        tabId="inputs"
        title={
          <button type="button">
            {t('generics.inputs')} <Spinner color="info" size={12} />
          </button>
        }
      />
    ))
    .with({ taskInputs: [] }, () => <Tab tabId="inputs" title={t('generics.inputs')} />)
    .with({ taskInputs: P.nullish }, () => <Tab tabId="inputs" title={t('generics.inputs')} />)
    .otherwise(() => (
      <Tab tabId="inputs" title={`${t('generics.inputs')} (${filteredTaskInputs.length})`} />
    ));

  const tabs = match(strategy)
    .with('REVIEW', () => [<Tab tabId="details" title={t('generics.details')} key="details" />])
    .otherwise(() => [
      <Tab tabId="build" title={t('generics.build')} key="build" />,
      strategy !== 'FLOLAKE' && inputsTab,
      <Tab tabId="versions" title={t('generics.versions')} key="versions" />,
      <Tab tabId="details" title={t('generics.details')} key="details" />,
    ]);

  return (
    <TabGroup defaultValue="build" value={value} onValueChange={onValueChange}>
      {tabs}
    </TabGroup>
  );
};
