import { t } from '@/utils/i18n';
import { Dialog } from '@floqastinc/flow-ui_core';
import { ExampleSetStatus, Task } from '@floqastinc/transform-v3';
import { useMutation } from '@tanstack/react-query';
import { useAtom } from 'jotai';
import { useParams } from 'react-router-dom';
import { match } from 'ts-pattern';
import { shouldRefetchTasksAtom } from '../../BuilderPage.store';
import { statusChangeAtom } from './store';
import { queryClient } from '@/components/queryClient';
import { queryKeys } from '@BuilderV3/api/query-keys';
import { capitalize } from '@/utils/string';
import v3 from '@/services/v3';
import { AGENT } from '@/constants';
import { getIsChatBasedStrategy } from '@/utils/strategy';

type StatusChangeDialogProps = {
  open: boolean;
  onOpenChange: (isOpen: boolean) => void;
  currentTask?: Task;
};
export const StatusChangeDialog = ({
  open,
  onOpenChange,
  currentTask,
}: StatusChangeDialogProps) => {
  const { workflowId, taskId } = useParams();
  if (!workflowId || !taskId) {
    console.error(t('components.Builder.Errors.noIDsprovided'));
    throw new Error(t('components.Builder.Errors.noIDsprovided'));
  }

  const [statusChange, setStatusChange] = useAtom(statusChangeAtom);

  const [_, setRefetchTasksOverride] = useAtom(shouldRefetchTasksAtom);
  const updateExampleMutation = useMutation({
    mutationFn: async ({
      exampleSetId,
      status,
    }: {
      exampleSetId: string;
      status: ExampleSetStatus;
    }) => {
      // 1. Fetch the current example by ID
      const { data: example } = await v3.examples.getExample({
        workflowId,
        taskId,
        exampleSetId,
      });

      // 2. Check if it's a flolake strategy
      const isFlolake = example?.strategy?.kind === 'FLOLAKE';
      if (!currentTask) {
        console.error(t('components.Builder.Errors.taskNotDefined'));
        return;
      }

      // 3. If so, update the task to use the example's strategy
      if (isFlolake && status === 'ACTIVE') {
        // Update the task with the example's strategy
        const task = {
          ...currentTask,
          strategy: {
            ...example.strategy,
          },
        };

        await v3.tasks.updateTask({
          workflowId,
          taskId,
          task,
        });
      }

      return v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId,
        example: {
          status,
        },
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExamples({
          workflowId,
          taskId,
        }),
      });
      if (getIsChatBasedStrategy(data.data?.strategy) && data.data?.status === 'ACTIVE') {
        setRefetchTasksOverride(true);
        queryClient.invalidateQueries({
          queryKey: queryKeys.tasks.byWorkflow(workflowId),
        });
      }
      if (data?.data?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.examples.getExample({
            workflowId,
            taskId,
            exampleSetId: data?.data?.id,
          }),
        });
      }
      setStatusChange({
        fromStatus: null,
        toStatus: null,
        exampleSetId: null,
      });
    },
  });

  const bodyText = match(statusChange)
    .with({ fromStatus: 'ACTIVE', toStatus: 'DRAFT' }, () => t('components.Builder.statusChange1'))
    .with({ fromStatus: 'DRAFT', toStatus: 'ACTIVE' }, () => t('components.Builder.statusChange2'))
    .with({ fromStatus: 'ARCHIVED', toStatus: 'ACTIVE' }, () =>
      t('components.Builder.statusChange3'),
    )
    .with({ fromStatus: 'ARCHIVED', toStatus: 'DRAFT' }, () =>
      t('components.Builder.statusChange4'),
    )
    .with({ fromStatus: 'DRAFT', toStatus: 'ARCHIVED' }, () =>
      t('components.Builder.statusChange5'),
    )
    .with({ fromStatus: 'ACTIVE', toStatus: 'ARCHIVED' }, () =>
      t('components.Builder.statusChange6'),
    )
    .with({ fromStatus: 'ACTIVE', toStatus: 'PUBLISHED' }, () =>
      t('components.Builder.statusChange7'),
    )
    .otherwise(() => '');

  return statusChange.fromStatus && statusChange.toStatus && statusChange.exampleSetId ? (
    <Dialog open={open} onOpenChange={onOpenChange} type="warning">
      <Dialog.Header>
        {t('components.Builder.changeFrom')} {capitalize(statusChange.fromStatus)}{' '}
        {t('generics.to')} {capitalize(statusChange.toStatus)}
      </Dialog.Header>
      <Dialog.Body>{bodyText}</Dialog.Body>
      <Dialog.Footer>
        <Dialog.FooterCancelBtn
          onClick={() => {
            setStatusChange({
              fromStatus: null,
              toStatus: null,
              exampleSetId: null,
            });
          }}
        >
          {t('generics.cancel')}
        </Dialog.FooterCancelBtn>
        <Dialog.FooterActionBtn
          onClick={() => {
            if (statusChange.toStatus && statusChange.exampleSetId) {
              updateExampleMutation.mutate({
                exampleSetId: statusChange.exampleSetId,
                status: statusChange.toStatus,
              });
            }
          }}
        >
          {t('generics.proceed')}
        </Dialog.FooterActionBtn>
      </Dialog.Footer>
    </Dialog>
  ) : null;
};
