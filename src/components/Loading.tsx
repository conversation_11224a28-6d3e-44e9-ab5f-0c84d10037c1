import React from 'react';
import { Spinner, Text } from '@floqastinc/flow-ui_core';
import { styled } from 'styled-components';

const GenericCenteredDiv = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
  height: 100%;
  width: 100%;
`;

const StyledText = styled(Text)`
  margin-top: 16px;
  text-align: center;
  max-height: fit-content;
`;

const OverlayCenteredDiv = styled.div`
  display: grid;
  place-items: center;
  width: 100%;
  height: 100%;
  z-index: 9999;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(240, 240, 240, 0.5);
`;

interface LoadingProps {
  isLoaded?: boolean;
  children?: React.ReactNode;
  size?: number;
  text?: string;
}

export const Loading: React.FC<LoadingProps> = ({ isLoaded, children, size, text }) => {
  return (
    <>
      {isLoaded && children}
      {!isLoaded && (
        <GenericCenteredDiv>
          <Spinner aria-label="Loading Spinner" color="success" size={size ?? 48} />
          {text && (
            <StyledText weight={6} size={6} lineHeight={6}>
              {text}
            </StyledText>
          )}
        </GenericCenteredDiv>
      )}
    </>
  );
};

export const OverlayLoading = () => {
  return (
    <OverlayCenteredDiv>
      <Spinner color="success" size={48} />
    </OverlayCenteredDiv>
  );
};
