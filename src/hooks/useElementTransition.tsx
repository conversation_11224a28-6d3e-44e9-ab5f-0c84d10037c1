import { useCallback, useEffect, useRef, useState } from 'react';
import { isEqual } from 'es-toolkit';

type TransitionState<T> = {
  // Currently selected value
  value: T | null;
  // State of transitioned element (e.g. a toggle, or a slider, etc.)
  isActive: boolean;
  // CSS transition state
  isTransitioning: boolean;
};

/**
 * A `useState`-like hook for tracking state through a CSS transition.
 *
 * *Context*: CSS transitions take time and work differently than React's built-in
 * model of state. For React, state changes tend to happen instantaneously. If state
 * is changed in React while a CSS transition is happening, the content in the div may
 * disappear or change during the middle of the transition, giving an awkward and choppy UX.
 * This hook adds additional mechanisms to delay setting the state until after the React built-in
 * `onTransitionEnd` attribute prop function is called, bridging the timing gap between React
 * and CSS UI transitions.
 *
 * @example
 * ```
 * const MyChangingDiv = {
 *   const [
 *     value,
 *     setValue,
 *     { isTransitioning, isActive, pendingValue, onTransitionEnd }
 *   ] = useElementTransitionState('initial');
 *
 *   return (
 *     <>
 *     <div
 *       class="bg-blue-500 transition duration-300 hover:bg-green-500"
 *       onTransitionEnd={onTransitionEnd}
 *     >
 *       {value}
 *     </div>
 *     <button onClick={() => setValue('new value')}>Click me, then hover the div</button>
 *   </>
 *   )
 * }
 * ```
 *
 * One thing to note from the example above is that state changes aren't triggered _until_
 * a CSS transition is triggered. That is, clicking on the button will not set the new value
 * until `onTransitionEnd` is called, which will only be called when the div is hovered. Thus,
 * theoretically, you could click the button, wait for 5 years, then hover the div, and _only then_
 * would the state change.
 */
export const useElementTransitionState = <T,>(
  initialValue: T | null = null,
): [
  TransitionState<T>['value'],
  (value: T | null) => void,
  {
    isActive: TransitionState<T>['isActive'];
    isTransitioning: TransitionState<T>['isTransitioning'];
    onTransitionEnd: () => void;
  },
] => {
  const isMounted = useRef(true);

  const [state, setState] = useState<TransitionState<T>>({
    value: initialValue,
    isActive: initialValue !== null,
    isTransitioning: false,
  });

  /**
   * Handler for when a transition completes. Flushes the pending state
   * to be the active state once the (CSS) transition completes.
   */
  const onTransitionEnd = useCallback(() => {
    if (!isMounted.current) return;

    setState((prevState) => {
      return {
        ...prevState,
        isTransitioning: false,
      };
    });
  }, []);

  const setValue = useCallback((value: T | null) => {
    setState((prevState) => {
      // Collapse the slideout only if its active
      if (value === null) {
        if (prevState.isActive) {
          return {
            ...prevState,
            isActive: false,
            isTransitioning: true,
          };
        }
        return prevState;
      }
      // If double click, close
      if (isEqual(value, prevState.value) && prevState.isActive) {
        // Then just set the element as inactive (i.e. as a toggle)
        return {
          ...prevState,
          isActive: false,
          isTransitioning: true,
        };
      }

      // Otherwise, be sure its open
      return {
        ...prevState,
        value,
        isActive: true,
      };
    });
  }, []);

  // Clean up hook on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  return [
    state.value,
    setValue,
    {
      isActive: state.isActive,
      isTransitioning: state.isTransitioning,
      onTransitionEnd,
    },
  ];
};
