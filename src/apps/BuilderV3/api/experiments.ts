import { Experiment } from '@floqastinc/transform-v3';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import v3, { ApiError } from '@/services/v3';
import { queryKeys } from './query-keys';

export const useExperiments = (transformExperimentsEnabled: boolean) =>
  useQuery<Experiment[], Error>({
    queryKey: [queryKeys.experiments.resource],
    queryFn: async () => {
      const response = await v3.experiments.getExperiments();
      if (response.errors.length) {
        throw new ApiError(response.errors);
      }
      return response.data;
    },
    retry: false,
    enabled: transformExperimentsEnabled, // only fetch if experiments flag is enabled
  });

type Assignment = {
  experimentName: string;
  assignmentId?: string;
  variantId: string;
};

// NOTE: We are currently supporting workflows' ability to edit their experiment assignments
// regardless of whether they opted in to the experiment from the creation. So we need to support
// both creating and updating assignments from the same hook to simplify UI logic.
export const useCreateOrUpdateExperimentAssignment = (workflowId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: [queryKeys.workflows.byId(workflowId), 'experiments', 'createOrUpdate'],
    mutationFn: async (changedAssignments: Assignment[]) => {
      const response = await Promise.all(
        changedAssignments.map((assignment) => {
          // If the assignment has an assignmentId, we are updating an existing assignment
          if (assignment.assignmentId) {
            return v3.experimentAssignments.updateWorkflowExperimentAssignment({
              workflowId,
              assignmentId: assignment.assignmentId,
              updateAssignment: {
                variantId: assignment.variantId,
              },
            });
          }
          // Otherwise, create a new assignment
          return v3.experimentAssignments.assignWorkflowToExperiment({
            workflowId,
            assignment,
          });
        }),
      );
      // refetch latest assignment since this is now out of date. Could be optimistic update too
      queryClient.refetchQueries({
        queryKey: [queryKeys.workflows.byId(workflowId), 'experiments'],
      });
      return response;
    },
  });
};

export const useExperimentsForWorkflow = (
  transformExperimentsEnabled: boolean,
  workflowId: string | undefined,
) =>
  useQuery({
    queryKey: [queryKeys.workflows.byId(workflowId!), 'experiments'],
    queryFn: async () => {
      const response = await v3.experimentAssignments.getWorkflowExperimentAssignments({
        workflowId: workflowId!,
      });
      return response.data ?? [];
    },
    enabled: transformExperimentsEnabled && !!workflowId,
  });
