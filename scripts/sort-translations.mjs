import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function sortObjectKeys(obj) {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(sortObjectKeys);
  }

  return Object.keys(obj)
    .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: 'base' }))
    .reduce((result, key) => {
      result[key] = sortObjectKeys(obj[key]);
      return result;
    }, {});
}

/**
 * Sorts the keys of a translation file in place.
 * Runs as a pre-commit hook.
 */
function sortTranslationFile(filePath) {
  try {
    const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    const sortedContent = sortObjectKeys(content);
    fs.writeFileSync(filePath, JSON.stringify(sortedContent, null, 2) + '\n');
    console.log(`Sorted ${filePath}`);
  } catch (error) {
    console.error(`Error sorting ${filePath}:`, error);
    process.exit(1);
  }
}

// Get all translation files
const localesDir = path.join(__dirname, '../src/locales');
const locales = fs.readdirSync(localesDir);

locales.forEach((locale) => {
  const translationFile = path.join(localesDir, locale, 'translation.json');
  if (fs.existsSync(translationFile)) {
    sortTranslationFile(translationFile);
  }
});
