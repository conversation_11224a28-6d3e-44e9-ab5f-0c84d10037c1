import { useEffect, useState } from 'react';
import { Button, useToast } from '@floqastinc/flow-ui_core';
import cloneDeep from 'lodash/cloneDeep';
import { TaskDescription, TaskDescriptionDescription } from '@floqastinc/transform-v3';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { DetailSteps } from '../../../../../components/DetailSteps';
import * as Styled from './styles';
import DetailsCard from '@BuilderV3/routes/workflows/components/DetailsSlideout/DetailsCard';
import v3 from '@/services/v3';
import { Loading } from '@/components/Loading';

interface DetailsTabProps {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
}

export const DetailsTab = ({ workflowId, taskId, exampleSetId }: DetailsTabProps) => {
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const [descriptionValue, setDescriptionValue] = useState<TaskDescriptionDescription[]>();

  const { showToast, Toast } = useToast();
  const queryClient = useQueryClient();

  const fetchDescription = async (): Promise<TaskDescription | null> => {
    setLoading(true);
    let response = await v3.taskDescriptions.getTaskDescriptionByCompositeKey({
      workflowId,
      taskId,
      exampleSetId,
    });
    if (!response?.data?.id) {
      response = await v3.taskDescriptions.createTaskDescription({
        workflowId,
        taskId,
        exampleSetId,
        description: {
          generate: true,
        },
      });
    }
    if (!response?.data?.id) {
      console.error('Workflow task exampleSet description not found:', response);
      showToast(
        <Toast type="error">
          <Toast.Title>Something went wrong!</Toast.Title>
          <Toast.Message>
            We were unable to get description. ({response.errors.map((e) => e.detail).join('; ')})
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    }
    setLoading(false);
    return response.data || null;
  };

  const { data } = useQuery({
    queryKey: ['description'],
    queryFn: fetchDescription,
  });

  useEffect(() => {
    setDescriptionValue(cloneDeep(data?.description));
  }, [data?.description]);

  const updateDescription = async ({
    description,
    generate,
  }: {
    description?: TaskDescriptionDescription[];
    generate: boolean;
  }): Promise<TaskDescription | null> => {
    if (!data || !data?.id) return null;
    setLoading(true);
    const response = await v3.taskDescriptions.updateTaskDescription({
      taskDescriptionId: data?.id,
      workflowId,
      taskId,
      exampleSetId,
      description: {
        description: description || [],
        generate: generate,
      },
    });
    return response.data || null;
  };

  const updateDescriptionMutation = useMutation({
    mutationFn: ({
      description,
      generate,
    }: {
      description?: TaskDescriptionDescription[];
      generate: boolean;
    }) => updateDescription({ description, generate }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['description'] });
      setLoading(false);
      showToast(
        <Toast type="success">
          <Toast.Title>Success</Toast.Title>
          <Toast.Message>Updated task description</Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    },
    onError: (error) => {
      setLoading(false);
      console.error('Error updating task description:', error);
      showToast(
        <Toast type="error">
          <Toast.Title>Error</Toast.Title>
          <Toast.Message>Failed to fetch task description</Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    },
  });

  if (loading) {
    return <Loading />;
  }

  const regenButton = (
    <Button
      disabled={loading}
      color="dark"
      variant="outlined"
      onClick={() =>
        updateDescriptionMutation.mutate({
          generate: true,
        })
      }
    >
      Regenerate
    </Button>
  );

  let header;
  let body;
  if (editing) {
    header = (
      <>
        {regenButton}
        <Button
          disabled={loading}
          color="dark"
          variant="ghost"
          onClick={() => {
            setDescriptionValue(cloneDeep(data?.description));
            setEditing(false);
          }}
        >
          Cancel
        </Button>
        <Button
          disabled={loading}
          color="dark"
          variant="outlined"
          onClick={() => {
            updateDescriptionMutation.mutate({
              description: descriptionValue || [],
              generate: false,
            });
            setEditing(false);
          }}
        >
          Save
        </Button>
      </>
    );
    body = (
      <DetailsCard
        editable={true}
        taskDescription={descriptionValue || []}
        onChange={setDescriptionValue}
      />
    );
  } else {
    header = (
      <>
        {regenButton}
        <Button disabled={loading} color="dark" variant="outlined" onClick={() => setEditing(true)}>
          Edit
        </Button>
      </>
    );
    body = <DetailSteps workflowId={workflowId} taskId={taskId} isOpen={true} />;
  }

  return (
    <Styled.DetailsTab>
      <Styled.DetailsTabHeader>{header}</Styled.DetailsTabHeader>
      <Styled.DetailsTabBody>{body}</Styled.DetailsTabBody>
    </Styled.DetailsTab>
  );
};
