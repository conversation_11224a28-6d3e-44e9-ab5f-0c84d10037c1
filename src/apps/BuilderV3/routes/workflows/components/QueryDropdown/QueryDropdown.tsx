import { DropdownButton, DropdownPanel } from '@floqastinc/flow-ui_core';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { DropdownOptions } from '../SQLEditor/types';
import v3 from '@/services/v3';

export const QueryDropdown = ({
  currentQuery,
  selectedQuery,
  setSelectedQuery,
}: {
  currentQuery: string;
  selectedQuery: DropdownOptions;
  setSelectedQuery: (query: DropdownOptions) => void;
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const { data: queries } = useQuery({
    queryKey: ['flolake-query-statements'],
    queryFn: async () => {
      const data = await v3.strategies.getStoredFlolakeQueries();
      return data.data;
    },
  });

  useEffect(() => {
    if (queries && queries.length > 0) {
      const foundQuery = queries.find((option) => option.statement === currentQuery);
      if (foundQuery) {
        setSelectedQuery({
          id: foundQuery.statement,
          name: foundQuery.statement,
          bindMapping: foundQuery.bindMapping,
        });
      }
    }
  }, [queries, currentQuery]);

  const handleQuerySelect = (statement: string) => {
    const foundQuery = queries?.find((option) => option.statement === statement);
    if (foundQuery) {
      setSelectedQuery({
        id: foundQuery.statement,
        name: foundQuery.statement,
        bindMapping: foundQuery.bindMapping,
      });
    }
    setIsOpen(false);
  };

  return (
    <DropdownPanel
      style={{ maxWidth: '250px' }}
      isOpen={isOpen}
      aria-label="Query Dropdown"
      disableClear
      disableFilter
      onOpenChange={setIsOpen}
      onChange={handleQuerySelect}
      selectionMode="single"
      selectedValues={selectedQuery.name ? selectedQuery.name : ''}
    >
      <DropdownPanel.Trigger>
        <DropdownButton open={isOpen}>
          {selectedQuery.name ? selectedQuery.name : 'Preview Query'}
        </DropdownButton>
      </DropdownPanel.Trigger>
      <DropdownPanel.Content>
        {queries &&
          queries.length > 0 &&
          queries.map((query) => (
            <DropdownPanel.Option key={query.statement} value={query.statement}>
              {query.statement}
            </DropdownPanel.Option>
          ))}
      </DropdownPanel.Content>
    </DropdownPanel>
  );
};
