import { useEffect, useState, useRef, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { t } from '@/utils/i18n';
import { Button, Heading, Flex } from '@floqastinc/flow-ui_core';
import { EditorView, basicSetup } from 'codemirror';
import { python } from '@codemirror/lang-python';
import { keymap } from '@codemirror/view';
import { useMutation, useSuspenseQuery } from '@tanstack/react-query';
import { indentWithTab } from '@codemirror/commands';
import ScriptsDropdown from './ScriptsDropdown';
import {
  PageContainer,
  EditorContainer,
  ButtonContainer,
  PaddedDivider,
  AlertContainer,
  PaddedLoading,
} from './ScriptsPage.styled';
import v3, { ApiError } from '@/services/v3';
import { Loading } from '@/components/Loading';

export const ScriptsPage = () => {
  const { workflowId } = useParams();

  if (!workflowId) {
    return null;
  }

  return <WrappedScriptsPage workflowId={workflowId} />;
};

type WrappedScriptsPageProps = {
  workflowId: string;
};

const WrappedScriptsPage = ({ workflowId }: WrappedScriptsPageProps) => {
  const editorRef = useRef<EditorView | null>(null);
  const [selectedTask, setSelectedTask] = useState<string | null>(null);
  const [isFetching, setIsFetching] = useState(false);
  // const [isSaving, setIsSaving] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    if (!editorRef.current) {
      const view = new EditorView({
        doc: '',
        extensions: [basicSetup, python(), keymap.of([indentWithTab])],
        parent: document.getElementById('editor') as HTMLElement,
      });
      editorRef.current = view;
    }

    return () => {
      if (editorRef.current) {
        editorRef.current.destroy();
        editorRef.current = null;
      }
    };
  }, []);

  const getTaskScript = useCallback(
    async (selectedTask: string | undefined) => {
      emptyCodeEditor();
      if (!selectedTask) {
        return;
      }
      try {
        setIsFetching(true);
        setShowErrorAlert(false);
        const { data, errors } = await v3.tasks.getTaskScript({
          workflowId,
          taskId: selectedTask,
        });
        if (errors.length || !data) {
          throw new ApiError(errors);
        }
        applyToCodeEditor(data.script);
      } catch (error) {
        setErrorMessage(
          t('components.Admin.failedToFetchScript') +
            `${error instanceof ApiError ? error.message : 'Unknown error'}`,
        );
        setShowErrorAlert(true);
      } finally {
        setIsFetching(false);
      }
    },
    [workflowId],
  );

  // const handleSave = async () => {
  //   if (isSaving) {
  //     return;
  //   }
  //   const code = editorRef.current?.state.doc.toString();
  //   if (!workflowId || !selectedTask || !code) {
  //     return;
  //   }
  //   try {
  //     setIsSaving(true);
  //     setShowErrorAlert(false);
  //     const { errors } = await v3.tasks.updateTaskScript({
  //       workflowId,
  //       taskId: selectedTask,
  //       taskScript: {
  //         script: code,
  //       },
  //     });
  //     if (errors.length) {
  //       throw new ApiError(errors);
  //     }
  //     setShowSuccessAlert(true);
  //   } catch (error) {
  //     setErrorMessage(
  //       `Failed to save script: ${error instanceof ApiError ? error.message : 'Unknown error'}`,
  //     );
  //     setShowErrorAlert(true);
  //   } finally {
  //     setIsSaving(false);
  //   }
  // };

  const handleSave = useMutation({
    mutationFn: async () => {
      const code = editorRef.current?.state.doc.toString();
      if (!workflowId || !selectedTask || !code) {
        return;
      }
      const { errors } = await v3.tasks.updateTaskScript({
        workflowId,
        taskId: selectedTask,
        taskScript: {
          script: code,
        },
      });
      if (errors.length) {
        throw new ApiError(errors);
      }
    },
    onSuccess: () => {
      setShowSuccessAlert(true);
    },
    onError: (error) => {
      setErrorMessage(
        t('components.Admin.failedToSaveScript') +
          `${error instanceof ApiError ? error.message : 'Unknown error'}`,
      );
      setShowErrorAlert(true);
    },
    onMutate: () => {
      setShowErrorAlert(false);
    },
  });

  const isSaving = handleSave.isPending;

  const applyToCodeEditor = (text: string) => {
    if (editorRef.current) {
      emptyCodeEditor();
      editorRef.current.dispatch({
        changes: {
          from: 0,
          to: editorRef.current.state.doc.length,
          insert: text,
        },
      });
      editorRef.current.focus();
    }
  };

  const emptyCodeEditor = () => {
    if (editorRef.current) {
      editorRef.current.dispatch({
        changes: { from: 0, to: editorRef.current.state.doc.length, insert: '' },
      });
    }
  };

  const { data: workflow } = useSuspenseQuery({
    queryKey: ['workflow', workflowId],
    queryFn: async () => {
      const { data, errors } = await v3.workflows.getWorkflow({ workflowId });
      if (errors.length) {
        throw new ApiError(errors);
      }
      return data;
    },
  });

  const { data: tasks } = useSuspenseQuery({
    queryKey: ['workflow-tasks', workflowId],
    queryFn: async () => {
      const { data, errors } = await v3.tasks.getTasks({ workflowId });
      if (errors.length) {
        throw new ApiError(errors);
      }
      return data;
    },
  });

  return (
    <PageContainer>
      <Flex direction="column" gap="1rem">
        <Heading variant="h2" weight="semibold">
          {t('components.Admin.workflowScripts')}
        </Heading>
        <Heading variant="h4" color="neutral-600">
          {t('components.Admin.workflow', { workflowName: workflow?.name })}
        </Heading>

        <PaddedDivider />

        {showSuccessAlert && (
          <AlertContainer
            color="success"
            autoClose={true}
            alertDuration={5000}
            onClose={() => setShowSuccessAlert(false)}
          >
            <p>{t('components.Admin.scriptSaved')}</p>
          </AlertContainer>
        )}

        {showErrorAlert && (
          <AlertContainer color="danger" onClose={() => setShowErrorAlert(false)}>
            <p>{errorMessage}</p>
          </AlertContainer>
        )}

        <ScriptsDropdown
          tasks={tasks}
          selectedValue={selectedTask}
          setSelectedValue={setSelectedTask}
          getTaskScript={getTaskScript}
        />

        <EditorContainer>
          <div id="editor">{isFetching && <PaddedLoading />}</div>
        </EditorContainer>

        <ButtonContainer>
          <Button onClick={handleSave.mutate} disabled={!selectedTask || isFetching || isSaving}>
            {isSaving ? <Loading /> : 'Save Script'}
          </Button>
        </ButtonContainer>
      </Flex>
    </PageContainer>
  );
};
