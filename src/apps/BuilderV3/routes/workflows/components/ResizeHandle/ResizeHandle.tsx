import { styled } from 'styled-components';
import { IconButton } from '@floqastinc/flow-ui_core';
import ArrowLeft from '@floqastinc/flow-ui_icons/material/KeyboardDoubleArrowLeft';
import ArrowRight from '@floqastinc/flow-ui_icons/material/KeyboardDoubleArrowRight';

const VerticalLine = styled.div`
  height: 100%;
  width: 2px;
  border: 1px solid var(--flo-base-color-neutral-300);
  transition: border 0.1s ease-in;
`;

const StyledDiv = styled.div`
  display: flex;
  flex-direction: row;
  height: 100%;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid var(--flo-base-color-neutral-300);
  &:hover {
    ${VerticalLine} {
      border: 2px solid var(--flo-base-color-neutral-400);
    }
  }
`;

export const ResizeHandle = () => {
  return (
    <StyledDiv>
      <VerticalLine />
    </StyledDiv>
  );
};

type ExpandButtonProps = {
  direction: 'left' | 'right';
  onClick: () => void;
};
export const ExpandButton = ({ direction, onClick }: ExpandButtonProps) => {
  if (direction === 'left') {
    return (
      <StyledDiv>
        <IconButton onClick={onClick}>
          <ArrowLeft height={32} width={32} />
        </IconButton>
      </StyledDiv>
    );
  } else {
    return (
      <StyledDiv>
        <IconButton onClick={onClick}>
          <ArrowRight height={32} width={32} />
        </IconButton>
      </StyledDiv>
    );
  }
};
