{"components": {"ActivityLog": {"activityLog": "Activity Log", "ActvityHistoryItem": {"runBy": "Run by"}, "errorOccurredWhileLoadingRuns": "Error occurred while loading runs", "noRunsFound": "No runs found", "unexpectedError": "An unexpected error occurred"}, "Admin": {"actionPermanentDeleteTeam": "This action will permanently delete the team and all associated data. This action cannot be undone.", "APIKeyCreated": "API Key Created", "apiKeyDeleted": "API Key deleted", "APImakeRequests": "API Make Requests", "APISwaggerUI": "API Swagger UI", "attemptedRuns": "Attempted Runs: {{totalAttemptedRuns}}", "backToApp": "Back to App", "changeTeam": "Change Team", "completedAttempted": "of attempted runs completed", "completionRate": "Completion Rate: {{completionRate}}%", "createdAt": "Created At", "createdNewAPIKey": "A new API key has been created with the secret key: {{secretKey}}", "createKey": "Create Key", "deleteAPIKey": "Delete API Key", "deleteTeam": "Delete Team", "deleteUser": "Delete User", "emailSent": "Email sent", "failedToFetchScript": "Failed to fetch script", "failedToSaveScript": "Failed to save script", "filterWorkflows": "Filter Workflows", "getStartedInviting": "Get Started Inviting", "inviteByEmail": "Invite by <PERSON><PERSON>", "inviteMoreUsers": "Invite More Users", "inviteSent": "<PERSON><PERSON><PERSON> sent", "inviteUser": "Invite User", "lastUsed": "Last Used", "loadMore": "Load More", "newCreatedBy": "New Created By", "newEntityID": "New Entity ID", "noAPIKey": "No API Key", "noOneHere": "No one here", "notFound": "Not found", "noWorkflowsFound": "No workflows found", "organizationSettings": "Organization Settings", "permanentlyDelete": "Permanently Delete", "permanentlyDeleteAPIKey": "Permanently Delete API Key", "permDeleteUser": "Permanently Delete User", "pleaseCopySecret": "Please copy this secret key now. You won't be able to see it again!", "resendEmail": "<PERSON><PERSON><PERSON>", "roleUpdated": "Role updated", "runsByDate": "Runs by Date", "runsByStatus": "Runs by Status", "runStats": "Run Statistics", "scriptSaved": "<PERSON><PERSON><PERSON> saved", "secretKey": "Secret Key", "selectStep": "Select Step", "selectTeamMigrate": "Select Team to Migrate", "somethingWentWrong": "Something went wrong", "startAPIKey": "Start API Key", "switchTeams": "Switch Teams", "teamExternalID": "Team External ID", "teamName": "Team Name", "totalRuns": "Total Runs: {{totalRuns}}", "typeDelete": "Type Delete", "unableToInviteUser": "Unable to invite user", "unableToUpdateUserRole": "Unable to update user role", "userDeleted": "User deleted", "userDeleteUndone": "User deletion undone", "userRoleUpdated": "User role updated", "users": "Users", "viewScripts": "<PERSON>", "workflowMigrated": "Workflow migrated", "workflowScripts": "Workflow Scripts"}, "AgentList": {"agents": "Agents", "connectedData": "Connected Data", "deleteAgent": "Delete Agent", "editAgent": "Edit Agent", "lastEdited": "Last Edited", "lastRun": "Last Run", "oneHumanStep": "One human step is required for this agent.", "searchAgents": "Search Agents", "showActivityLog": "Show activity log", "unexpectedFallthrough": "Unexpected fallthrough while rendering agents table"}, "Builder": {"aboutTo": "You are about to ", "aboutTo2": " a message. This will also remove all subsequent messages in the chat. Do you want to continue?", "aboutToDeleteStep": "You are about to delete your step{{taskName}}, saved at {{taskCreatedAt}}", "actionBarEdit": "Edit", "actionBarSave": "Save", "actions": "Actions", "activateOnly": "Activate Only", "activateTest": "Activate & Test", "activeExperiments": "Active Experiments:", "add": "Add", "addCustomPrompt": "Add a custom prompt to the step. This will override the default prompt for the step.", "addFlag": "Add flag", "addInput": "Add input", "addInputsPrevSteps": "Add inputs from previous steps", "addItem": "Add Item", "addNewStep": "Add New Step", "addSection": "Add Section", "addStep": "Add a Step", "agentDescriptionOptional": "{{AGENT}} Description (optional)", "agentName": "Agent Name", "agentNamePlaceholder": "{{AGENT}} Name", "allFilesUnchanged": " and all of its files will remain unchanged.", "analyzingFiles": "Analyzing Files...", "aPreviousStep": "a previous step", "archived": "Archived:", "areYouSure": "Are you sure you want to delete the", "areYouSureRegenerateResponse": "Are you sure you want to regenerate this message? Regenerating this response will erase all subsequent messages in the thread.", "autoOpeningNewOutput": "Auto-opening new output", "builderInput": "Builder Input", "buildWorkflow": "Build Workflow", "cancel": "Cancel", "cannotUndo": "This operation cannot be undone.", "changeFrom": "Changing from ", "changingFrom": "Changing from", "chatMSGTextArea": "Chat message textarea", "chatWithAI": "Chat with AI to create a step for your {{agent}}", "checkSQLSyntax": "Please check your SQL syntax", "confirmAction": "Confirm Action", "copySQL": "Copy SQL", "copySQLErrorMessage": "Copy Error Message", "copyUserPrompt": "Copy Prompt", "couldTakeAMinute": "Could take a minute", "CreateAgent": {"advancedSetup": "Advanced Setup", "modes": "Modes", "settingsError": "Error saving changes", "settingsSaved": "Successfully saved changes"}, "createBlankVersion": "Create a blank version", "createNewWorkflow": "Create New Workflow", "createStep": "Create New Step", "createVersion": "Create New Version", "currentOutput": "Current Output", "dataSources": "Data Sources", "date": "Date", "datetime": "DATETIME", "default": "Default:", "delete": "Delete", "deleteInput": "Delete Input", "deleteMessage": "Are you sure you want to delete this message? Deleting this response will erase all subsequent messages in the thread.", "deleteRun": "Delete this run", "deleteStep": "Delete Step", "deleteStepAfter": "Delete this step and everything after", "deleteVersion": "Delete Version", "deleteVersionBody": "This will permanently delete this version. This action cannot be undone.", "deleteWorkflow": "Delete Workflow", "description": "Description", "detailsHere": "Details here...", "developmentVer": "A version still in development.", "draft": "Draft:", "draftMessage": "Draft Message", "draftOneOutput": "Draft One Output", "edit": "Edit", "editAgent": "Edit {{AGENT}}", "editMessage": "Are you sure you want to edit this message? Editing this response will erase all subsequent messages in the thread.", "editNotes": "Edit notes", "editStep": "Edit Step", "editTaskDescription": "Edit Task Description", "editTitle": "Edit title", "editWorkflow": "Edit Workflow", "enhanceWithAI": "Enhance with AI", "enhancingWithAI": "Enhancing with AI", "ensureInputsFilled": "Please ensure all inputs are filled", "enterDesc": "Enter a description", "enterDescription": "Enter description", "enterSQLQuery": "Enter SQL Query", "enterTitle": "Enter a title", "enterValue": "Enter value", "entity": "Entity", "Errors": {"agentNameRequired": "Agent name is required", "appSyncAuthenticationValidationInvalid": "AppSync Authentication validation invalid {{workflowId, taskId, exampleSetId}}", "badNetworkResponse": "Network response was not ok", "cantTransformSource": "Could not transform source: ", "currentTaskNotFound": "Current task not found", "emptyMessage": "Message cannot be empty.", "emptyPrompt": "Empty prompt", "enterSQLDescription": "Please enter a description of the SQL you want to generate", "errorApplyingSchema": "Error applying schema", "errorCreatingContext": "Error creating file context for workflow", "errorDuringCleanup": "Error during cleanup", "errorExecutingFlolake": "Error executing <PERSON>", "errorExecutingWorkflow": "Error executing workflow", "errorFetchingEntities": "Error fetching entities: ", "errorFetchingStrat": "Error fetching strategies: ", "errorHandlingInputs": "Error handling inputs", "errorUpdatingTask": "Error updating task:", "errorUpdatingWorkflow": "Error updating workflow status", "failedCreateExample": "Unexpected data error: Failed to create example", "failedCreateInputs": "Unexpected data error: Failed to create example input", "failedCreateOutputs": "Unexpected data error: Failed to create example output", "failedCreateTask": "Unexpected data error: Failed to create task'", "failedCreateTaskInput": "Unexpected data error: Failed to create task input", "failedCreateTaskOutput": "Unexpected data error: Failed to create task output", "failedCreateWorkflow": "Unexpected data error: Failed to create workflow", "failedDeleteExample": "Unexpected data error: Failed to delete example", "failedDeleteInputs": "Unexpected data error: Failed to delete example input", "failedDeleteOutputs": "Unexpected data error: Failed to delete example output", "failedExampleInputs": "Unexpected data error: Failed to get example inputs", "failedExampleOutputs": "Unexpected data error: Failed to get example outputs", "failedGetExample": "Unexpected data error: Failed to get example", "failedGetTask": "Unexpected data error: Failed to get task", "failedGetTaskInputURI": "Unexpected data error: Failed to get task input example uri", "failedGetTaskOutputs": "Unexpected data error: Failed to get task outputs", "failedGetWorkflowRuns": "Unexpected data error: Failed to get workflowRuns", "failedInputFile": "Unexpected data error: Failed to get example input file uri", "failedOutputFile": "Unexpected data error: Failed to get example output file uri", "failedPrincipals": "Unexpected data error: Failed to get principals", "failedSetExampleInput": "Unexpected error: Failed to set example input file value, but no errors were generated", "failedToFetchDesc": "Failed to fetch task description", "failedToGetWorkflow": "Unexpected data error: Failed to get workflows", "failedUpdateExperimentAssignments": "Failed to update experiment assignments", "failedUpdateTask": "Unexpected data error: Failed to update task", "failedUploadFile": "Failed to upload file: ", "failedWorkflowIn": "Unexpected data error: Failed to get workflow inputs", "failedWorkflowInputs": "Unexpected data error: Failed to get workflow run inputs", "fetchJEMTemplate": "Error fetching JEM Template:", "fetchMutation": "Error during fetching and mutation", "fileContextDisabled": "File context is disabled", "fileFromURI": "Error getting file from uri", "fileTypeUnsupported": "File type is not supported.", "forItem1": "For item ", "forItem2": "Field not found:", "generationFailed": "Generation failed", "generationFailedMessage": "We were unable to generate the SQL query. Please try again.", "IDRequired": "workflowId, taskId, and exampleSetId are required in the URL", "inputNotFound": "Could not find input with id ", "invalidColumnReference": "Invalid column reference: ", "invalidConfirm": "Invalid confirmation action.", "invalidConnection": "Invalid connection variable", "invalidEntryKey": "Invalid entry for key {{key}}", "invalidSchemaReference": "Invalid schema reference: ", "invalidSystem": "Invalid system: ", "invalidTable": "Invalid table: ", "invalidTableReference": "Invalid table reference: ", "issueDeletingInput": "There was an issue deleting the input", "itemNotFoundIndex": "Item not found at index: ", "noConvertedSQL": "SQL Conversion failed", "noConvertedSQLMessage": "We were unable to convert the SQL query. Please try again.", "noData": "No data returned", "noDataExample": "Unexpected error: no data returned from example set", "noDataMessage": "Unexpected error: no data returned from messages", "noDataOrErrors": "Unexpected error: no data or errors returned", "noDataReturned": "Unexpected error: no data or errors returned", "noDataReturnedCEI": "Unexpected error: no data or errors returned from createExampleInput", "noDataReturnedCTI": "Unexpected error: no data or errors returned from createTaskInput", "noDataReturnedCWI": "Unexpected error: no data or errors returned from createWorkflowInput", "noDataReturnedSEIFV": "Unexpected error: no data or errors returned from setExampleInputFileValue", "noExampleOutputID": "No example output id found", "noExamplePropsVOD": "No example provided in props for VersionOptionsDropdown", "noFileinDropdown": "No file found in dropdown options for selected id: ", "noIDsprovided": "No workflowId, taskId, or exampleSetId provided in params for Builder Page", "noMessageID": "Unexpected error: Message does not have an ID", "noMsgIDDel": "No message ID provided for deletion.", "noMsgIDEdit": "No message ID provided for editing.", "noMsgIDRegen": "No message ID provided for regeneration.", "noPrevTaskOutputs": "Unexpected data error: Failed to get previous task outputs", "noRunID": "workflowRunId is required", "noStratAvailable": "No strategies available", "noStrategyType": "Must specify output type for task strategy kind", "noTaskDelete": "Task to delete not found", "noTaskRevert": "Task to revert to not found", "noWorkflowID": "No workflow ID provided in params", "rejectedFiles": "Rejected files: ", "schemaError": "<PERSON><PERSON><PERSON>", "somethingWentWrong": "Something went wrong!", "taskDataUndefined": "Unexpected error: task runs data is undefined", "taskExampleIDMissing": "Task ID or Example Set ID is missing", "taskIDinput": "Error getting task input example uri for task", "taskIDoutput": "Error getting task output example uri for task", "taskInputNotFound": "Task input not found", "taskNotDefined": "Current task is not defined", "taskNotFound": "Task not found", "transformingSystemName": "Error transforming system name:", "unableToGetDescription": "We were unable to get description.", "unexpectedFileError": "An unexpected error occurred while loading the file.", "unexpectedInternalError": "We've experienced an unexpected internal error. Please try again.", "unexpectedWorkflowInput": "Unexpected wokflow input type", "unsupportedInputType": "Unsupported input type", "unsupportedProvider": "Unsupported provider: ", "unsupportedStrat": "Strategy not supported", "workflowDataUndefined": "Unexpected error: workflow run data is undefined", "workflowDescNotFound": "Workflow task exampleSet description not found:", "workflowInputUri": "Unexpected data error: Failed to get workflow run input value uri"}, "executeLLMGeneratedSQL": "Execute SQL Statement", "failedToFetchDesc": "Failed to fetch description", "file": "File", "fileType": "File Type", "finalizedVersionDefault": "A finalized version that can be set as default.", "forWorkflowToRun": "For a workflow to run, each step must have a Default version. When a Draft is ready, it can be Published, and only one Published version can be designated as Default at a time. Versions that are no longer needed can be Archived.", "GlobalInputs": {"addInput": "Add Input", "cancelOrSave": "Cancel or save before adding new inputs", "dataInput": "Data Input", "newInput": "New Input"}, "inactive": "Inactive", "inactiveExperiments": "Inactive Experiments:", "includeSelectedRange": "Include selected range(s)", "incompleteInput": "Incomplete Input", "initialInputIntegrationDescription": "Connect to data platform", "initialInputIntegrationTitle": "Integration", "initialInputUploadFileDescription": "Upload excel, csv, etc. Add more details", "initialInputUploadFileTitle": "Upload File", "inputType": "Input Type", "invalidSQLQuery": "Invalid SQL Query", "loading": "Loading...", "messageDeleted": "Your message was successfully deleted!", "messageRegenerated": "Your message was successfully regenerated!", "nameAgent": "Name this Agent", "nameAndDescription": "Name & Description", "newItem": "New Item", "newVersionName": "New Version Name", "next": "Next", "noActiveExampleFound": "No active example found", "noActiveVersions": "There are no active versions.", "noConnectionsAvailable": "No connections available", "noDataAvailable": "No data available", "noEntityAvailable": "No entities available", "noExamplesFound": "No examples found", "noSourcesAvailable": "No sources available", "noSQLQuery": "No SQL Query", "noTablesAvailable": "No tables available", "notFoundIn": "not found in ", "notInUse": "A version no longer in use.", "notSupportPreview": "is not supported for preview", "notYetRun": " has not yet been run.", "noWorkflowRunsCan": "No workflow runs can be made until a version is made active.", "number": "NUMBER", "open": "Open", "openInBuilder": "Open in Builder", "optionalDescription": "Description (optional)", "pCol": "+ Column", "permanentlyDelete1": "This action will permanently delete the ", "permanentlyDelete2": " input and all associated data. Once deleted, the input cannot be recovered. This action is not reversible.", "permanentlyDeleteInput": "Permanently delete input?", "pleaseCheckQuery": "Please check your query and try again", "pleaseWaitStep": "Please wait while we get this step ready to run.", "published": "Published:", "reachToSupport": "Please reach out to support before running this step.", "readyToRun": "This step is ready to run!", "regenerateAssistantResponse": "Regenerate Assistant Response", "regenerateMessage": "Regenerate message", "rename": "<PERSON><PERSON>", "renameVersion": "Rename Version", "requestedFormat": "Requested format", "runAgain": "Run again", "runRemoved1": "The run will be removed along with any files it produced. The parent ", "runWorkflow": "Run Workflow", "save": "Save", "search": "Search...", "searchConnections": "Search Connections", "selectAgentType": "Select Agent Type", "selectDate": "Select a date", "selectEntity": "Select Entity", "selectFile": "Select a file", "selectInitialInput": "Select Initial Input", "selectInput": "Select Input", "selectOption": "Select an option", "selectSource": "Select a Source", "selectStrat": "Select a strategy", "selectTable": "Select a Table", "selectTool": "Select Tool", "selectType": "Select a type", "selectVariant": "Select variant", "selectVersion": "Select Version", "setActive": "Set as active", "sqlEditor": "SQL Editor", "sqlEditorPrompt": "Describe what data you want to retrieve, e.g. 'Get all account data in Coupa'.", "sqlEditorPromptLabel": "SQL Generation Prompt", "sqlEditorToggleLabel": "AI Mode", "sqlGenerated": "SQL Generated", "sqlGeneratedSuccess": "SQL has been generated successfully", "statusChange1": "Reverting this version to a draft will leave no versions active. Runs cannot be made on this {{AGENT.toLocalLowerCase()}} until an active version is selected.", "statusChange2": "Activating this version will deactivate all other active versions. Runs will be made using this version.", "statusChange3": "This action will unarchive this version and activate it. All other active versions will be deactivated. Runs will be made using this version.", "statusChange4": "This will convert this version to a draft. Runs will not use this version unless is it selected as 'active.'", "statusChange5": "This action will archive this version. Archived versions can be restored at any time.", "statusChange6": "This action will archive this version. Archived versions can be restored at any time. Runs cannot be made on this {{AGENT.toLocalLowerCase()}} until an active version is selected.", "statusChange7": "This action will deactivate this version. Runs will not be made using this version.", "statusChange8": "Activating this version will set it as the default for future agent runs. We recommend running a test to ensure every step works before proceeding.", "stepOutput": "Step {{index}} Output", "stepTaskName": "Step: {{currentTask}}", "submit": "Submit", "taskBeenRunSuccessfully": "Your task has been succesfully run.", "taskDescription": "Task Description", "taskRunFailed": "Task run failed", "taskRunSuccess": "Task run successful", "testRun": "Test Run", "text": "Text", "textInput": "text-input", "theRange": "The range(s) {{range}} have been selected in sheet {{sheet}}. Please use that information to respond to the following message: {{message}}", "thisVersionIsArchived": "This version is archived. Change to Draft to make updates.", "thisVersionIsDraft": "This version is a draft. Publish changes to make this version available for running a workflow.", "thisVersionIsPublished": "This version is published. Change to <PERSON><PERSON><PERSON> to run it in a workflow.", "thisWillEllipsis": "This will...", "title": "Title", "TODO": "TODO", "treeViewTitle": "Data Sources", "typeCommand": "Type Command", "unableCreateNewStep": "Unable to create new step if the last step is not published.", "undo": "Undo", "updatedTaskDescription": "Updated task description", "versionExecutedWorkflow": "The version that will be executed when the workflow runs.", "versionOfStep": "Version of Step ", "versions": "Versions", "versionsCanBeOTFS": "Versions can be in one of the following statuses: ", "versionsTabSaved": "Saved", "viewDetails": "View Details", "viewRunHistory": "View run history", "willRemoveAllStepsAfter": ", which will remove all steps made after this step.", "workFlowRunTableSubtitle": "Get started by running this workflow. All future runs and their statuses will be listed here."}, "Connections": {"Actions": {"manage": "Manage", "requestAccess": "Request Access", "setup": "Set Up"}, "Data": {"availableData": "Available Data", "noDataAvailable": "No data available"}, "Errors": {"loadingPage": "Error loading page", "pleaseTryAgain": "Please try again"}, "Header": {"searchPlaceholder": "Filter connections"}, "Permissions": {"adminAccessRequired": "Requires admin access"}, "Status": {"connected": "Connected", "notConnected": "Not Connected"}, "title": "Transform Connections"}, "CreateAgent": {"advancedSetup": "Advanced Setup", "experiments": "Experiments", "modes": "Modes", "off": "Off", "on": "On"}, "FileInput": {"dropToUpload": "Or drop file here to upload", "formatList": "CSV, XLSX, or PDF less than 10MB", "uploadFiles": "Upload Files"}, "HeaderTitle": {"ariaLabels": {"cancelEditing": "Cancel editing", "editWorkflowName": "Edit workflow name", "saveChanges": "Save changes"}, "errors": {"nameRequired": "Name cannot be empty", "nameTooLong": "Name is too long (max {{max}} characters)"}, "toast": {"errorTitle": "Error", "invalidInputTitle": "Invalid Input", "updateAgentError": "Failed to update agent name"}, "tooltip": {"editName": "Edit Name"}}, "Runner": {"activeAgentsRequire": "Active agents require at least one human review step.", "activityLog": "Activity Log", "addInputsToRunAgent": "Add Inputs to Run Agent", "agentHasNoSteps": "Agent currently has no steps.", "agentHasNoSteps2": "Please navigate to the builder to add and publish steps for this agent.", "agentPreview": "Agent Preview", "agentPreview2": ": Review the steps prior to running.", "agentSteps": "Agent Steps", "allAgents": "All Agents", "approveRun": "Approve Run", "downloadFile": "Download File", "enterInput": "Enter {{name}}", "enterNumber": "Enter a number", "enterText": "Enter text", "Errors": {"badNetworkResponse": "Network response was not ok", "errorRunningWorkflow": "Error running workflow:", "inputError": "Unexpected error: Failed to create workflow run input", "noActiveExample": "No active example set found", "noDataFromWorkflowRun": "Unexpected error: Did not receive data from successful workflow run creation", "noDataReturned": "No data returned", "noDataReturnedFromTaskRun": "Unexpected error: no data returned from task run log", "noFileFoundForID": "No file found for input ID: ", "noInputFoundForID": "No input found for ID: ", "noRunData": "No run data received", "noTaskDescription": "Unexpected data error: No task description body returned.", "noWorkflowID": "No workflow ID specified", "requestError": "request error"}, "followingStepsExecuted": "The following steps will be executed when an agent is run.", "generatingDescription": "Generating description...", "inputsCount": "Inputs ({{count}})", "journalEntries": "Journal Entries", "makeActive": "Make Active", "noDataPreview": "No data to preview.", "rejectRun": "Reject Run", "resumeRun": "Resume Run", "runAgent": "Run Agent", "runBy": "Run by", "stepOfInput": "Step {{i}} of {{inputs}}", "taskUnpublished": "This task currently is unpublished. Please publish this task to generate a description.", "unpublishedStep": "Unpublished Step", "unsupportedInputType": "Unsupported input type", "uploadFile": "Upload File", "uploadInput": "Upload {{name}}", "uploadInputFile": "Upload {{name}} File", "viewErrorDetails": "View Error Details"}, "SQLEditorSplit": {"viewLLMDescription": "View LLM Description"}}, "generics": {"actions": "Actions", "active": "Active", "add": "Add", "addFlag": "Add Flag", "archived": "Archived", "authorize": "Authorize", "back": "Back", "beta": "Beta", "build": "Build", "cancel": "Cancel", "canel": "Cancel", "chat": "Cha<PERSON>", "close": "Close", "completed": "Completed", "confirm": "Confirm", "continue": "Continue", "count": "Count", "create": "Create", "created": "Created", "createdBy": "Created By", "custom": "Custom", "dashboard": "Dashboard", "date": "Date", "delete": "Delete", "deletion": "Deletion", "description": "Description", "details": "Details", "download": "Download", "downloadFile": "Download File", "draft": "Draft", "editor": "Editor", "email": "Email", "error": "Error", "execute": "Execute", "experiments": "Experiments", "failed": "Failed", "file": "File", "finish": "Finish", "found": "found", "generate": "Generate", "ID": "ID", "inProgress": "In Progress", "inputs": "Inputs", "lastModified": "Last Modified", "lastRun": "Last Run", "match": "Match", "message": "Message", "migrate": "Migrate", "name": "Name", "no": "No", "output": "Output", "preview": "Preview", "proceed": "Proceed", "publish": "Publish", "regenerate": "Regenerate", "rejected": "Rejected", "results": "Results", "role": "Role", "run": "Run", "runBy": "Run by", "runDeletion": "Run Deletion", "save": "Save", "saving": "Saving", "search": "Search", "select": "Select", "selected": "Selected", "send": "Send", "settings": "Settings", "startedAt": "Started at", "status": "Status", "step": "Step", "strategy": "Strategy", "submit": "Submit", "success": "Success", "task": "task", "team": "Team", "teams": "Teams", "templates": "Templates", "text": "Text", "this": "This", "title": "Title", "to": "to", "total": "Total", "undo": "Undo", "userStatus": "Status: {{status}}", "versions": "Versions", "view": "View", "you": "You"}}