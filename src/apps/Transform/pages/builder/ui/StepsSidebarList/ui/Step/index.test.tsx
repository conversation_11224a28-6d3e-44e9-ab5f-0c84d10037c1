import { describe, expect, afterEach, test, vi } from 'vitest';
import { Task } from '@floqastinc/transform-v3';
import { Step } from '.';
import { customRender } from '@/utils/testing';
import { useTask } from '@v3/tasks';

vi.mock('@v3/tasks');

const mockScriptTask = {
  id: 'task-1',
  kind: 'SCRIPT',
  name: 'Test Task',
  strategy: {
    kind: 'SCRIPT',
    buildStatus: 'BUSY',
  },
} as unknown as Task;

const mockReviewTask = {
  id: 'task-2',
  kind: 'REVIEW',
  name: 'Review Task',
  strategy: {
    kind: 'REVIEW',
  },
} as unknown as Task;

const mockJemExportTask = {
  id: 'task-3',
  kind: 'JEM_EXPORT',
  name: 'JEM Export Task',
  strategy: {
    kind: 'JEM_EXPORT',
  },
} as unknown as Task;

describe('Step', () => {
  afterEach(() => {
    vi.resetAllMocks();
  });

  test('GIVEN a step is a REVIEW step, THEN it should display a pause icon', async () => {
    vi.mocked(useTask).mockReturnValue({
      data: mockReviewTask,
    } as unknown as ReturnType<typeof useTask>);

    const { container } = customRender(<Step taskId={mockReviewTask.id} stepNum={1} />);

    await vi.waitFor(() => expect(container.querySelector('.pause-icon')).toBeInTheDocument());
  });

  test('GIVEN a step is a JEM_EXPORT step, THEN it should display a JEM icon', async () => {
    vi.mocked(useTask).mockReturnValue({
      data: mockJemExportTask,
    } as unknown as ReturnType<typeof useTask>);

    const { container } = customRender(<Step taskId={mockJemExportTask.id} stepNum={2} />);

    await vi.waitFor(() => expect(container.querySelector('.bolt-icon')).toBeInTheDocument());
  });

  test('GIVEN a step is a SCRIPT step with BUSY status, THEN it should display an loading icon', async () => {
    const mockBusyTask = {
      ...mockScriptTask,
      strategy: {
        ...mockScriptTask.strategy,
        buildStatus: 'BUSY',
      },
    } as unknown as Task;
    vi.mocked(useTask).mockReturnValue({
      data: mockBusyTask,
    } as unknown as ReturnType<typeof useTask>);

    const { container } = customRender(<Step taskId={mockScriptTask.id} stepNum={3} />);

    await vi.waitFor(() => expect(container.querySelector('.busy-icon')).toBeInTheDocument());
  });

  test('GIVEN a step is a SCRIPT step with COMPILED status, THEN it should display a check icon', async () => {
    const mockCompiledTask = {
      ...mockScriptTask,
      strategy: {
        ...mockScriptTask.strategy,
        buildStatus: 'COMPILED',
      },
    } as unknown as Task;

    vi.mocked(useTask).mockReturnValue({
      data: mockCompiledTask,
    } as unknown as ReturnType<typeof useTask>);

    const { container } = customRender(<Step taskId={mockCompiledTask.id} stepNum={4} />);

    await vi.waitFor(() => expect(container.querySelector('.compiled-icon')).toBeInTheDocument());
  });

  test('GIVEN a step is a SCRIPT step with FAILED status, THEN it should display a warning icon', async () => {
    const mockFailedTask = {
      ...mockScriptTask,
      strategy: {
        ...mockScriptTask.strategy,
        buildStatus: 'FAILED',
      },
    } as unknown as Task;

    vi.mocked(useTask).mockReturnValue({
      data: mockFailedTask,
    } as unknown as ReturnType<typeof useTask>);

    const { container } = customRender(<Step taskId={mockFailedTask.id} stepNum={5} />);

    const failedIcon = await vi.waitFor(async () => container.querySelector('.failed-icon'));

    expect(failedIcon).toBeInTheDocument();
  });

  test('GIVEN a step is a SCRIPT step, THEN it should display the step number', async () => {
    vi.mocked(useTask).mockReturnValue({
      data: mockScriptTask,
    } as unknown as ReturnType<typeof useTask>);

    const { container } = customRender(<Step taskId={mockScriptTask.id} stepNum={4} />);

    await vi.waitFor(() => expect(container.querySelector('.step-number')).toHaveTextContent('4'));
  });
});
