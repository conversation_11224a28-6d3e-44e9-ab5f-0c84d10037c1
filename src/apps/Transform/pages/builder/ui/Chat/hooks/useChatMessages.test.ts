import { act } from 'react';
import { afterAll, beforeEach, describe, expect, it, vi } from 'vitest';
import { renderHook } from 'vitest-browser-react';
import { useChatMessages } from './useChatMessages';
import { LocalStorage } from '@/utils/local-storage';

describe('useChatMessages', () => {
  const setItem = vi.spyOn(LocalStorage, 'set');
  vi.spyOn(LocalStorage, 'get').mockReturnValue(null);
  const removeItem = vi.spyOn(LocalStorage, 'remove');
  console.warn = vi.fn();
  console.error = vi.fn();

  beforeEach(() => {
    setItem.mockClear();
  });

  afterAll(() => {
    vi.clearAllMocks();
  });

  it('should return the correct initial state', () => {
    const { result } = renderHook(() =>
      useChatMessages({
        workflowId: 'test-workflow',
        taskId: 'test-task',
        exampleSetId: 'test-example-set',
      }),
    );

    expect(result.current.message).toBe('');
    expect(typeof result.current.setMessage).toBe('function');
  });

  it('should set and get message correctly', () => {
    const { result } = renderHook(() =>
      useChatMessages({
        workflowId: 'test-workflow',
        taskId: 'test-task',
        exampleSetId: 'test-example-set',
      }),
    );

    act(() => {
      result.current.setMessage('Hello, world!');
    });

    vi.waitFor(() => expect(result.current.message).toBe('Hello, world!'));
  });

  it('should save message to local storage', async () => {
    const { result } = renderHook(() =>
      useChatMessages({
        workflowId: 'test-workflow',
        taskId: 'test-task',
        exampleSetId: 'test-example-set',
      }),
    );

    act(() => {
      result.current.setMessage('Hello, world!');
    });

    const localStorageKey = `chatMessage-test-workflow-test-task-test-example-set`;
    vi.waitFor(() =>
      expect(setItem).toHaveBeenCalledWith(localStorageKey, {
        value: 'Hello, world!',
        timestamp: expect.any(Number),
      }),
    );
  });

  it('should clear old messages from local storage', () => {
    const findAllByPrefix = vi.spyOn(LocalStorage, 'findAllByPrefix');
    const localStorageKey = `chatMessage-test-workflow-test-task-test-example-set`;

    findAllByPrefix.mockReturnValue({
      [localStorageKey]: {
        value: 'Hello, world!',
        timestamp: Date.now() - 1000 * 60 * 60 * 24 * 100, // 100 days old
      },
    });

    renderHook(() =>
      useChatMessages({
        workflowId: 'test-workflow',
        taskId: 'test-task',
        exampleSetId: 'test-example-set',
      }),
    );

    expect(removeItem).toHaveBeenCalledWith(localStorageKey);
  });
});
