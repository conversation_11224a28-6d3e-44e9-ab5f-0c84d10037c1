import { I<PERSON><PERSON>utton, Sidebar, TreeView, Skeleton, Input, Flex } from '@floqastinc/flow-ui_core';
import { FolderOpenOutlined, FolderOutlined, SearchOutlined } from '@floqastinc/flow-ui_icons';
import { t } from '@/utils/i18n';
import Add from '@floqastinc/flow-ui_icons/material/Add';
import { EditorView } from 'codemirror';
import { useCallback, useState, RefObject } from 'react';
import { createSystemKey } from '@BuilderV3/utils/conversionFunctions';
import { FlolakeConnectionData } from '@/api/shared/types';

interface SourceTreeViewProps {
  connections: FlolakeConnectionData[];
  isFlolakeDataLoading: boolean;
  editorRef: RefObject<EditorView | null>;  
  isDraft: boolean;
}

export const SourceTreeView = ({
  connections,
  isFlolakeDataLoading,
  editorRef,
  isDraft,
}: SourceTreeViewProps) => {
  const [searchTerm, setSearchTerm] = useState('');

  const handleInsertSchema = useCallback(
    (schema: string) => {
      const editorInstance = editorRef.current;
      if (!editorInstance) return;

      const cursorPos = editorInstance.state.selection.main.head;
      const insertPosition =
        cursorPos === -1 ||
        cursorPos === editorInstance.state.doc.length ||
        (cursorPos === 0 && editorInstance.state.doc.length > 0)
          ? editorInstance.state.doc.length
          : cursorPos;

      editorInstance.dispatch({
        changes: {
          from: insertPosition,
          to: insertPosition,
          insert: schema,
        },
      });
      editorInstance.focus();
    },
    [],
  );
  // Function to sort column names properly, especially for numerical vals
  const sortColumnNames = (columns: any[]): any[] => {
    return [...columns].sort((a, b) => {
      // Extract all numbers from the column names
      const aNumbers = a.name.match(/\d+/g) || [];
      const bNumbers = b.name.match(/\d+/g) || [];

      // If both names contain numbers, compare them numerically
      if (aNumbers.length > 0 && bNumbers.length > 0) {
        // Compare each number in sequence
        for (let i = 0; i < Math.min(aNumbers.length, bNumbers.length); i++) {
          const aNum = parseInt(aNumbers[i], 10);
          const bNum = parseInt(bNumbers[i], 10);

          if (aNum !== bNum) {
            return aNum - bNum;
          }
        }

        // If all numbers are equal, compare the full strings
        return a.name.localeCompare(b.name);
      }

      // Default string comparison for columns without numbers
      return a.name.localeCompare(b.name);
    });
  };

  const filterData = (data: any) => {
    if (!searchTerm) return true;
    const term = searchTerm.toLowerCase();

    const sourceName = data.transformSystem.toLowerCase();

    const connectionName = data.connectionName?.toLowerCase() || '';

    // // If source or connection name matches, show everything
    if (sourceName.includes(term) || connectionName.includes(term)) {
      return true;
    }

    // Check if any tables match the search term
    const hasMatchingTables = data.tableData.some((table: any) =>
      table.tableName.toLowerCase().includes(term),
    );

    if (hasMatchingTables) {
      return true;
    }

    // If searching in columns, only show tables with matching columns
    return data.tableData.some((table: any) =>
      table.columns.some(
        (column: any) =>
          column.name.toLowerCase().includes(term) || column.type.toLowerCase().includes(term),
      ),
    );
  };

  const getFilteredTables = (tables: any[], sourceName: string, connectionName: string) => {
    if (!searchTerm) return tables;
    const term = searchTerm.toLowerCase();

    // If source or connection name matches, show all tables
    if (sourceName.toLowerCase().includes(term) || connectionName.toLowerCase().includes(term)) {
      return tables;
    }

    // If searching for table names, only show matching tables
    const hasTableMatches = tables.some((table: any) =>
      table.tableName.toLowerCase().includes(term),
    );

    if (hasTableMatches) {
      return tables.filter((table: any) => table.tableName.toLowerCase().includes(term));
    }

    // If searching in columns, only show tables with matching columns
    return tables.filter((table: any) =>
      table.columns.some(
        (column: any) =>
          column.name.toLowerCase().includes(term) || column.type.toLowerCase().includes(term),
      ),
    );
  };

  const getFilteredColumns = (
    columns: any[],
    sourceName: string,
    tableName: string,
    connectionName: string,
  ) => {
    if (!searchTerm) return columns;
    const term = searchTerm.toLowerCase();

    // If source, table, or connection name matches, show all columns
    if (
      sourceName.toLowerCase().includes(term) ||
      tableName.toLowerCase().includes(term) ||
      connectionName.toLowerCase().includes(term)
    ) {
      return columns;
    }

    // Otherwise only show matching columns
    return columns.filter(
      (column: any) =>
        column.name.toLowerCase().includes(term) || column.type.toLowerCase().includes(term),
    );
  };

  const hasValidConnections = connections && connections.length > 0;
  const showLoading = isFlolakeDataLoading && !hasValidConnections;

  return (
    <Sidebar
      style={{
        borderRight: 'none',
        padding: '24px',
        maxWidth: 'none',
        width: '95%',
        height: '100%', // Take full height
        display: 'flex',
        flexDirection: 'column', // Stack children vertically
        overflow: 'hidden', // Hide overflow
      }}
    >
      <div
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 10,
          backgroundColor: 'var(--fq-background)',
          paddingBottom: '12px',
        }}
      >
        <Sidebar.Title>
          {t('components.Builder.treeViewTitle')}
        </Sidebar.Title>
        {!showLoading && (
          <Input
            onChange={(value: string) => setSearchTerm(value)}
            aria-label={t('components.Builder.searchConnections')}
            isDisabled={!connections || connections.length === 0}
            placeholder={t('components.Builder.search')}
            styleOverrides={{
              root: {
                width: '95%',
                paddingLeft: '10px',
                paddingTop: '8px',
              }
            }}
            value={searchTerm}
          >
            <Input.AddonLeft>
              <SearchOutlined size={20} />
            </Input.AddonLeft>
          </Input>
        )}
      </div>
      <Sidebar.Menu>
        {showLoading ? (
          <div
            style={{
              padding: '12px',
              display: 'flex',
              flexDirection: 'column',
              gap: '10px',
            }}
          >
            <Skeleton variant="rectangle" height={32} width="100%" />
            <Skeleton variant="rectangle" height={32} width="100%" />
          </div>
        ) : !hasValidConnections ? (
          <Sidebar.Item isDisabled>
            {t('components.Builder.noConnectionsAvailable')}</Sidebar.Item>
        ) : (
          <TreeView
            collapseIcon={<FolderOutlined size={20} />}
            expandIcon={<FolderOpenOutlined size={20} />}
          >
            {connections &&
              connections.filter(filterData).map((conn: any, index: number) => {
                if (!conn.tableData) {
                  return null;
                }

                const sourceName =
                  conn.transformSystem?.charAt(0).toUpperCase() +
                  conn.transformSystem?.slice(1).toLowerCase();

                return (
                  <TreeView.Section
                    key={`${conn.integrationSystem}-${index}`}
                    itemId={conn.integrationSystem}
                    label={`${sourceName} - ${conn.connectionName}`}
                    initiallyExpanded={false}
                  >
                    {getFilteredTables(
                      conn.tableData,
                      conn.transformSystem,
                      conn.connectionName,
                    ).map((table: any) => {
                      const tableName =
                        table.tableName.charAt(0).toUpperCase() +
                        table.tableName.slice(1).toLowerCase();
                      const tableId = `${table.schemaName}.${table.tableName}`;
                      return (
                        <TreeView.Item
                          key={tableId}
                          itemId={tableId}
                          label={tableName}
                          initiallyExpanded={false}
                        >
                          {getFilteredColumns(
                            sortColumnNames(table.columns),
                            conn.transformSystem,
                            table.tableName,
                            conn.connectionName,
                          ).map((column: any) => {
                            const columnId = `${tableId}.${column.name}`;

                            return (
                              <TreeView.Item
                                key={columnId}
                                itemId={columnId}
                                style={{
                                  width: '100%',
                                }}
                                label={
                                  <Flex
                                    align="center"
                                    jusitfy="flex-start"
                                  >
                                    <IconButton
                                      size="sm"
                                      disabled={!isDraft}
                                      onClick={() => {
                                        const systemName = createSystemKey(conn);
                                        const schema = `{${systemName}}.{${table.tableName.toUpperCase()}}.{${column.name.toUpperCase()}}`;
                                        handleInsertSchema(schema);
                                      }}
                                    >
                                      <Add />
                                    </IconButton>
                                    <span
                                      style={{
                                        whiteSpace: 'nowrap',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        flex: 1,
                                      }}
                                      onClick={() => {
                                        navigator.clipboard.writeText(conn.schemaName);
                                      }}
                                    >
                                      {`${column.name} (${column.type})`}
                                    </span>
                                  </Flex>
                                }
                              />
                            );
                          })}
                        </TreeView.Item>
                      );
                    })}
                  </TreeView.Section>
                );
              })}
          </TreeView>
        )}
      </Sidebar.Menu>
    </Sidebar>
  );
};
