import { Theme } from '@floqastinc/flow-ui_core';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createElement } from 'react';
import { ComponentRenderOptions, render, RenderResult } from 'vitest-browser-react';
import { BrowserRouter } from 'react-router-dom';

/**
 * Custom render function that applies the FloQast theme before rendering
 * This eliminates the need to call Theme.apply() in individual test files
 *
 * @param ui - The React component to render
 * @param options - Additional render options
 * @returns The rendered screen object from vitest-browser-react
 */
export function customRender(
  ui: React.ReactNode,
  { container, baseElement, wrapper: WrapperComponent }: ComponentRenderOptions = {},
): RenderResult {
  Theme.apply();

  const queryClient = new QueryClient();

  const AllProviders = ({ children }: { children: React.ReactNode }) => {
    const wrapped = WrapperComponent ? createElement(WrapperComponent, null, children) : children;

    return createElement(QueryClientProvider, { client: queryClient }, wrapped);
  };

  return render(ui, {
    container,
    baseElement,
    wrapper: AllProviders,
  });
}

/**
 * Utility to create a wrapper that provides BrowserRouter context for tests
 */
export const createBrowserWrapper = () => {
  return function Wrapper({ children }: { children: React.ReactNode }) {
    return createElement(BrowserRouter, null, children);
  };
};
