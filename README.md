# transform-client

## Quick Start

1. Install dependencies: `npm ci`
1. Ensure [transform_api](https://github.com/FloQastInc/transform_api) is running locally
1. Run project: `npm run dev`
1. Access the Transform Client at http://localhost:8005/transform

### Notes

- These instructions run the app in "Standalone" mode, if you need to run this app "injected" into another FloQast app, find the "Running Locally with Injected Mode" below
- `/transform` is the route which Transform Client is mounted in deployed environements, so it is configured as the `basename` in React Router in this app

## File Structure

```
transform-client/src/apps/
|-- Transform/
|  |-- app/
|  |  |-- routes.tsx
|  |  |-- app.tsx
|  |-- components/
|  |  |-- MyComponent/
|  |  |  |-- MyComponent.tsx
|  |  |  |-- MyComponent.styles.tsx
|  |  |  |-- MyComponent.test.tsx
|  |-- pages/
|  |  |-- my-page/
|  |  |  |-- MyPage.tsx
|  |  |  |  |-- ui/
|  |  |  |  |  |-- MyCompoundComponent/
|  |  |  |  |  |  |-- components/
|  |  |  |  |  |  |-- MyCompoundComponent.tsx
|  |  |  |  |  |  |-- MyCompoundComponent.styles.tsx
|  |  |  |  |  |  |-- MyCompoundComponent.test.tsx
```

Root level:

```
transform-client/src/
|-- apps/
|-- components/
|-- hooks/
|-- locales/
|-- services/ (May be moved into api)
|-- utils/
```

## Development

### Available Commands

In the project directory, you can run:

#### `npm run dev`

Runs the app in the development mode.<br />
Open http://localhost:8005/transform to view it in the browser.

The page will reload if you make edits.<br />
You will also see any lint errors in the console.

#### `npm start`

Runs the app in injected mode (requires [client-hub](https://github.com/FloQastInc/client-hub) to be running).

The bundle will be rebuilt if you make edits, but will need to manually reload the host app to see the changes.

#### `npm test`

Launches the test runner in the interactive watch mode.<br />
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

#### `npm run build`

Builds the app for production to the `build` folder.<br />
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.<br />
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### Updating Environment Variables

The `.env.development` environment variables are shared amongst all devs. Create a `.env.development.local` to override development environment variables that are unique per developer. This file should not be committed into source code.

For example:

```shell
echo "REACT_APP_DEVELOPMENT_AUTHORIZATION=<EMAIL>" > .env.development.local
```

Fq environment now use `.env` as default. If your environment variables name are dynamic (for example: `REACT_APP_CLOSE_API_HOSTNAME` is `https://fq1.floqast.engineering` on `fq1` and `https://fq2.floqast.engineering` on `fq2`), you can set the value with your NODE_ENV as a variable, for example `REACT_APP_CLOSE_API_HOSTNAME=https://$REACT_APP_NODE_ENV.floqast.engineering`.

**NOTE**: If `.env.*` is used, the values from `.env.*` will override any values specified in `.env` as they take precedence over `.env`.

### Invoke lambdas in local-lambda

#### Prerequisites

- [ ] email of user in the desired environment (local or fq)

#### Steps

1. Set the `REACT_APP_DEVELOPMENT_AUTHORIZATION` environment variable in `.env.development.local` to the desired user's email

1. Spin up [local-lambda](https://github.com/FloQastInc/local-lambda) connected to your fq env. For example, if you are getting a user from `fq5`, run `yarn local fq5` for `fq5` in the `local-lambda` repo.

1. Run the app in standalone mode with `npm run start:standalone`. **NOTE**: If you were already running the app and you made changes to any `.env` files, you will need to restart the app in order to see the new changes.

1. (Optional - Recommended for new dev) Use our Example component to test sending request to lambdas.

1. Use `lambdaRequest` functions from `/helpers/request` to send request to lambda. For code example, see [Example component](https://github.com/FloQastInc/transform-client/blob/main/src/components/Example/Example.js).

### Invoke lambdas in fq env

#### Steps

1. Deploy your injected client to your fq environment.

1. Ensure the deployed [client-hub](https://github.com/FloQastInc/client-hub) has included the new client module. Follow this [guide](https://floqast.atlassian.net/wiki/spaces/PLATFORM/pages/2177957907/Creating+running+a+new+injected+Client+Hub+frontend#Integrating-into-client-hub) for more info.

1. (Optional - Recommended for new dev) Pass in any system and lambda name to test the lambda in your fq environment.

1. Use `lambdaRequest` functions from `/helpers/request` to send request to lambda. For code example, see [Example component](https://github.com/FloQastInc/transform-client/blob/main/src/components/Example/LambdaTestSection.js).

### Sending request to www-close in fq env

1. Ensure the `REACT_APP_CLOSE_API_HOSTNAME` environment variable is updated with the correct value in your `.env` files. For example, if your fq env is `fq5`, ensure `REACT_APP_CLOSE_API_HOSTNAME=https://fq5.floqast.engineering` exists inside `.env.fq5`.

1. (Optional - Recommended for new dev) Click on the `Test close endpoint` button to test a request to the `/api/users/me` route in www-close

1. Use `request` functions from `/helpers/request` to send request to close api. For code example, see [Example component](https://github.com/FloQastInc/transform-client/blob/main/src/components/Example/WWWCloseTestSection.js).

---

## Running Locally with Injected Mode

See [this](https://floqast.atlassian.net/wiki/spaces/CLIO/pages/2819981548/How+to+Run+React+Clients+in+Injected+Standalone+modes) for more information.

Services needed:

- `client-hub`
- `client-proxy`

1. Create `.env.development.local`
   - Set `REACT_APP_DEVELOPMENT_AUTHORIZATION=YOUR_EMAIL` (this should be specific to the environment, for example `<EMAIL>`)
1. Run `client-hub`
   - `cd client-hub`
   - `npm run start`
1. Run `client-proxy`
   - `cd client-proxy`
   - `npm run start --env=fq11`
1. Run `transform-client`
   - `npm run start`

### Post-Install

1. Update shared dependency versions to match the host app. For example, if this client were to be injected into [close-client-v2](https://github.com/FloQastInc/close-client-v2), update the `react` version (as well as `react-dom` or `react-router-dom`) in [package.json](./package.json) to match [the version specified there](https://github.com/FloQastInc/close-client-v2/blob/main/package.json#L101-L102).

---

## Deployment

### CI/CD pipeline

When a branch is created with a suffix of the env (e.g. `my_branch-fq2`) and pushed to github, a webhook triggers a Jenkins build for `fq2`. The Jenkins pipeline will deploy the artifacts to `https://services-fq2.floqast.engineering/:repo_name`. At this url, a `build.json` file will be available that contains a reference to the folder that contains your `main.css` and `main.js` files. This is done for cache busting purposes.

`https://services-fq2.floqast.engineering/:repo_name/build.json`

```
{
  "assets": "https://services-fq2.floqast.engineering/:repo_name/1587588914.555.0_1960864",
  "branch": "my_branch-fq2",
  "hash": "1960864",
  "number": "555",
  "time": "1587588914",
  "utc": "2020-04-22T13:55:14Z-0700",
  "version": "1587588914.555.0_1960864"
}
```

In this example, `https://services-fq2.floqast.engineering/:repo_name/1587588914.555.0_1960864` will be the url where `main.css` and `main.js` will be located.

### The build.yml file

There is a `build.yml` file at the root of this repo. This file is used by the CI/CD pipeline to determine which node version to use.

### Public Access

By default, all react applications are locked down to the FloQast VPN. In order to make your client publicly accessible in production, you must:

1. Add your client name (`transform-client`) into [allowlisted_paths/client-services](https://github.com/FloQastInc/terraform-infrastructure/blob/main/fq/client-services/variables.tf#L29)
1. Apply your terraform changes by running [Terraform-Apply-Client-Services](https://jenkins-dev.floqast.engineering/job/Terraform/job/Terraform-Apply-Client-Services/)

## Local Authentication

Before running the app locally, you will need to add the `REACT_APP_DEVELOPMENT_AUTHORIZATION` env variable to `.env.development.local`. The value should be your development login email address:

```
REACT_APP_DEVELOPMENT_AUTHORIZATION=<EMAIL>
```
