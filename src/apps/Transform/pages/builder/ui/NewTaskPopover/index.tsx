import {
  I<PERSON><PERSON>utton,
  Popover,
  Card,
  Text,
  Input,
  DropdownPanel,
  DropdownButton,
  Button,
  InputWrapper,
} from '@floqastinc/flow-ui_core';
import Add from '@floqastinc/flow-ui_icons/material/Add';
import { t } from '@/utils/i18n';
import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import * as Styled from './index.styles';
import { useStrategies, CreateableStrategyKind } from './hooks/useStrategies';
import { LoadingButton } from '@/components/LoadingButton/LoadingButton';
import { createTask, CreateTaskParams } from '@/api/bff/tasks';
import { useTasks } from '@v3/tasks';
import { useExamples } from '@v3/examples';

export const NewTaskPopover = () => {
  const { workflowId = '' } = useParams();
  const navigate = useNavigate();

  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [strategyKind, setStrategyKind] = useState<CreateableStrategyKind | undefined>(undefined);
  const [isStrategyDropdownOpen, setIsStrategyDropdownOpen] = useState(false);

  const tasksQuery = useTasks(
    {
      workflowId,
    },
    {
      enabled: !!workflowId,
    },
  );

  const lastTaskId = tasksQuery.data?.[tasksQuery.data.length - 1]?.id;
  const examplesQuery = useExamples(
    {
      workflowId,
      taskId: lastTaskId!, // Using non-null assertion since query is disabled when lastTaskId is undefined
    },
    {
      enabled: !!workflowId && !!lastTaskId,
    },
  );
  const strategies = useStrategies();
  const defaultStrategy = strategies.find((s) => s.kind === strategyKind)?.defaultStrategy;

  const hasActiveExample = examplesQuery.data?.some((example) => example.status === 'ACTIVE');

  // NOTE: This presumes that there is an output for the task.
  const isNewTaskCreationDisabled = (tasksQuery.data?.length ?? 0) > 0 && !hasActiveExample;

  const createTaskMutation = useMutation({
    mutationKey: ['createTask', workflowId],
    mutationFn: (params: CreateTaskParams) => createTask(params),
    onSuccess: (data) => {
      setOpen(false);
      navigate(
        `/builder/v3/agents/${workflowId}/steps/${data.task.id}/examples/${data.example.id}`,
        { replace: true },
      );
    },
    onError: (error) => {
      console.error('Error creating task:', error);
    },
  });

  const handleSubmit = () => {
    if (!title || !defaultStrategy) {
      return;
    }
    createTaskMutation.mutate({
      task: {
        name: title,
        description,
        strategy: defaultStrategy,
      },
      workflowId,
      previousTaskId: lastTaskId,
    });
  };

  return (
    <Popover
      data-testid="new-task-popover"
      open={open}
      onOpenChange={(isOpen: boolean) => {
        setOpen(isOpen);
      }}
    >
      <Popover.Trigger>
        <IconButton disabled={isNewTaskCreationDisabled}>
          <Add />
        </IconButton>
      </Popover.Trigger>
      <Popover.Content>
        <Card>
          <Card.Header separator={false}>
            <Text weight={6}>{t('components.Builder.createStep')}</Text>
          </Card.Header>
          <Card.Description>
            <Styled.CardContent>
              <Input
                label={t('components.Builder.title')}
                placeholder={t('components.Builder.enterTitle')}
                value={title}
                onChange={(value: string) => {
                  setTitle(value);
                }}
              />
              <Input
                label={t('components.Builder.description')}
                placeholder={t('components.Builder.enterDesc')}
                value={description}
                onChange={(value: string) => {
                  setDescription(value);
                }}
              />
              <InputWrapper>
                <InputWrapper.Label>{t('components.Builder.selectTool')}</InputWrapper.Label>
                <DropdownPanel
                  style={{ width: '100%' }}
                  onChange={(value: CreateableStrategyKind) => {
                    setStrategyKind(value);
                    setIsStrategyDropdownOpen(false);
                  }}
                  isOpen={isStrategyDropdownOpen}
                  onOpenChange={(isOpen: boolean) => {
                    setIsStrategyDropdownOpen(isOpen);
                  }}
                  disableClear={true}
                  disableFilter={true}
                  selectedValues={strategyKind}
                >
                  <DropdownPanel.Trigger>
                    <DropdownButton>
                      <Text>
                        {strategyKind
                          ? strategies.find((s) => s.kind === strategyKind)?.label
                          : t('components.Builder.selectStrat')}
                      </Text>
                    </DropdownButton>
                  </DropdownPanel.Trigger>
                  <DropdownPanel.Content>
                    {strategies.map(({ kind, label }) => (
                      <DropdownPanel.Option key={kind} value={kind} name={label}>
                        {label}
                      </DropdownPanel.Option>
                    ))}
                  </DropdownPanel.Content>
                </DropdownPanel>
              </InputWrapper>
              {createTaskMutation.isPending ? (
                <Styled.ButtonContainer>
                  <LoadingButton data-testid="new-task-loading-button">
                    {t('generics.submit')}
                  </LoadingButton>
                </Styled.ButtonContainer>
              ) : (
                <Styled.ButtonContainer>
                  <Button
                    data-testid="new-task-submit-button"
                    disabled={!title || !strategyKind}
                    onClick={handleSubmit}
                  >
                    {t('generics.submit')}
                  </Button>
                </Styled.ButtonContainer>
              )}
            </Styled.CardContent>
          </Card.Description>
        </Card>
      </Popover.Content>
    </Popover>
  );
};
