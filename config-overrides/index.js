/* eslint-disable no-restricted-syntax, i18next/no-literal-string, import/no-extraneous-dependencies */
const {
    addPlugins,
    getPlugin,
    pluginByName,
    removePlugins,
} = require('@craco/craco');
const StandaloneSingleSpaPlugin = require('standalone-single-spa-webpack-plugin');
const eslintConfig = require('../.eslintrc.js');
const { isDevelopment, isStandalone } = require('./env');
const { getExternals, getStandaloneImportMap } = require('./externals');

const appName = 'transform-client';

module.exports = {
    webpack: {
        configure: (config, { paths }) => {
            config.externals = getExternals();

            config.entry = `${paths.appSrc}/single-spa-entry.js`;

            config.output.library = appName;
            config.output.libraryTarget = 'umd';
            config.output.filename = 'main.hub.js';

            // set bundle optimizations
            config.optimization = {
                ...config.optimization,
                minimize: !isDevelopment,
                splitChunks: {
                    cacheGroups: { default: false },
                },
            };

            if (isDevelopment) {
                // handle lack of webpack v5 polyfills
                //
                // So far only necessary due to including api-mocks lib,
                // which is only used in dev.
                //
                // References:
                // https://webpack.js.org/blog/2020-10-10-webpack-5-release/#automatic-nodejs-polyfills-removed
                // https://gist.github.com/ef4/d2cf5672a93cf241fd47c020b9b3066a
                // eslint-disable-next-line global-require
                const webpack = require('webpack');

                config.resolve = {
                    ...config.resolve,
                    fallback: {
                        ...config.resolve.fallback,
                        fs: false,
                    },
                };

                addPlugins(config, [
                    new webpack.ProvidePlugin({ Buffer: ['buffer', 'Buffer'] }),
                ]);
            }

            config.module.rules.push({
                test: /\.m?js$/,
                include: /node_modules/,
                type: 'javascript/auto',
                resolve: {
                    fullySpecified: false,
                },
            });

            config.resolve = {
                ...config.resolve,
                alias: {
                    '@': `${paths.appSrc}`,
                },
            };

            const { match: cssPlugin } = getPlugin(
                config,
                pluginByName('MiniCssExtractPlugin')
            );

            if (cssPlugin) {
                cssPlugin.options.filename = 'main.hub.css';
            }

            // https://stackoverflow.com/a/60353355
            removePlugins(config, pluginByName('ModuleScopePlugin'));

            if (!isStandalone) {
                // remove html plugin when injected
                removePlugins(config, pluginByName('HtmlWebpackPlugin'));
            }

            if (isStandalone) {
                // disable chunks
                delete config.optimization;

                addPlugins(config, [
                    new StandaloneSingleSpaPlugin({
                        appOrParcelName: `@floqast/${appName}`,
                        importMapUrl: new URL(
                            'https://services-fq1.floqast.engineering/import-map.json'
                        ),
                        importMap: getStandaloneImportMap(),
                    }),
                ]);
            }

            return config;
        },
    },
    jest: {
        configure: (config, { paths }) => {
            config.transformIgnorePatterns = [
                '/node_modules/(?!(fq-ui)/)/(?!(@floqastinc/flow-ui_core|@floqastinc/flow-ui_icons)/)',
            ];
            if (!config.transform) {
                config.transform = {};
            }
            config.transform['^.+\\.(js|jsx)$'] = 'babel-jest';
            config.moduleNameMapper = {
                '^@/(.*)': `${paths.appSrc}/$1`,
                '^fq-ui$': `${paths.appNodeModules}/fq-ui/dist/bundle.js`,
            };
            return config;
        },
    },
    style: {
        sass: {
            loaderOptions: {
                additionalData: `$appName: ${appName}; $standalone: ${!!isStandalone};`,
            },
        },
    },
    devServer: {
        allowedHosts: 'all',
        headers: {
            'Access-Control-Allow-Origin': '*',
        },
    },
    eslint: {
        mode: 'file',
        configure: () => eslintConfig,
    },
};
