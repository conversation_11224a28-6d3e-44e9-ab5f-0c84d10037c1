import { useEffect, useState } from 'react';
import { Heading, Text, Toggle } from '@floqastinc/flow-ui_core';
import { Experiment, WorkflowExperimentAssignment } from '@floqastinc/transform-v3';
import * as Styled from './index.styled';
import { useFeatureFlags } from '@/components/FeatureFlag';
import { useExperiments, useExperimentsForWorkflow } from '@BuilderV3/api/experiments';
import { t } from '@/utils/i18n';
import { Loading } from '@/components/Loading';

type ExperimentsProps = {
  onChange: (experimentName: string, variantId: string) => void;
  workflowId: string;
};

const getActiveExperimentVariantForWorkflow = (
  experiment: Experiment,
  assignments: WorkflowExperimentAssignment[],
  workflowId?: string,
) => {
  if (workflowId) {
    // In the case of editing an existing workflow we should either find the variant
    const assignment = assignments.find(
      (a) => a.experimentName === experiment.experimentName,
    )?.variantId;
    if (assignment) {
      return assignment;
    }
    // or default to `stable`, the presumed default for new experiments for existing workflows
    return 'stable';
  }
  // In the case of creating a new workflow we default to 'experimental'
  return 'experimental';
};

// converts list of experiments to key-value object mapping
const createAssignedExperimentsMap = (experiments: WorkflowExperimentAssignment[]) => {
  const result = Object.fromEntries(
    experiments.map((assignment: WorkflowExperimentAssignment) => [
      assignment.experimentName,
      assignment.variantId,
    ]),
  );
  return result;
};

export const Experiments = ({ workflowId, onChange }: ExperimentsProps) => {
  const { getFlag } = useFeatureFlags();
  const transformExperimentsEnabled = getFlag('enable-experiments', false);
  const [initializedAssignments, setInitializedAssignments] = useState<boolean>(!!workflowId);

  // get all experiments regardless of assigned state
  const { data: experimentOptions } = useExperiments(transformExperimentsEnabled);
  // fetch current experiment states
  const { data: experimentAssignments, isPending: assignmentsIsPending } =
    useExperimentsForWorkflow(transformExperimentsEnabled, workflowId);

  const handleExperimentChange = (value: boolean, experiment: Experiment) => {
    const variantToUse = experiment.variants.find((e) =>
      // TODO: this is not ideal, would rather have experiment model schema on/off
      value ? e.id === 'experimental' : e.id === 'stable',
    );
    if (variantToUse) {
      onChange(experiment.experimentName, variantToUse.id);
    }
  };

  // determine which experiments need to default to on
  useEffect(() => {
    // on creation, we need to set experiments to default to true
    if (!workflowId && !initializedAssignments) {
      const newAssignments = createAssignedExperimentsMap(experimentAssignments ?? []);
      (experimentOptions ?? [])
        .filter((experiment) => experiment.active)
        .forEach((experiment) => {
          if (!newAssignments[experiment.experimentName]) {
            // no experiment exists, set it in the map
            handleExperimentChange(true, experiment);
          }
        });
      setInitializedAssignments(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [experimentOptions, workflowId, initializedAssignments]);
  // hide this component if transform experiments are not enabled
  if (!transformExperimentsEnabled) {
    return null;
  }
  if (workflowId && assignmentsIsPending) {
    return <Loading />;
  }
  return (
    <>
      <Styled.ExperimentSettings>
        <Heading variant={'h5'}>{t('components.CreateAgent.experiments')}</Heading>
        <Styled.ExperimentGroup>
          {(experimentOptions ?? [])
            .filter((experiment) => experiment.active)
            .map((experiment: Experiment) => {
              const experimentIsOn =
                getActiveExperimentVariantForWorkflow(
                  experiment,
                  experimentAssignments ?? [],
                  workflowId,
                ) === 'experimental';

              return (
                <Styled.ExperimentRow key={experiment.label}>
                  <Styled.ExperimentLabel>
                    <Text>{experiment.label}</Text>
                  </Styled.ExperimentLabel>
                  <Toggle
                    defaultChecked={experimentIsOn}
                    label={
                      experimentIsOn
                        ? t('components.CreateAgent.on')
                        : t('components.CreateAgent.off')
                    }
                    onChange={(value: boolean) => handleExperimentChange(value, experiment)}
                    styleOverrides={{
                      label: {
                        margin: 'auto auto',
                      },
                    }}
                  />
                </Styled.ExperimentRow>
              );
            })}
        </Styled.ExperimentGroup>
      </Styled.ExperimentSettings>
    </>
  );
};
