// @ts-nocheck
// TODO: We should remove this after we fix the typing, but doing this for now to help migration
/**
 * Filters keys from an object that do not match the given filter.
 * @param {Record<string | Symbol | number, unknown>} obj
 * @param {(kvPair: [string, unknown]) => boolean} filterFn
 *
 * @return {Record<string | Symbol | number, unknown>}
 *
 * @example
 * ```js
 * const obj = { a: 1, b: 2, c: 3, d: 'str' };
 * const result = filterObject(obj, ([_, value]) => typeof value === 'number' && number >= 2);
 * // returns { b: 2, c: 3 }
 * ```
 */
export const filterObject = (obj, filterFn) => {
  if (typeof obj !== 'object') return {};

  return Object.keys(obj).reduce((resultObj, currKey) => {
    const currValue = obj[currKey];

    if (!filterFn([currKey, currValue])) {
      return resultObj;
    }

    return {
      ...resultObj,
      [currKey]: currValue,
    };
  }, {});
};

export const filterEmptyKeys = (obj) => {
  return filterObject(obj, ([_, value]) => !isEmpty(value));
};

// Lodash's isEmpty interprets `false` as empty, which
//  is not particularly useful, since `false` could be interpreted
//  as _having_ a value (similar to how `0` is a valid value).
export const isEmpty = (value) => {
  return (
    // true/false should be considered non-empty
    typeof value !== 'boolean' &&
    // undefined/null
    (value === 'undefined' ||
      value === null ||
      // object
      Object.keys(value).length === 0 ||
      // array
      (Array.isArray(value) && value.length === 0) ||
      // string
      value?.trim?.().length === 0)
  );
};

export const sortObjectArrByKeyOrder = <T>(
  objArr: T[],
  keyOrder: string[],
  key: keyof T,
): T[] => {
  const orderMap = new Map(keyOrder.map((item, index) => [item, index]));
  const arr = Array.from(objArr);
  return arr.sort((a, b) => {
    const indexA = orderMap.get(a[key] as string) ?? Number.MAX_SAFE_INTEGER;
    const indexB = orderMap.get(b[key] as string) ?? Number.MAX_SAFE_INTEGER;
    return indexA - indexB;
  });
};
