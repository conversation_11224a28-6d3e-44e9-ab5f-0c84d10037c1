import * as React from 'react';
import { Heading, Text, Flex } from '@floqastinc/flow-ui_core';
import CheckCircle from '@floqastinc/flow-ui_icons/material/CheckCircle';
import * as Styled from '../styled';
import { t } from '@/utils/i18n';
import { UploadFile } from '@/svg/UploadFile';
import { Integration } from '@/svg/Integration';

type InputSelectionFormProps = {
  initialInput: string | null;
  setInitialInput: (input: string) => void;
};

type InitialInputCardProps = {
  title: string;
  description: string;
  svg: React.ComponentType;
  active: boolean;
  onClick: () => void;
};

const InitialInputCard = ({
  title,
  description,
  svg: SvgComponent,
  active,
  onClick,
}: InitialInputCardProps) => {
  return (
    <Styled.InitialInputCard active={active} onClick={onClick}>
      <Flex gap={4} direction="column" align="flex-end">
        <Styled.InitialInputCheckmark>
          {active && <CheckCircle color="var(--flo-base-color-core-600)" size="20" />}
        </Styled.InitialInputCheckmark>
        <SvgComponent />
      </Flex>
      <Flex direction="column" gap={4} style={{ height: 'fit-content' }}>
        <Heading variant="h5" weight="medium">
          {t(title)}
        </Heading>
        <Text>{t(description)}</Text>
      </Flex>
    </Styled.InitialInputCard>
  );
};

export const InputSelectionForm = ({ initialInput, setInitialInput }: InputSelectionFormProps) => {
  return (
    <Styled.InputSelectionForm>
      <Flex direction="column" align="center" gap={40} style={{ height: 'fit-content' }}>
        <Flex direction="column" align="center" gap={4}>
          <Heading variant="h5" weight="medium">
            {t('components.Builder.selectInitialInput')}
          </Heading>
          <Text>{t('components.Builder.thisWillEllipsis')}</Text>
        </Flex>
        <Flex gap={32}>
          <InitialInputCard
            title="components.Builder.initialInputUploadFileTitle"
            description="components.Builder.initialInputUploadFileDescription"
            svg={UploadFile}
            active={initialInput === 'upload-file'}
            onClick={() => setInitialInput('upload-file')}
          />
          <InitialInputCard
            title="components.Builder.initialInputIntegrationTitle"
            description="components.Builder.initialInputIntegrationDescription"
            svg={Integration}
            active={initialInput === 'integration'}
            onClick={() => setInitialInput('integration')}
          />
        </Flex>
      </Flex>
    </Styled.InputSelectionForm>
  );
};
