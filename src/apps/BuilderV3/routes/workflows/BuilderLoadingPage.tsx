import Skeleton from 'react-loading-skeleton';
import { <PERSON><PERSON>, <PERSON><PERSON>, Head<PERSON> } from '@floqastinc/flow-ui_core';
import { t } from '@/utils/i18n';
import ChevronLeft from '@floqastinc/flow-ui_icons/material/ChevronLeft';
import { useQuery } from '@tanstack/react-query';
import { sortBy } from 'lodash';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Task } from '@floqastinc/transform-v3';
import * as Styled from './styled';
import { useTaskQueries } from './BuilderPage.hooks';
import { LoadingButton } from '@/components/LoadingButton/LoadingButton';
import { PageTitle } from '@/components/PageTitle';
import { StyledLinkButton, useModal } from '@/components';
import { BUILDER, EXAMPLES, STEPS, V3, AGENTS } from '@/constants';
import { getWorkflowTasksQuery } from '@BuilderV3/api/tasks';
import { getExamplesQuery } from '@BuilderV3/api/examples';

// TEMP: Keeping BuilderPage a little cleaner by keeping the re-navigation logic
// here until we can migrate to a cleaner routing solution
// (i.e. tanstack router/start, react-router 7)
// Currently, the BuilderPage is receiving a `/workflows/:id` url and then
// fetching the tasks and re-navigating after that. This logic is kept here
// instead, along with the fetching of the active exampleSet for that task.
export const BuilderLoadingPage = () => {
  const navigate = useNavigate();
  const { workflowId = '', taskId = '' } = useParams();
  const { openModal } = useModal();

  if (!workflowId) {
    console.error(t('components.Builder.Errors.noWorkflowID'));
    navigate('/builder');
  }

  const { data: tasks } = useQuery({
    ...getWorkflowTasksQuery(workflowId),
    enabled: !taskId,
  });

  const latestTask = tasks ? tasks[tasks.length - 1] : undefined;

  const { data: examples } = useQuery({
    ...getExamplesQuery({ workflowId, taskId: (taskId || latestTask?.id)! }),
    enabled: !!taskId || !!latestTask?.id,
  });

  useEffect(() => {
    if (examples) {
      const activeExample = examples
        ? examples.find((example) => example.status === 'ACTIVE')
        : undefined;
      const latestExample = sortBy(examples, 'updatedAt').reverse()[0];

      const exampleToRouteTo = activeExample ?? latestExample;

      if (examples && !exampleToRouteTo) {
        console.error(
          examples?.length > 0
            ? t('components.Builder.noActiveExampleFound')
            : t('components.Builder.noExamplesFound'),
        );
        navigate('/builder');
      }

      if (workflowId && (taskId || latestTask?.id) && exampleToRouteTo?.id) {
        const taskIdToUse = taskId || latestTask?.id;
        // If taskId is provided in params (i.e. in case of Preview Step or Restore Step)
        // then we default tothat.
        const url = `/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${taskIdToUse}/${EXAMPLES}/${exampleToRouteTo.id}`;
        navigate(url, { replace: true });
      }
    }
  }, [workflowId, latestTask?.id, taskId, examples, navigate]);

  const { createTaskMutation } = useTaskQueries();
  useEffect(() => {
    // If no taskId is provided in the URL and the tasks query is done and there are no tasks,
    // the user needs to create a task.
    if (!taskId && tasks && !tasks.length) {
      openModal('AddTask', {
        onSave: (task: Task) => createTaskMutation.mutate(task),
        shouldNavigateOnCancel: true,
      });
    }
  }, [taskId, tasks]);

  return (
    <Styled.Page>
      <PageTitle title={`Builder`} />
      <Styled.BackButton>
        <StyledLinkButton onClick={() => navigate(`/${BUILDER}/${V3}`)}>
          <ChevronLeft color="var(--flo-base-color-neutral-500)" size={16} />
          {t('generics.back')}
        </StyledLinkButton>
      </Styled.BackButton>
      <Styled.PageHeader>
        <Heading variant="h5" weight="regular">
          <Skeleton />
        </Heading>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Heading variant="h2" weight="semibold">
            <Skeleton />
          </Heading>
        </div>
      </Styled.PageHeader>
      <Styled.ButtonRow>
        <Button color="dark" variant="outlined" disabled onClick={() => {}}>
          {t('generics.downloadFile')}
        </Button>
        <LoadingButton>{t('components.Builder.selectVersion')}</LoadingButton>
        <Button disabled onClick={() => {}}>
          {t('components.Builder.createVersion')}
        </Button>
      </Styled.ButtonRow>
      <Styled.ChatWrapper
        style={{
          border: '1px solid var(--flo-base-color-neutral-300)',
        }}
      />
      <Styled.FilePreview />
      {taskId ||
        (latestTask && (
          <Styled.LoadingCentered>
            <Spinner color="success" size={48} />
          </Styled.LoadingCentered>
        ))}
    </Styled.Page>
  );
};
