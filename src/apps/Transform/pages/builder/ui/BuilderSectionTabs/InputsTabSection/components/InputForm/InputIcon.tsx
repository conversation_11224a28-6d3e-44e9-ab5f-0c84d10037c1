import { DataType } from "@floqastinc/transform-v3";
import Event from '@floqastinc/flow-ui_icons/material/Event';
import { match } from "ts-pattern";
import { FileTable } from "@/svg/FileTable";
import { TextSelectStart } from "@/svg/TextSelectStart";

type InputIconProps = {
  variant: DataType;
};
export const InputIcon = ({ variant }: InputIconProps) => {
  return match(variant)
    .with('TEXT', 'NUMBER', () => <TextSelectStart />)
    .with('DATETIME', () => (
      <Event height={18} width={18} color="var(--flo-sem-color-content-neutral-medium)" />
    ))
    .with('FILE', () => <FileTable />)
    .exhaustive();
};