import React, { useState } from 'react';
import {
  useMutation,
  useQueryClient,
  useSuspenseInfiniteQuery,
} from '@tanstack/react-query';
import { TeamsResponse } from '@floqastinc/transform-v0';
import { useToast, Link as FqLink, Heading } from '@floqastinc/flow-ui_core';

import * as Styled from '../../styled';
import TeamsList from './app-components/TeamsList';
import TeamModal from './app-components/TeamModal';
import v0, { ApiError } from '@/services/v0';
import { APP_CONSTANTS, TEAMS_PAGE_STRINGS } from '@/utils/string';

export const TeamsPage: React.FC = () => {
  const { showToast, Toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [editTeamId, setEditTeamId] = useState<string | null>(null);
  const [teamModalData, setTeamModalData] = useState<{
    name: string;
    externalId?: string;
  } | null>(null);
  const queryClient = useQueryClient();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useSuspenseInfiniteQuery<TeamsResponse, Error>({
      queryKey: ['teams', 'list', { params: { first: 50 } }],
      queryFn: async ({ pageParam = '' }) => {
        const res = await v0.teams.getTeams({
          first: 50,
          after: pageParam as string,
        });
        if (res.errors.length) throw new ApiError(res.errors);
        return res;
      },
      getNextPageParam: (lastPage) => {
        if (!lastPage.data?.length || !lastPage.pageInfo?.hasNextPage) {
          return undefined;
        }
        return lastPage.pageInfo.endCursor;
      },
      initialPageParam: '',
    });

  // Create Team Mutation
  const createTeamMutation = useMutation({
    mutationFn: (newTeam: { name: string; externalId?: string }) =>
      v0.teams.createTeam({ team: newTeam }),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      setIsOpen(false);
      showToast(
        <Toast type={APP_CONSTANTS.SUCCESS}>
          <Toast.Title>{TEAMS_PAGE_STRINGS.TOAST_TITLES.CREATED}</Toast.Title>
          <Toast.Message>
            {`${variables.name}`}{' '}
            {TEAMS_PAGE_STRINGS.TOAST_MESSAGES.CREATE_SUCCESS}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    },
    onError: (error) => {
      console.error(error);
      showToast(
        <Toast type={APP_CONSTANTS.ERROR}>
          <Toast.Title>{TEAMS_PAGE_STRINGS.TOAST_TITLES.ERROR}</Toast.Title>
          <Toast.Message>
            {TEAMS_PAGE_STRINGS.TOAST_MESSAGES.CREATE_ERROR}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    },
  });

  // Edit Team Mutation
  const editTeamMutation = useMutation({
    mutationFn: ({
      teamId,
      name,
      externalId,
    }: {
      teamId: string;
      name: string;
      externalId?: string;
    }) => v0.teams.updateTeam({ teamId, team: { name, externalId } }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      showToast(
        <Toast type={APP_CONSTANTS.SUCCESS}>
          <Toast.Title>{TEAMS_PAGE_STRINGS.TOAST_TITLES.UPDATED}</Toast.Title>
          <Toast.Message>
            {TEAMS_PAGE_STRINGS.TOAST_MESSAGES.UPDATE_SUCCESS}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
      setEditTeamId(null);
      setTeamModalData(null);
    },
    onError: (error) => {
      console.error(error);
      showToast(
        <Toast type={APP_CONSTANTS.ERROR}>
          <Toast.Title>{TEAMS_PAGE_STRINGS.TOAST_TITLES.ERROR}</Toast.Title>
          <Toast.Message>
            {TEAMS_PAGE_STRINGS.TOAST_MESSAGES.UPDATE_ERROR}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    },
  });

  // Delete Team Mutation
  const deleteTeamMutation = useMutation({
    mutationFn: ({ teamId }: { teamId: string; teamName: string }) =>
      v0.teams.deleteTeam({ teamId }),
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      showToast(
        <Toast type={APP_CONSTANTS.SUCCESS}>
          <Toast.Title>{TEAMS_PAGE_STRINGS.TOAST_TITLES.DELETED}</Toast.Title>
          <Toast.Message>
            {`${variables.teamName}`}{' '}
            {TEAMS_PAGE_STRINGS.TOAST_MESSAGES.DELETE_SUCCESS}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    },
    onError: (error) => {
      console.error(error);
      showToast(
        <Toast type={APP_CONSTANTS.ERROR}>
          <Toast.Title>{TEAMS_PAGE_STRINGS.TOAST_TITLES.ERROR}</Toast.Title>
          <Toast.Message>
            {TEAMS_PAGE_STRINGS.TOAST_MESSAGES.DELETE_ERROR}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    },
  });

  // Handlers
  const handleCreateTeam = (name: string, externalId?: string) => {
    createTeamMutation.mutate({ name, externalId });
  };

  const handleEditTeam = (
    teamId: string,
    name: string,
    externalId?: string,
  ) => {
    editTeamMutation.mutate({ teamId, name, externalId });
  };

  const handleDeleteTeam = (teamId: string, teamName: string) => {
    deleteTeamMutation.mutate({ teamId, teamName });
  };

  const teams = data?.pages.flatMap((page) => page.data ?? []) ?? [];

  return (
    <div>
      <Styled.Heading>
        <div>
          <Heading>{TEAMS_PAGE_STRINGS.HEADER_TEXTS.TEAMS_HEADER}</Heading>
          <p>
            {TEAMS_PAGE_STRINGS.ADDITIONAL_FUNCTIONALITY_TEXT}{' '}
            <FqLink
              href="/api/v0"
              target="_blank"
              style={{ display: 'inline' }}
            >
              {TEAMS_PAGE_STRINGS.HEADER_TEXTS.SWAGGER_UI}
            </FqLink>
            .
          </p>
        </div>
        <div>
          <TeamModal
            onSave={handleCreateTeam}
            buttonText={TEAMS_PAGE_STRINGS.BUTTON_TEXTS.CREATE_TEAM}
            modalTitle={TEAMS_PAGE_STRINGS.MODAL_TITLES.CREATE_TEAM}
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            key={isOpen ? 'open' : 'closed'}
          />
        </div>
      </Styled.Heading>
      <TeamsList
        teams={teams}
        fetchNextPage={fetchNextPage}
        hasNextPage={!!hasNextPage}
        isFetchingNextPage={isFetchingNextPage}
        onEditTeam={(teamId, name, externalId) => {
          setEditTeamId(teamId);
          setTeamModalData({ name, externalId });
        }}
        onDeleteTeam={handleDeleteTeam}
      />
      {editTeamId && teamModalData && (
        <TeamModal
          initialData={teamModalData}
          onSave={(name, externalId) =>
            handleEditTeam(editTeamId, name, externalId)
          }
          buttonText={TEAMS_PAGE_STRINGS.BUTTON_TEXTS.EDIT_TEAM}
          modalTitle={TEAMS_PAGE_STRINGS.MODAL_TITLES.EDIT_TEAM}
          isOpen={!!editTeamId}
          setIsOpen={() => setEditTeamId(null)}
        />
      )}
    </div>
  );
};
