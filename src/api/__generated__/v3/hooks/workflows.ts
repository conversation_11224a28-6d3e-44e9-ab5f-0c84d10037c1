/**
 * This code was generated by @basketry/react-query@0.1.0-alpha.9
 *
 * Changes to this file may cause incorrect behavior and will be lost if
 * the code is regenerated.
 *
 * To make changes to the contents of this file:
 * 1. Edit node_modules/@floqastinc/transform-v3/lib/domain.oas3.json
 * 2. Run `npx basketry`
 *
 * About Basketry: https://basketry.io
 * About @basketry/react-query: https://github.com/basketry/react-query#readme
 */

import type {
  CreateWorkflowParams,
  DeepWorkflow,
  DeepWorkflowResponse,
  DeleteWorkflowParams,
  Error,
  GetDeepWorkflowParams,
  GetWorkflowParams,
  GetWorkflowsParams,
  UpdateWorkflowParams,
  Workflow,
  WorkflowResponse,
  WorkflowsResponse,
} from "@floqastinc/transform-v3";
import {
  type InfiniteData,
  queryOptions,
  type UndefinedInitialDataOptions,
  useInfiniteQuery,
  useMutation,
  type UseMutationOptions,
  useQuery,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { useWorkflowService } from "./context";
import {
  applyPageParam,
  assert,
  compact,
  getInitialPageParam,
  getNextPageParam,
  getPreviousPageParam,
  guard,
  type PageParam,
  type QueryError,
} from "./runtime";

/**
 * Create a new workflow
 */
export function useCreateWorkflow(
  options?: Omit<
    UseMutationOptions<Workflow, QueryError<Error[]>, CreateWorkflowParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowService = useWorkflowService();
  return useMutation({
    mutationFn: async (params: CreateWorkflowParams) => {
      const res = await guard(workflowService.createWorkflow(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetDeepWorkflowQueryOptions = (params: GetDeepWorkflowParams) => {
  const workflowService = useWorkflowService();
  return queryOptions<DeepWorkflowResponse, QueryError<Error[]>, DeepWorkflow>({
    queryKey: [`/workflows/${params.workflowId}/deep`],
    queryFn: async () => {
      const res = await guard(workflowService.getDeepWorkflow(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a workflow with all related data. In the future, we will be providing this type of deep data fetching via GraphQL. For this reason, this endpoint is deprecated and will be removed in a future version.
 * @deprecated
 */
export function useDeepWorkflow(
  params: GetDeepWorkflowParams,
  options?: Omit<
    UndefinedInitialDataOptions<DeepWorkflowResponse, QueryError<Error[]>, DeepWorkflow>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetDeepWorkflowQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a workflow with all related data. In the future, we will be providing this type of deep data fetching via GraphQL. For this reason, this endpoint is deprecated and will be removed in a future version.
 * @deprecated
 */
export function useSuspenseDeepWorkflow(
  params: GetDeepWorkflowParams,
  options?: Omit<
    UndefinedInitialDataOptions<DeepWorkflowResponse, QueryError<Error[]>, DeepWorkflow>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetDeepWorkflowQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

/**
 * Delete a workflow
 */
export function useDeleteWorkflow(
  options?: Omit<
    UseMutationOptions<Workflow, QueryError<Error[]>, DeleteWorkflowParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowService = useWorkflowService();
  return useMutation({
    mutationFn: async (params: DeleteWorkflowParams) => {
      const res = await guard(workflowService.deleteWorkflow(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}`] });
      queryClient.invalidateQueries({ queryKey: [`/workflows`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

/**
 * Update a workflow
 */
export function useUpdateWorkflow(
  options?: Omit<
    UseMutationOptions<Workflow, QueryError<Error[]>, UpdateWorkflowParams>,
    "mutationFn"
  >,
) {
  const queryClient = useQueryClient();
  const workflowService = useWorkflowService();
  return useMutation({
    mutationFn: async (params: UpdateWorkflowParams) => {
      const res = await guard(workflowService.updateWorkflow(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      queryClient.invalidateQueries({ queryKey: [`/workflows/${params.workflowId}`] });
      queryClient.invalidateQueries({ queryKey: [`/workflows`] });
      assert(res.data);
      return res.data;
    },
    ...options,
  });
}

const useGetWorkflowQueryOptions = (params: GetWorkflowParams) => {
  const workflowService = useWorkflowService();
  return queryOptions<WorkflowResponse, QueryError<Error[]>, Workflow>({
    queryKey: [`/workflows/${params.workflowId}`],
    queryFn: async () => {
      const res = await guard(workflowService.getWorkflow(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data) => {
      assert(data.data);
      return data.data;
    },
  });
};

/**
 * Get a workflow
 */
export function useWorkflow(
  params: GetWorkflowParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowResponse, QueryError<Error[]>, Workflow>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * Get a workflow
 */
export function useSuspenseWorkflow(
  params: GetWorkflowParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowResponse, QueryError<Error[]>, Workflow>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}

const useGetWorkflowsQueryOptions = (params?: GetWorkflowsParams) => {
  const workflowService = useWorkflowService();
  return queryOptions<WorkflowsResponse, QueryError<Error[]>>({
    queryKey: [
      `/workflows`,
      compact({
        first: params?.first,
        after: params?.after,
        last: params?.last,
        before: params?.before,
        sortOn: params?.sortOn,
        sortOrder: params?.sortOrder,
        search: params?.search,
        status: params?.status,
      }),
    ].filter(Boolean),
    queryFn: async () => {
      const res = await guard(workflowService.getWorkflows(params));
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
  });
};

/**
 * List all workflows
 */
export function useWorkflows(
  params?: GetWorkflowsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowsResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowsQueryOptions(params);
  return useQuery({ ...defaultOptions, ...options });
}

/**
 * List all workflows
 */
export function useSuspenseWorkflows(
  params?: GetWorkflowsParams,
  options?: Omit<
    UndefinedInitialDataOptions<WorkflowsResponse, QueryError<Error[]>>,
    "queryKey" | "queryFn" | "select"
  >,
) {
  const defaultOptions = useGetWorkflowsQueryOptions(params);
  return useSuspenseQuery({ ...defaultOptions, ...options });
}
function useInfiniteWorkflowsQueryOptions(params?: GetWorkflowsParams) {
  const workflowService = useWorkflowService();
  return {
    queryKey: [
      `/workflows`,
      compact({
        sortOn: params?.sortOn,
        sortOrder: params?.sortOrder,
        search: params?.search,
        status: params?.status,
      }),
      { inifinite: true },
    ].filter(Boolean),
    queryFn: async ({ pageParam }: PageParam) => {
      const res = await guard(
        workflowService.getWorkflows(applyPageParam(params ?? {}, pageParam)),
      );
      if (res.errors.length) {
        const handled: QueryError<Error[]> = { kind: "handled", payload: res.errors };
        throw handled;
      }
      return res;
    },
    select: (data: InfiniteData<WorkflowsResponse, string | undefined>) =>
      data.pages.flatMap((page) => page.data),
    initialPageParam: getInitialPageParam(params ?? {}),
    getNextPageParam,
    getPreviousPageParam,
  };
}

/**
 * List all workflows
 */
export const useInfiniteWorkflows = (params?: GetWorkflowsParams) => {
  const options = useInfiniteWorkflowsQueryOptions(params);
  return useInfiniteQuery(options);
};

/**
 * List all workflows
 */
export const useSuspenseInfiniteWorkflows = (params?: GetWorkflowsParams) => {
  const options = useInfiniteWorkflowsQueryOptions(params);
  return useSuspenseInfiniteQuery(options);
};
