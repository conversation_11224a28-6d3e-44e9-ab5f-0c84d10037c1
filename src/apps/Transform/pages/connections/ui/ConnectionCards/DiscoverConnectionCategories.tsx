import { useMemo } from 'react';
import { Styled_ContentContainer } from '../../styles/container.styles';
import { useConnections } from '../../hooks/useConnections';
import { ConnectionCategory } from './ConnectionCategory';

interface DiscoverConnectionCategoriesProps {
  searchTerm: string;
}

export const DiscoverConnectionCategories = ({ searchTerm }: DiscoverConnectionCategoriesProps) => {
  const { availableConnections } = useConnections();

  const filteredCategories = useMemo(() => {
    if (!searchTerm) return availableConnections;

    return availableConnections
      .map((category) => ({
        ...category,
        providers: category.providers.filter((provider) =>
          provider.name.label.toLowerCase().includes(searchTerm.toLowerCase()),
        ),
      }))
      .filter((category) => category.providers.length > 0);
  }, [availableConnections, searchTerm]);

  return (
    <Styled_ContentContainer>
      {filteredCategories.map((category) => {
        return (
          <ConnectionCategory
            providers={category.providers}
            categoryName={category.categoryName}
            key={category.categoryName.key}
          />
        );
      })}
    </Styled_ContentContainer>
  );
};
