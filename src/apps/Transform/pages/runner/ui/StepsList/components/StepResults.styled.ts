import { Card, Text } from '@floqastinc/flow-ui_core';
import { styled } from 'styled-components';

export const StepResultsCard = styled(Card)<object>`
  background-color: transparent;
  border: 1px solid var(--flo-base-color-neutral-500);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 12px;
  width: 100%;
`;

export const FileWrapper = styled.div<object>`
  margin-bottom: 6px;
`;

export const FileContainer = styled.div`
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 8px;
  display: flex;
  height: 32px;
  align-items: center;
  padding: 3px 8px;
  gap: 8px;
  width: fit-content;
  max-width: 100%;
  transition: width 0.3s ease-in-out;
  background-color: var(--flo-base-color-neutral-0);
`;

export const IconContainer = styled.div`
  width: 20px;
  height: 20px;
  flex-shrink: 0;
`;

export const TextContainer = styled.div`
  padding: 0px 2px 0px 0px;
`;

export const Name = styled(Text)<object>`
  color: var(--flo-base-color-neutral-800);
  font-size: var(--flo-base-font-size-5);
  font-weight: var(--flo-base-font-weight-5);
  line-height: var(--flo-base-line-height-4);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const NameContainer = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  padding: 0px 0px 6px 0px;
`;

export const RunByContainer = styled.div`
  display: flex;
  gap: 6px;
  align-items: center;
  width: fit-content;
`;

export const RunDateContainer = styled.div`
  display: flex;
  align-items: center;
  position: relative;
  width: fit-content !important;
  min-width: 'max-content';
`;
