import { t } from '@/utils/i18n';
import { RunStatus as TaskRunStatus } from '@floqastinc/transform-v3';
import { Button } from '@floqastinc/flow-ui_core';
import Pause from '@floqastinc/flow-ui_icons/material/Pause';
import CheckCircle from '@floqastinc/flow-ui_icons/material/CheckCircle';
import Close from '@floqastinc/flow-ui_icons/material/Close';
import * as Styled from '../Step.styled';

interface ReviewStepProps {
  isSelected: boolean;
  name: string;
  onClick: () => void;
  onRejectClick: () => void;
  onApproveClick: () => void;
  isFinalStep?: boolean;
  status?: TaskRunStatus;
}

export const ReviewStep = (props: ReviewStepProps) => {
  const { isFinalStep, isSelected, name, status, onClick, onRejectClick, onApproveClick } = props;

  const isReview = status === 'REVIEW';
  const reviewCompleted = status === 'COMPLETED' || status === 'REJECTED';

  let icon = null;
  switch (status) {
    case 'REVIEW':
      icon = <Pause aria-label="Pause Icon" color="var(--flo-sem-color-white)" size={24} />;
      break;
    case 'COMPLETED':
      icon = (
        <CheckCircle aria-label="Green Check Icon" color="var(--flo-sem-color-success)" size={24} />
      );
      break;
    case 'REJECTED':
      icon = <Close aria-label="Red X Icon" color="var(--flo-sem-color-danger)" size={24} />;
      break;
    default:
      break;
  }

  const Card = reviewCompleted ? Styled.StepCard : Styled.ReviewStepCard;
  const cardTestId = `${name}-${Card === Styled.StepCard ? 'step-card' : 'review-step-card'}`;

  // TODO: Should add logic for determining error state and displaying error info like the base Step
  return (
    <Card
      data-testid={cardTestId}
      isSelected={isSelected}
      error={false}
      onClick={onClick}
      role="tab"
      aria-selected={isSelected}
    >
      <Styled.StepDetailsContainer>
        {icon && <Styled.IconContainer>{icon}</Styled.IconContainer>}
        <Styled.Name
          isSelected={isSelected}
          showButton={false}
          status={reviewCompleted ? '' : 'REVIEW'}
        >
          {name}
        </Styled.Name>
      </Styled.StepDetailsContainer>
      {isReview && (
        <Styled.ButtonContainer>
          <Styled.RejectButton
            data-testid="reject-run-button"
            color={'danger'}
            variant={'ghost'}
            onClick={onRejectClick}
          >
            {t('components.Runner.rejectRun')}
          </Styled.RejectButton>
          <Button
            data-testid={isFinalStep ? 'approve-run-button' : 'resume-run-button'}
            color={'primary'}
            variant={'filled'}
            onClick={onApproveClick}
          >
            {isFinalStep ? t('components.Runner.approveRun') : t('components.Runner.resumeRun')}
          </Button>
        </Styled.ButtonContainer>
      )}
    </Card>
  );
};
