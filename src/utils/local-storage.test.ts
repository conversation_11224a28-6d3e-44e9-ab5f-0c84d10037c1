import { afterEach, describe, expect, it } from 'vitest';
import { LocalStorage } from './local-storage';

describe('LocalStorage', () => {
  afterEach(() => {
    window.localStorage.clear();
  });

  describe('findAllByPrefix', () => {
    it('should return all keys with the specified prefix', () => {
      window.localStorage.setItem(
        'chatMessage-test-workflow-test-task-test-example-set',
        JSON.stringify({
          value: 'Hello, world!',
          timestamp: 1234567890,
        }),
      );
      window.localStorage.setItem('other-key', 'test');

      const prefix = 'chatMessage-';
      const result = LocalStorage.findAllByPrefix(prefix);

      expect(result).toEqual({
        'chatMessage-test-workflow-test-task-test-example-set': {
          value: 'Hello, world!',
          timestamp: 1234567890,
        },
      });
    });

    it('should return an empty object if no keys match the prefix', () => {
      window.localStorage.setItem(
        'chatMessage-test-workflow-test-task-test-example-set',
        JSON.stringify({
          value: 'Hello, world!',
          timestamp: 1234567890,
        }),
      );

      const prefix = 'nonexistent-';
      const result = LocalStorage.findAllByPrefix(prefix);
      expect(result).toEqual({});
    });
  });
});
