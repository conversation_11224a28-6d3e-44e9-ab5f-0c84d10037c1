import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import duplicateJemTemplate from './duplicate-jem-template';
import v3, { ApiError } from '@/services/v3';

vi.mock('@/services/v3', () => {
  return {
    __esModule: true,
    default: {
      jemTemplate: {
        getJemTemplate: vi.fn(),
      },
    },
    ApiError: vi.fn(),
  };
});

describe('duplicateJemTemplate', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.mocked(console.error).mockRestore();
  });

  it('should not call getJemTemplate if taskId is missing', async () => {
    await duplicateJemTemplate('', 'exampleSetId', 'workflowId');

    expect(vi.mocked(v3.jemTemplate.getJemTemplate)).not.toHaveBeenCalled();
    expect(console.error).toHaveBeenCalledWith('Task ID or Example Set ID is missing.');
  });

  it('should not call getJemTemplate if exampleSetId is missing', async () => {
    await duplicateJemTemplate('taskId', '', 'workflowId');

    expect(vi.mocked(v3.jemTemplate.getJemTemplate)).not.toHaveBeenCalled();
    expect(console.error).toHaveBeenCalledWith('Task ID or Example Set ID is missing.');
  });

  it('should call getJemTemplate with correct params', async () => {
    vi.mocked(v3.jemTemplate.getJemTemplate).mockResolvedValueOnce({} as any);

    await duplicateJemTemplate('taskId', 'exampleSetId', 'workflowId');

    expect(vi.mocked(v3.jemTemplate.getJemTemplate)).toHaveBeenCalledWith({
      workflowId: 'workflowId',
      taskId: 'taskId',
      exampleSetId: 'exampleSetId',
    });
  });

  it('should throw ApiError if jemTemplate.errors exists', async () => {
    vi.mocked(v3.jemTemplate.getJemTemplate).mockResolvedValueOnce({
      errors: ['Some error'],
    } as any);

    await duplicateJemTemplate('taskId', 'exampleSetId', 'workflowId');

    expect(ApiError).toHaveBeenCalledWith(['Some error']);
    expect(console.error).toHaveBeenCalledWith(
      'Error fetching JEM Template:',
      expect.any(ApiError),
    );
  });

  it('should handle exceptions thrown by getJemTemplate', async () => {
    const error = new Error('Network error');
    vi.mocked(v3.jemTemplate.getJemTemplate).mockRejectedValueOnce(error);

    await duplicateJemTemplate('taskId', 'exampleSetId', 'workflowId');

    expect(console.error).toHaveBeenCalledWith('Error fetching JEM Template:', error);
  });
});
