import styled from 'styled-components';

export const InputCheckbox = styled.div`
  margin-right: 8px;
  margin-top: 12px;
`;

type ChatWindowProps = {
  $isDragActive: boolean;
};
// position: relative for allowing progress loader overlay as absolute
// Height:
// - 3px - top border
// - 48px - total top and bottom padding
export const ChatWindow = styled.div<ChatWindowProps>`
  position: relative;
  display: grid;
  grid-area: chat;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr auto;
  align-items: start;
  gap: 12px;
  padding: 24px;
  border-top: solid var(--flo-base-color-neutral-300);
  height: calc(100% - 3px - 48px);
  border: ${(props) => props.$isDragActive && '1px dashed var(--flo-base-color-blue-800)'};
  background: ${(props) => props.$isDragActive && 'var(--flo-sem-color-info-background)'};
`;

export const QuickReply = styled.div`
  display: grid;
  grid-auto-flow: column;
  gap: 8px;
`;

export const Centered = styled.div`
  display: grid;
  place-items: center;
  width: 100%;
  height: 100%;
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(240, 240, 240, 0.5);
`;

export const Messages = styled.div<{ $leaveSpaceForChatBox?: boolean }>`
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: scroll;
  height: 100%;
`;

export const ChatBox = styled.div`
  display: grid;
  border: 1px solid var(--flo-base-color-neutral-300);
  border-radius: 8px;
  gap: 0;
`;

export const TextArea = styled.textarea`
  border: none;
  outline: none;
  overflow: auto;
  box-shadow: none;
  resize: none;
  font-size: 14px;
  font-family: Inter;
  font-weight: 300;
  padding: 12px 8px;

  &::placeholder {
    color: var(--flo-base-color-neutral-400);
  }
`;

export const TextAreaActions = styled.div`
  display: flex;
  justify-content: space-between;
  padding-right: 8px;
  padding-bottom: 8px;
`;

export const TextAreaActionsRight = styled.div`
  display: flex;
  gap: 2px;
`;
