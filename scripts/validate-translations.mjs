import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Valid translation key patterns
const VALID_KEY_PATTERNS = [
  /^components\.[A-Za-z]+\.[A-Za-z]+(\.[A-Za-z]+)*$/,  // components.ComponentName.key
  /^generics\.[A-Za-z]+$/,                             // generics.key
  /^common\.[A-Za-z]+(\.[A-Za-z]+)*$/                  // common.key
];

function isValidTranslationKey(key) {
  return VALID_KEY_PATTERNS.some(pattern => pattern.test(key));
}

function getUsedTranslationKeys() {
  try {
    const command = 'grep -r "t([\'\\"]" --include="*.tsx" --include="*.ts" src/';
    const output = execSync(command, { encoding: 'utf8' });
    const keys = new Set();
    
    // Extract keys from the output
    const keyRegex = /t\(['"]([^'"]+)['"]\)/g;
    let match;
    while ((match = keyRegex.exec(output)) !== null) {
      const key = match[1];
      if (isValidTranslationKey(key)) {
        keys.add(key);
      }
    }
    
    return Array.from(keys);
  } catch (error) {
    console.error('Error finding translation keys:', error.message);
    return [];
  }
}

function getTranslationKeys() {
  const translationFile = path.join(__dirname, '../src/locales/en/translation.json');
  const content = JSON.parse(fs.readFileSync(translationFile, 'utf8'));
  
  function extractKeys(obj, prefix = '') {
    return Object.entries(obj).reduce((keys, [key, value]) => {
      const newKey = prefix ? `${prefix}.${key}` : key;
      if (typeof value === 'object' && value !== null) {
        return [...keys, ...extractKeys(value, newKey)];
      }
      return [...keys, newKey];
    }, []);
  }
  
  return extractKeys(content);
}

function validateTranslations() {
  const usedKeys = getUsedTranslationKeys();
  const translationKeys = getTranslationKeys();
  
  const missingKeys = usedKeys.filter(key => !translationKeys.includes(key));
  
  if (missingKeys.length > 0) {
    console.error(`\n✖ npm run validate-translations:\n${missingKeys.length} missing translation keys:`);
    missingKeys.forEach(key => console.error(`  - ${key}`));
    process.exit(1);
  }
  
  console.log('All translation keys are present!');
}

validateTranslations(); 