export const INTEGRATION_TABLE_DATA: Record<string, string[]> = {
  coupa: [
    'Invoices',
    'Purchase Orders',
    'Suppliers',
    'Expense Reports',
    'Virtual Cards',
    'Requisitions',
    'Contracts',
    'Approvals',
    '& More!',
  ],
  netsuiteenhanced: [
    'Transactions',
    'Periods',
    'Accounts',
    'Locations',
    'Classes',
    'Departments',
    'Books',
    'Schedules',
    'Mappings',
    '& More!',
  ],
  intacctenhanced: [
    'Accounts',
    'Details',
    'Contracts',
    'AR Details',
    'AP Payments',
    'Users',
    '& More!',
  ],
};

export const getIntegrationTableData = (
  integrationSystem: string,
): string[] => {
  const normalizedSystem = integrationSystem.toLowerCase();
  return INTEGRATION_TABLE_DATA[normalizedSystem] || [];
};
