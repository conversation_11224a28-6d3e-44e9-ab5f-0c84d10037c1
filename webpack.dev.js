const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const { merge } = require('webpack-merge');

const common = require('./webpack.common.js');

if (!process.env.REACT_APP_DEVELOPMENT_AUTHORIZATION) {
  throw new Error(
    'REACT_APP_DEVELOPMENT_AUTHORIZATION environment variable is required for local development. Please set this in your shell.',
  );
}

module.exports = merge(common, {
  mode: 'development',
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers':
        'x-fq-intl-vars, content-type, Authorization',
    },
    historyApiFallback: {
      rewrites: [
        { from: /^\/$/, to: '/index.html' },
        { from: /^\/auth/, to: '/auth.html' },
      ],
    },
    open: ['/transform'],
    static: {
      directory: path.resolve(__dirname, 'dist'),
    },
    port: 8005,
    hot: true,
    proxy: [
      {
        context: ['/api'],
        target: 'http://localhost:8080/',
        pathRewrite: { '^/api': '' },
        secure: true,
        changeOrigin: true,
      },
    ],
  },
  devtool: 'inline-source-map',
  plugins: [
    new MiniCssExtractPlugin({
      filename: '[name].css',
    }),
    // The .env.development.local file doesn't work with the dotenv-webpack plugin
    // so we have to use the DefinePlugin to set the environment variables directly.
    // Define these as normal env variables in your shell.
    // Example: export REACT_APP_DEVELOPMENT_AUTHORIZATION=<EMAIL>
    new webpack.DefinePlugin({
      'process.env.REACT_APP_DEVELOPMENT_AUTHORIZATION': JSON.stringify(
        process.env.REACT_APP_DEVELOPMENT_AUTHORIZATION,
      ),
    }),
  ],
});
