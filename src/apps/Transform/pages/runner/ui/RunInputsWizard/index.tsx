import { useState } from 'react';
import {
  Wizard,
  FileUpload,
  TextArea,
  Input,
  CalendarSelectBox,
  DropdownButton,
  Flex,
} from '@floqastinc/flow-ui_core';
import { WorkflowInput } from '@floqastinc/transform-v3';
import { match, P } from 'ts-pattern';
import DateRange from '@floqastinc/flow-ui_icons/material/DateRange';
import * as Styled from './styles';
import { t } from '@/utils/i18n';
import { InputValue } from '@Transform/shared/types';

type FileUploadStepProps = {
  onChange: (file: File) => void;
  onDelete: () => void;
};
const FileUploadStep = ({ onChange, onDelete }: FileUploadStepProps) => {
  const [file, setFile] = useState<File | null>(null);

  return (
    <FileUpload
      onChange={(files: File[]) => {
        setFile(files[0]);
        onChange(files[0]);
      }}
      allowedFileTypes={'.csv,.xlsx'}
    >
      <FileUpload.DropZone>
        <FileUpload.UploadButton>{t('components.Runner.uploadFile')}</FileUpload.UploadButton>
      </FileUpload.DropZone>
      <FileUpload.FileList>
        {file ? (
          <FileUpload.File
            file={file}
            onDelete={() => {
              setFile(null);
              onDelete();
            }}
          />
        ) : null}
      </FileUpload.FileList>
    </FileUpload>
  );
};

const displayDate = (date: Date) => {
  // check date for invalid date
  if (isNaN(date.getTime())) {
    return undefined;
  }

  // valid date. return formatted date
  return date.toISOString().split('T')[0];
};

const getStepTitle = (input: WorkflowInput) =>
  match(input.type)
    .with('FILE', () => t('components.Runner.uploadInput', { name: input.name }))
    .with(P.union('TEXT', 'NUMBER', 'DATETIME'), () =>
      t('components.Runner.enterInput', { name: input.name }),
    )
    .otherwise(() => t('components.Runner.unsupportedInputType'));
type RunInputsWizardProps = {
  inputs: WorkflowInput[];
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onCompletion: (inputs: { [id: string]: InputValue }) => void;
};
export const RunInputsWizard = ({
  inputs,
  isOpen,
  onOpenChange,
  onCompletion,
}: RunInputsWizardProps) => {
  const [activeStep, setActiveStep] = useState(0);
  const [runInputs, setRunInputs] = useState<{ [id: string]: InputValue }>({});
  const [invalidInput, setInvalidInput] = useState<boolean>(false);
  const today = new Date();
  const [calendarOpenStates, setCalendarOpenStates] = useState<Record<string, boolean>>({});
  const [activePeriods, setActivePeriods] = useState<
    Record<string, { month: number; year: number }>
  >({});
  const maxDate = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate())
    .toISOString()
    .slice(0, 10);
  const minDate = new Date(today.getFullYear() - 5, today.getMonth(), today.getDate())
    .toISOString()
    .slice(0, 10);

  const isNextDisabled =
    runInputs[inputs[activeStep]?.id] === undefined ||
    invalidInput ||
    runInputs[inputs[activeStep]?.id].value === '' ||
    (runInputs[inputs[activeStep]?.id].value === '-' &&
      runInputs[inputs[activeStep]?.id].type === 'NUMBER');

  const isNumber = (num: unknown) => {
    if (typeof num === 'number') {
      return num - num === 0;
    }
    if (typeof num === 'string' && num.trim() !== '') {
      return Number.isFinite ? Number.isFinite(+num) : isFinite(+num);
    }
    return false;
  };

  return (
    <Wizard
      activeStep={activeStep}
      onCompletion={() => {
        onOpenChange(false);
        onCompletion(runInputs);
        setActiveStep(0);
        setRunInputs({});
      }}
      onStepChange={(step: number) => {
        setActiveStep(step);
      }}
      propOverrides={{
        modalRoot: {
          onOpenChange: (isOpen: boolean) => {
            if (!isOpen) {
              setActiveStep(0);
            }
            onOpenChange(isOpen);
          },
          open: isOpen,
        },
        modalHeader: {
          children: t('components.Runner.addInputsToRunAgent'),
        },
        modalFooter: {
          style: {
            justifyContent: 'end',
          },
        },
      }}
    >
      <Wizard.Trigger></Wizard.Trigger>
      <Wizard.Steps>
        {inputs.map((input, i) => {
          return (
            <Wizard.Step
              key={input.id}
              title={t('components.Runner.uploadInputFile', { name: input.name })}
              subtitle={t('components.Runner.stepOfInput', { i: i + 1, inputs: inputs.length })}
            >
              <Wizard.StepSubtitle>
                {t('components.Runner.stepOfInput', { i: i + 1, inputs: inputs.length })}
              </Wizard.StepSubtitle>
              <Wizard.StepTitle>{getStepTitle(input)}</Wizard.StepTitle>
              <Wizard.StepContent>
                {match(input.type)
                  .with('FILE', () => (
                    <FileUploadStep
                      onChange={(file) => {
                        setRunInputs({
                          ...runInputs,
                          [input.id]: {
                            ...input,
                            kind: 'FILE',
                            value: file,
                          },
                        });
                      }}
                      onDelete={() => {
                        const newInputs = { ...runInputs };
                        delete newInputs[input.id];
                        setRunInputs(newInputs);
                      }}
                    />
                  ))
                  .with('TEXT', () => (
                    <TextArea
                      actions={[]}
                      isLabelVisible
                      label={t('components.Runner.enterText')}
                      value={runInputs[input.id]?.value}
                      onChange={(text: string) => {
                        setRunInputs({
                          ...runInputs,
                          [input.id]: {
                            ...input,
                            kind: 'TEXT',
                            value: text,
                          },
                        });
                      }}
                    />
                  ))
                  .with('NUMBER', () => (
                    <Input
                      type="text"
                      label={t('components.Runner.enterNumber')}
                      value={runInputs[input.id]?.value || ''}
                      onChange={(num: number) => {
                        if (num && num !== '-' && !isNumber(num)) {
                          return;
                        }
                        setRunInputs({
                          ...runInputs,
                          [input.id]: {
                            ...input,
                            kind: 'NUMBER',
                            value: parseFloat(num) || num || '', // Store null for empty input
                          },
                        });
                      }}
                    />
                  ))
                  .with('DATETIME', () => (
                    <Flex
                      inline
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                    >
                      <CalendarSelectBox
                        activePeriod={
                          activePeriods[input.id] || {
                            month: today.getMonth() + 1,
                            year: today.getFullYear(),
                          }
                        }
                        maximumDate={maxDate}
                        minimumDate={minDate}
                        onChange={(date: string) => {
                          setInvalidInput(!date);
                          const newDate = date ? new Date(date) : undefined;
                          setRunInputs({
                            ...runInputs,
                            [input.id]: {
                              ...input,
                              kind: 'DATETIME',
                              value: newDate,
                            },
                          });
                        }}
                        onMonthChange={(newPeriod: { month: number; year: number }) => {
                          setActivePeriods({
                            ...activePeriods,
                            [input.id]: newPeriod,
                          });
                        }}
                        onOpenChange={(isOpen: boolean) => {
                          setCalendarOpenStates({
                            ...calendarOpenStates,
                            [input.id]: isOpen,
                          });
                        }}
                        open={calendarOpenStates[input.id] || false}
                        trigger={
                          <DropdownButton
                            open={calendarOpenStates[input.id] || false}
                            onClick={() => {}}
                            style={{ width: '160px', height: '40px' }}
                          >
                            <Styled.ButtonText>
                              <DateRange
                                color="var(--flo-sem-color-icon-primary)"
                                style={{ opacity: 0.8 }}
                                size={20}
                              />
                              {match(runInputs[input.id])
                                .with({ kind: 'DATETIME' }, (dateInput) =>
                                  dateInput.value ? displayDate(dateInput.value) : undefined,
                                )
                                .otherwise(() => undefined) || 'Select a date'}
                            </Styled.ButtonText>
                          </DropdownButton>
                        }
                        value={match(runInputs[input.id])
                          .with({ kind: 'DATETIME' }, (dateInput) =>
                            dateInput.value ? displayDate(dateInput.value) : undefined,
                          )
                          .otherwise(() => undefined)}
                        styleOverrides={{
                          content: {
                            zIndex: 9001,
                          },
                        }}
                      />
                    </Flex>
                  ))
                  .exhaustive()}
              </Wizard.StepContent>
            </Wizard.Step>
          );
        })}
      </Wizard.Steps>
      <Wizard.PreviousButton hidden={activeStep === 0} />
      <Wizard.NextButton disabled={isNextDisabled}>
        {activeStep === inputs.length - 1 ? t('generics.finish') : t('generics.continue')}
      </Wizard.NextButton>
    </Wizard>
  );
};
