import v3 from '@/services/v3';
import {
  ArgumentValue,
  CreateWorkflowInputParams,
  UpdateWorkflowInputParams,
  WorkflowInput,
} from '@floqastinc/transform-v3';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { setWorkflowInputValue } from '@Transform/api/set-workflow-input-value';

export type UseUpdateWorkflowInputAndSetValueInput = UpdateWorkflowInputParams & {
  input: CreateWorkflowInputParams['input'] & {
    value: ArgumentValue;
  };
};

/**
 * Custom hook to update a workflow input and set its value.
 */
export const useUpdateWorkflowInputAndSetValue = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (args: UseUpdateWorkflowInputAndSetValueInput) => {
      const inputResponse = await v3.workflowInputs.updateWorkflowInput(args);

      if (inputResponse.errors?.length) {
        throw new Error(`Failed to update workflow input: ${inputResponse.errors.join(', ')}`);
      }

      if (!inputResponse?.data?.id) {
        throw new Error('Unexpected error: no data returned from updateWorkflowInput');
      }

      await setWorkflowInputValue({
        workflowId: args.workflowId,
        workflowInputId: inputResponse.data.id,
        input: args.input,
      });

      return inputResponse.data;
    },
    onMutate(input) {
      const queryData = queryClient.getQueryData([`/workflows/${input.workflowId}/inputs`]) as {
        data: WorkflowInput[];
      };

      if (queryData?.data) {
        queryClient.setQueryData([`/workflows/${input.workflowId}/inputs`], {
          data: queryData.data.map((inputItem) => {
            if (inputItem.id === input.workflowInputId) {
              return {
                ...inputItem,
                ...input.input,
              };
            }
            return inputItem;
          }),
        });
      }
      return { previousInputs: queryData?.data };
    },
    onError: async (error, { workflowId }, context) => {
      if (context?.previousInputs) {
        queryClient.setQueryData([`/workflows/${workflowId}/inputs`], {
          data: context?.previousInputs,
        });
      }
      console.error('Error updating input:', error);
    },
    onSettled: (_data, _error, { workflowId }) => {
      queryClient.invalidateQueries({ queryKey: [`/workflows/${workflowId}/inputs`] });
    },
  });
};
