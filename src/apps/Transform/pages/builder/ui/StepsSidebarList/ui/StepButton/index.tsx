import { Skeleton } from '@floqastinc/flow-ui_core'
import * as Styled from './index.styles';

type StepButtonProps = {
  children: React.ReactNode;
  onClick: () => void;
  isActive: boolean;
};
export const StepButton = ({ children, onClick, isActive }: StepButtonProps) => {
  return (
    <Styled.StepContainer>
      <Styled.StepButton onClick={onClick} $isActive={isActive}>
        {children}
      </Styled.StepButton>
    </Styled.StepContainer>
  );
};

const Pending = () => {
  return (
    <Styled.StepContainer>
      <Styled.SkeletonStepButton>
        <Skeleton variant="rectangle" height="100%" width="100%" />
      </Styled.SkeletonStepButton>
    </Styled.StepContainer>
  )
}

StepButton.Pending = Pending;
