name: Build
run-name: Deploy branch ${{ github.ref_name }} to ${{ inputs.environment }} by @${{ github.actor }}

on:
  pull_request:
    branches: main
    types:
      - opened
      - edited
      - closed
      - reopened
      - labeled
      - unlabeled
      - synchronize
  pull_request_review:
  push:
    branches:
      - '*-fq*'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: false
        type: choice
        options:
        - automation
        - automation-eu
        - automation-all
        - dev
        - fq1
        - fq2
        - fq4
        - fq5
        - fq6
        - fq7
        - fq8
        - fq9
        - fq10
        - fq11
        - fq12
        - production
        - production-eu
        - production-au
        - production-bug-bounty
      

jobs:
  BuildandDeployReactClient:
    uses: FloQastInc/.github/.github/workflows/react-client.yaml@main
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}


