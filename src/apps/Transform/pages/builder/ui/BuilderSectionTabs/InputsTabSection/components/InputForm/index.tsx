import {
  Card,
  Text,
  Button,
  Input,
  InputWrapper,
  DropdownPanel,
  CalendarSelectBox,
  DropdownButton,
} from '@floqastinc/flow-ui_core';
import DateRange from '@floqastinc/flow-ui_icons/material/DateRange';
import { match } from 'ts-pattern';
import { DataType, WorkflowInput } from '@floqastinc/transform-v3';
import { useEffect, useRef, useState } from 'react';
import { FileInput } from '../FileInput';
import * as Styled from './index.styles';
import { InputIcon } from './InputIcon';
import { t } from '@/utils/i18n';

export type InputFormData = Pick<WorkflowInput, 'name' | 'description' | 'type'> & {
  value: string | File | null | undefined;
};
type InputFormProps = {
  title?: string;
  input?: Partial<InputFormData>;
  inputTypeSelectDisabled?: boolean;
} & (
  | {
      mode: 'create' | 'edit';
      onSave: (input: InputFormData) => void;
      onChange: (input: Partial<InputFormData>) => void;
      onCancel: () => void;
    }
  | {
      mode: 'readonly';
    }
);

export const InputForm = (props: InputFormProps) => {
  // TODO: Make title prop a bit cleaner: there should be a better
  //  way of disambiguating a new input form and an editing input form
  //  (and a readonly form also)
  const { title, input, mode, inputTypeSelectDisabled } = props;
  const titleInputRef = useRef<HTMLInputElement>(null);
  const [showInputTypeDropdown, setShowInputTypeDropdown] = useState(false);
  const [selectedInputType, setSelectedInputType] = useState<DataType>(props.input?.type ?? 'FILE');
  const inputTypeDisplayText = match(selectedInputType)
    .with('TEXT', () => 'Text')
    .with('DATETIME', () => 'Date')
    .with('FILE', () => 'File')
    .with('NUMBER', () => 'Number')
    .exhaustive();

  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const today = new Date();
  const [activePeriod, setActivePeriod] = useState({
    month: today.getMonth() + 1,
    year: today.getFullYear(),
  });

  const inputsAreValid = !!input?.name && !!selectedInputType && !!input?.value;
  const isCreateMode = mode === 'create';
  const isEditMode = mode === 'edit';
  const isFormActive = isCreateMode || isEditMode;

  const onDelete = () => {
    // TODO: depends oninputs editing, change detection, and global inputs
    if (isFormActive) props.onCancel();
  };

  useEffect(() => {
    setSelectedInputType(props.input?.type ?? 'FILE');
  }, [props.input?.type]);

  useEffect(() => {
    if (isFormActive && titleInputRef.current) {
      titleInputRef.current.focus();
    }
  }, [mode, isFormActive]);

  return (
    <Card style={{ width: '100%' }}>
      <Styled.CardContent>
        <Styled.Header>
          <Text weight={5}>
            {title || input?.name || t('components.Builder.GlobalInputs.newInput')}
          </Text>
          {isFormActive ? (
            <Styled.HeaderActions>
              <Button
                variant="ghost"
                color="dark"
                size="sm"
                onClick={() => {
                  if (isFormActive) props.onCancel();
                }}
              >
                {t('components.Builder.cancel')}
              </Button>
              <Button
                variant="outlined"
                color="dark"
                size="sm"
                disabled={!inputsAreValid}
                onClick={() => {
                  if (isFormActive && inputsAreValid)
                    props.onSave({
                      name: input?.name || '',
                      description: input.description,
                      type: selectedInputType,
                      value: input.value,
                    });
                }}
                data-testid="save-input-button"
              >
                {t('components.Builder.save')}
              </Button>
            </Styled.HeaderActions>
          ) : null}
        </Styled.Header>
        <Styled.Body>
          <Input
            inputRef={titleInputRef}
            label={t('components.Builder.title')}
            placeholder={t('components.Builder.enterTitle')}
            value={input?.name}
            onChange={(name: string) => {
              if (isFormActive)
                props.onChange({
                  name,
                });
            }}
            disabled={mode === 'readonly'}
          />
          <Input
            label={t('components.Builder.description')}
            placeholder={t('components.Builder.enterDescription')}
            value={input?.description}
            onChange={(description: string) => {
              if (isFormActive)
                props.onChange({
                  description,
                });
            }}
            disabled={mode === 'readonly'}
          />
          <InputWrapper style={{ width: '100%' }}>
            <DropdownPanel
              aria-label={t('components.Builder.inputType')}
              disableClear={true}
              disableFilter={true}
              selectionMode="single"
              selectedValues={selectedInputType}
              open={showInputTypeDropdown}
              onChange={(inputType: DataType) => {
                setSelectedInputType(inputType);
                setShowInputTypeDropdown(false);
              }}
              onOpenChange={setShowInputTypeDropdown}
            >
              <DropdownPanel.Trigger>
                <Styled.InputFormatButton
                  as="button"
                  className={inputTypeSelectDisabled ? 'disabled' : ''}
                  disabled={mode === 'readonly' || inputTypeSelectDisabled}
                >
                  <InputIcon variant={selectedInputType} />
                  <Text>{inputTypeDisplayText}</Text>
                </Styled.InputFormatButton>
              </DropdownPanel.Trigger>
              <DropdownPanel.Content>
                <DropdownPanel.Option value="FILE">
                  <InputIcon variant="FILE" />
                  {t('components.Builder.file')}
                </DropdownPanel.Option>
                <DropdownPanel.Option value="DATETIME">
                  <InputIcon variant="DATETIME" />
                  {t('components.Builder.date')}
                </DropdownPanel.Option>
                <DropdownPanel.Option value="TEXT">
                  <InputIcon variant="TEXT" />
                  {t('components.Builder.text')}
                </DropdownPanel.Option>
              </DropdownPanel.Content>
            </DropdownPanel>
            <InputWrapper.Label
              style={{
                fontWeight: 'var(--flo-base-font-weight-4)',
              }}
            >
              {t('components.Builder.requestedFormat')}
            </InputWrapper.Label>
          </InputWrapper>
          <InputWrapper style={{ width: '100%' }}>
            {match(selectedInputType)
              .with('FILE', () => (
                <FileInput
                  onChange={(file) => {
                    if (isFormActive)
                      props.onChange({
                        value: file,
                      });
                  }}
                />
              ))
              .with('DATETIME', () => (
                <CalendarSelectBox
                  activePeriod={activePeriod}
                  onChange={(date: string) => {
                    if (isFormActive) {
                      props.onChange({
                        value: date,
                      });
                    }
                    setIsCalendarOpen(false);
                  }}
                  onMonthChange={setActivePeriod}
                  onOpenChange={setIsCalendarOpen}
                  open={isCalendarOpen}
                  trigger={
                    <DropdownButton open={isCalendarOpen} onClick={() => {}}>
                      <Styled.ButtonText>
                        <DateRange
                          color="var(--flo-sem-color-icon-primary)"
                          style={{ opacity: 0.8 }}
                          size={20}
                        />
                        {typeof input?.value === 'string' ? input.value : 'Select a date'}
                      </Styled.ButtonText>
                    </DropdownButton>
                  }
                  styleOverrides={{
                    content: {
                      zIndex: '9999',
                    },
                  }}
                />
              ))
              .with('TEXT', 'NUMBER', () => (
                <Input
                  value={input?.value ?? ''}
                  placeholder={t('components.Builder.enterValue')}
                  onChange={(value: string) => {
                    if (isFormActive)
                      props.onChange({
                        value,
                      });
                  }}
                />
              ))
              .exhaustive()}
            <InputWrapper.Label
              style={{
                fontWeight: 'var(--flo-base-font-weight-4)',
                color: 'var(--flo-sem-color-text-body-secondary)',
              }}
            >
              {t('components.Builder.builderInput')}
            </InputWrapper.Label>
          </InputWrapper>
        </Styled.Body>
        {isEditMode && (
          <Button
            variant="ghost"
            color="dark"
            size="sm"
            style={{
              padding: '6px',
            }}
            onClick={onDelete}
          >
            {t('components.Builder.delete')}
          </Button>
        )}
      </Styled.CardContent>
    </Card>
  );
};
