import PropTypes from 'prop-types';
import { t } from '@/utils/i18n';
import { Heading, Button } from '@floqastinc/flow-ui_core';
import { useModal } from '@/components';
import { Overlay, ModalWrapper, ModalHeader, Form } from './styled';
import { AGENT } from '@/constants';

export const DeleteWorkflowModal = ({ workflowName, onSubmit }) => {
  const { closeModal } = useModal();

  const handleSubmit = (event) => {
    event.preventDefault();
    onSubmit();
    closeModal();
  };

  return (
    <Overlay onClick={closeModal}>
      <ModalWrapper onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          {t('generics.confirm')} {AGENT} {t('generics.deletion')}
        </ModalHeader>
        <Heading variant="body-base" weight="medium">
          {t('components.Builder.areYouSure')} {AGENT.toLocaleLowerCase()} &quot;{workflowName}
          &quot;? {t('components.Builder.cannotUndo')}
        </Heading>
        <Form onSubmit={handleSubmit}>
          <Button type="submit" color="danger">
            {t('generics.confirm')}
          </Button>
          <Button type="button" onClick={closeModal} color="dark" variant="outlined">
            {t('generics.cancel')}
          </Button>
        </Form>
      </ModalWrapper>
    </Overlay>
  );
};

DeleteWorkflowModal.propTypes = {
  workflowName: PropTypes.string.isRequired,
  onSubmit: PropTypes.func.isRequired,
};
