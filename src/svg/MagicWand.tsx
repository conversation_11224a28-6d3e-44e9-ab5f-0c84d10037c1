interface MagicWandProps {
  width?: number;
  height?: number;
  color?: string;
}

export const MagicWand = ({ width = 20, height = 20, color = "#6B7280" }: MagicWandProps) => {
  return (
    <svg width={width} height={height} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <mask id="mask0_7781_102519" style={{ maskType: 'alpha' }} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
        <rect width="30" height="30" fill="#D9D9D9"/>
      </mask>
      <g mask="url(#mask0_7781_102519)">
        <path d="M7.36848 6.65536L5.16494 4.45182L5.93098 3.68578L8.13452 5.88932L7.36848 6.65536ZM10.2083 4.97266V1.84766H11.2916V4.97266H10.2083ZM15.5481 14.8349L13.3445 12.6314L14.1106 11.8654L16.3141 14.0689L15.5481 14.8349ZM14.5208 6.24516L13.7547 5.47911L15.9583 3.27557L16.7243 4.04161L14.5208 6.24516ZM15.0272 9.79161V8.70828H18.1522V9.79161H15.0272ZM4.59932 17.3781L2.61369 15.3718C2.47702 15.24 2.40869 15.0863 2.40869 14.9108C2.40869 14.7352 2.47911 14.5772 2.61994 14.4368L9.5064 7.55766C9.90793 7.15168 10.3955 6.9487 10.9691 6.9487C11.5427 6.9487 12.0311 7.15106 12.4341 7.55578C12.8402 7.95759 13.0433 8.44557 13.0433 9.01974C13.0433 9.59377 12.8402 10.0824 12.4341 10.4856L5.55515 17.372C5.41473 17.5129 5.25418 17.5833 5.07348 17.5833C4.89279 17.5833 4.73473 17.5149 4.59932 17.3781ZM5.08494 16.3237L10.1266 11.282L8.70994 9.86537L3.66827 14.907L5.08494 16.3237Z" fill={color}/>
      </g>
    </svg>
  )
}