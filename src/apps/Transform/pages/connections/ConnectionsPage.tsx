import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Divider, useToast } from '@floqastinc/flow-ui_core';
import { t } from '@/utils/i18n';

import ConnectionCardSkeleton from './ui/ConnectionCards/ConnectionCardSkeleton';
import { ConnectionsHeader } from './ui/ConnectionsHeader/ConnectionsHeader';
import { DiscoverConnectionCategories } from './ui/ConnectionCards/DiscoverConnectionCategories';

import { useConnections } from './hooks/useConnections';

export const ConnectionsPage = () => {
  const { isLoading, error } = useConnections();
  const [searchTerm, setSearchTerm] = useState('');

  const { showToast, Toast } = useToast();
  const toastShownRef = useRef(false);

  useEffect(() => {
    if (error && !toastShownRef.current) {
      toastShownRef.current = true;
      showToast(
        <Toast type="error">
          <Toast.Title>{t('components.Connections.Errors.loadingPage')}</Toast.Title>
          <Toast.Message>{t('components.Connections.Errors.pleaseTryAgain')}</Toast.Message>
        </Toast>,
        // eslint-disable-next-line i18next/no-literal-string
        { position: 'top-right' },
      );
    }
  }, [error, showToast, Toast]);

  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    setSearchParams('');
  }, [searchParams, setSearchParams]);

  return (
    <>
      <ConnectionsHeader setSearchTerm={setSearchTerm} />
      <Divider />
      {isLoading ? (
        <ConnectionCardSkeleton />
      ) : error ? (
        <></>
      ) : (
        <DiscoverConnectionCategories searchTerm={searchTerm} />
      )}
    </>
  );
};
