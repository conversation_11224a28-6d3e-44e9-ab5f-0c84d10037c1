import { useEffect, useRef, useState } from "react";
import {
  Button,
  TextArea,
  Popover,
  Card,
  Skeleton,
  useToast,
  ButtonGroup,
  Spinner,
  Tooltip,
} from "@floqastinc/flow-ui_core";
import { match } from "ts-pattern";
import EditOutlined from "@floqastinc/flow-ui_icons/material/EditOutlined";
import SyncOutlined from "@floqastinc/flow-ui_icons/material/SyncOutlined";
import DeleteOutlined from "@floqastinc/flow-ui_icons/material/DeleteOutlined";
import Add from "@floqastinc/flow-ui_icons/material/Add";
import Replay from "@floqastinc/flow-ui_icons/material/Replay";
import AutoFix from "@floqastinc/flow-ui_icons/fq/AutoFix";
import MagicAIStar from "@floqastinc/flow-ui_icons/fq/MagicAIStar";
import Markdown from "react-markdown";
import { useMutation } from "@tanstack/react-query";
import { ExampleSet } from "@floqastinc/transform-v3";
import { useSearchParams } from "react-router-dom";
import * as Styled from "./index.styles";
import { TooltipIconButton } from "./components/TooltipIconButton";
import { selectMessages, SelectedMessage } from "./utils/chatMessages";
import { ConfirmationDialog } from "./components/ConfirmationDialog";
import { useChatMessages } from "./hooks/useChatMessages";
import { StepWarningMessage } from "./components/StepWarningMessage/StepWarningMessage";
import { t } from "@/utils/i18n";
import { Li } from "@/components/Li";
import { ArrowWarmUpRun } from "@/svg/ArrowWarmUpRun";
import { CtrlZ } from "@/svg/CtrlZ";
import { CtrlE } from "@/svg/CtrlE";
import { CtrlEnter } from "@/svg/CtrlEnter";
import { TransformAIStar } from "@/svg/TransformAIStar";
import { useMessages } from "@v3/messages";
import { createMessage, useMessageStreaming, optimizeMessage } from "@/api/bff/messages";
import { useFeatureFlags } from "@/components/FeatureFlag";
import { editMessage, regenerateMessage } from "@BuilderV3/api/messages";
import { v3 } from "@/services/v3";
import { DISMISS_STEP_WARNING_CHAT_QUERY_PARAM } from "@/constants";
import { useExample } from "@v3/examples";
import { Loading } from "@/components/Loading";

// Extend the Window interface to include aptrinsic
declare global {
  interface Window {
    aptrinsic?: (...args: (string | object)[]) => void;
  }
}

type ChatProps = {
  workflowId: string;
  taskId: string;
  exampleSetId: string;
};

const shouldShowWarningMessage = (example: ExampleSet | undefined, dismissedWarning: boolean) =>
  !dismissedWarning &&
  example?.strategy?.kind === "SCRIPT" &&
  example?.strategy?.megascriptStatus === "FAILED";

export const Chat = ({ workflowId, taskId, exampleSetId }: ChatProps) => {
  const { getFlag } = useFeatureFlags();
  const [isAtBottom, setIsAtBottom] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const inputContainerRef = useRef<HTMLDivElement | null>(null);
  const [actionsMenuOpen, setActionsMenuOpen] = useState(false);
  const [chatBoxHeight, setChatBoxHeight] = useState(0);
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState(false);
  const [confirmationDialogAction, _setConfirmationDialogAction] = useState<
    "edit" | "delete" | "regenerate" | null
  >(null);
  const [selectedMessageId, setSelectedMessageId] = useState<string | undefined>(undefined);
  const [editingMessageId, setEditingMessageId] = useState<string | undefined>(undefined);
  const [editValue, setEditValue] = useState("");
  const [messages, setMessages] = useState<SelectedMessage[]>([]);
  const [searchParams] = useSearchParams();
  const dismissedWarning = searchParams.get(DISMISS_STEP_WARNING_CHAT_QUERY_PARAM) === "true";
  const { data: exampleSet } = useExample(
    {
      workflowId,
      taskId,
      exampleSetId,
    },
    {
      enabled: !!workflowId && !!taskId && !!exampleSetId,
    },
  );

  const { showToast, Toast } = useToast();
  type ToastType = "error" | "success";
  const renderToast = (type: ToastType, title: string, message: string) => {
    showToast(
      <Toast type={type}>
        <Toast.Title>{title}</Toast.Title>
        <Toast.Message>{message}</Toast.Message>
      </Toast>,
      {
        duration: 5000,
        position: "bottom-right",
      },
    );
  };

  const {
    message,
    setMessage,
    originalMessage,
    setOriginalMessage,
    optimizedContent,
    setOptimizedContent,
  } = useChatMessages({
    workflowId,
    taskId,
    exampleSetId,
  });

  const messagesQuery = useMessages({
    workflowId,
    taskId,
    exampleSetId,
  });
  useEffect(() => {
    if (messagesQuery.data?.data) {
      setMessages(selectMessages(messagesQuery.data.data));
    }
  }, [messagesQuery.data?.data]);

  const removeMessagesFrom = (messageId: string) => {
    const messagesToKeepIndex = messages.findIndex((msg) => msg.id === messageId);
    if (messagesToKeepIndex === -1) {
      return messages;
    }
    return messages.slice(0, messagesToKeepIndex);
  };
  const removeMessagesAfter = (messageId: string) => {
    const messagesToKeepIndex = messages.findIndex((msg) => msg.id === messageId);
    if (messagesToKeepIndex === -1) {
      return messages;
    }
    return messages.slice(0, messagesToKeepIndex + 1);
  };

  const [incomingMessage, setIncomingMessage] = useState("");
  const [incomingOptimizedMessage, setIncomingOptimizedMessage] = useState("");
  const [optimizeErrorMessage, setOptimizeErrorMessage] = useState("");

  const { assistantMessageIsPending, deletionIsPending } = useMessageStreaming({
    workflowId,
    taskId,
    exampleSetId,
    onMessageEvent: (message) => {
      //optimizeMessage yields slightly different response
      if (optimizeMessageMutation.isPending) {
        const parsedMessage = JSON.parse(message);
        // If it's an error response, only show toast when complete
        if (parsedMessage.status.includes("400")) {
          setOptimizeErrorMessage(parsedMessage.optimizedPrompt);
          return;
        }
        setIncomingOptimizedMessage(parsedMessage.optimizedPrompt);
      } else {
        setIncomingMessage(message);
      }
    },
    enabled: !messagesQuery.isPending,
  });

  const createMessageMutation = useMutation({
    mutationKey: ["createMessage", { workflowId, taskId, exampleSetId }],
    mutationFn: async (message: string) => {
      setIncomingMessage("");
      setMessage("");
      return createMessage({
        workflowId,
        taskId,
        exampleSetId,
        message,
      });
    },
    onSettled: () => {
      setIncomingMessage("");
      setActionsMenuOpen(false);
    },
  });
  const shouldShowAssistantMessageSkeleton =
    !createMessageMutation.isPending && assistantMessageIsPending;
  const optimizeMessageMutation = useMutation({
    mutationKey: ["optimizeMessage", { workflowId, taskId, exampleSetId }],
    mutationFn: async (message: string) => {
      setIncomingOptimizedMessage("");
      setOriginalMessage(message);

      return optimizeMessage({
        workflowId,
        taskId,
        exampleSetId,
        message: {
          content: message,
        },
      });
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onSuccess: (_, data) => {
      setOptimizedContent(incomingOptimizedMessage);
      setMessage(incomingOptimizedMessage);
    },
    onError: (error) => {
      console.error("error", error);
      renderToast("error", "Error", optimizeErrorMessage);
    },
    onSettled: () => {
      setActionsMenuOpen(false);
      setIncomingOptimizedMessage("");
    },
  });

  const editMessageMutation = useMutation({
    mutationKey: [
      "editMessage",
      {
        workflowId,
        taskId,
        exampleSetId,
        messageId: editingMessageId,
      },
    ],
    mutationFn: async ({ content, id }: { content: string; id: string }) => {
      if (!content.trim()) throw new Error(t("components.Builder.Errors.emptyMessage"));

      // Return the previous messages for rollback
      return editMessage({
        workflowId,
        taskId,
        exampleSetId,
        message: { id, content },
      });
    },
    onMutate: async ({ content, id }) => {
      // Store the previous messages state
      const previousMessages = messages;

      // Optimistically update the messages
      setMessages([
        ...removeMessagesFrom(id),
        {
          id: undefined,
          role: "user",
          content: content,
        },
      ]);

      // Return the previous messages for rollback
      return { previousMessages };
    },
    onError: (error, variables, context) => {
      console.error("error", error);
      // Revert to the previous messages state
      if (context?.previousMessages) {
        setMessages(context.previousMessages);
      }
    },
  });

  const regenerateMessageMutation = useMutation({
    mutationKey: [
      "regenerateMessage",
      {
        workflowId,
        taskId,
        exampleSetId,
        messageId: editingMessageId,
      },
    ],
    mutationFn: async (messageId: string | undefined) => {
      if (!messageId) {
        throw new Error(t("components.Builder.Errors.noMsgIDRegen"));
      }
      setMessages(removeMessagesAfter(messageId));

      return regenerateMessage({
        workflowId,
        taskId: taskId,
        exampleSetId,
        messageId,
      });
    },
    onSuccess: async () => {
      renderToast("success", "Success", t("components.Builder.messageRegenerated"));
    },
    onError: (error) => {
      console.error("error", error);
    },
  });

  const deleteMessageMutation = useMutation({
    mutationKey: [
      "deleteMessage",
      {
        workflowId,
        taskId,
        exampleSetId,
        messageId: editingMessageId,
      },
    ],
    mutationFn: async (messageId: string | undefined) => {
      if (!messageId) {
        throw new Error(t("components.Builder.Errors.noMsgIDDel"));
      }
      return v3.messages.deleteMessage({
        workflowId,
        taskId,
        exampleSetId,
        messageId,
      });
    },
    onSuccess: async () => {
      renderToast("success", "Success", t("components.Builder.messageDeleted"));
    },
    onError: (error) => {
      console.error("error", error);
    },
  });

  const handleEditSave = () => {
    if (!editingMessageId) {
      console.error(t("components.Builder.Errors.noMsgIDEdit"));
      return;
    }
    editMessageMutation.mutate({ content: editValue, id: editingMessageId });
    setEditingMessageId(undefined);
  };

  // Function to calculate and update padding based on input container height
  const updateChatBoxHeightCalculation = () => {
    if (inputContainerRef.current && containerRef.current) {
      const inputHeight = inputContainerRef.current.offsetHeight;
      // Add some extra padding (16px) for visual comfort
      const newPadding = inputHeight + 16;
      setChatBoxHeight(newPadding);
    }
  };

  // Calculate initial padding when component mounts
  useEffect(() => {
    updateChatBoxHeightCalculation();
    // Add resize listener to recalculate padding when window is resized
    window.addEventListener("resize", updateChatBoxHeightCalculation);
    return () => {
      window.removeEventListener("resize", updateChatBoxHeightCalculation);
    };
  }, []);

  // Auto scroll to bottom when new messages are added
  useEffect(() => {
    if (isAtBottom && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isAtBottom]);

  // Track scroll position to determine if user is at bottom
  const handleScroll = () => {
    if (containerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
      // Consider "at bottom" if within 100px of the bottom
      const atBottom = scrollHeight - scrollTop - clientHeight < 100;
      setIsAtBottom(atBottom);
    }
  };

  const exampleIsDraft = exampleSet?.status === "DRAFT";

  // Add keyboard event listener for hotkeys
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle if no modifier keys except Ctrl/Command are pressed
      if (e.altKey || e.shiftKey) return;

      // Check for either Ctrl (Windows/Linux) or Command (Mac)
      if (e.ctrlKey || e.metaKey) {
        // eslint-disable-next-line no-restricted-syntax
        switch (e.key.toLowerCase()) {
          case "e":
            // Enhance with AI
            if (message && !optimizeMessageMutation.isPending && !createMessageMutation.isPending) {
              e.preventDefault();
              optimizeMessageMutation.mutate(message);

              // aptrinsic function is used to create custom gainsight events
              // gainsight is initialized in client-hub
              if (window.aptrinsic)
                window.aptrinsic("track", "builder-chat-optimize-prompt-hotkey");
            }
            break;
          case "z":
            // Undo
            if (
              message === optimizedContent &&
              message !== originalMessage &&
              !optimizeMessageMutation.isPending &&
              !createMessageMutation.isPending
            ) {
              e.preventDefault();
              setMessage(originalMessage);
              setOptimizedContent("");

              if (window.aptrinsic) window.aptrinsic("track", "builder-chat-undo-optimize-hotkey");
            }
            break;
          case "enter":
            // Submit
            if (message && !createMessageMutation.isPending) {
              e.preventDefault();
              createMessageMutation.mutate(message);
            }
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    message,
    optimizedContent,
    originalMessage,
    optimizeMessageMutation.isPending,
    createMessageMutation.isPending,
    createMessageMutation,
    optimizeMessageMutation,
    setMessage,
    setOptimizedContent,
  ]);

  return (
    <>
      {confirmationDialogAction ? (
        <ConfirmationDialog
          isOpen={isConfirmationDialogOpen}
          onOpenChange={setIsConfirmationDialogOpen}
          action={confirmationDialogAction}
          message={"TODO"}
          onConfirm={() => {
            match(confirmationDialogAction)
              .with("edit", () => {
                setEditingMessageId(selectedMessageId);
                setEditValue(messages.find((m) => m.id === selectedMessageId)?.content || "");
                setIsConfirmationDialogOpen(false);
              })
              .with("regenerate", () => {
                regenerateMessageMutation.mutate(selectedMessageId);
                setIsConfirmationDialogOpen(false);
              })
              .with("delete", () => {
                deleteMessageMutation.mutate(selectedMessageId);
                setIsConfirmationDialogOpen(false);
              })
              .otherwise(() => {
                console.error(t("components.Builder.Errors.invalidConfirm"));
              });
          }}
        />
      ) : null}
      {deleteMessageMutation.isPending && (
        <Styled.LoadingOverlay>
          <Loading />
        </Styled.LoadingOverlay>
      )}
      <Styled.ChatContent>
        <Styled.ChatLog ref={containerRef} onScroll={handleScroll}>
          <Styled.ChatLogMessages>
            {messages.map((message, index) => {
              return match(message)
                .with({ role: "user" }, (msg) => {
                  const isEditing = editingMessageId === msg.id && msg.id !== undefined;
                  return (
                    <Styled.UserChatContainer key={index}>
                      <Styled.UserChat isEditing={isEditing}>
                        {isEditing ? (
                          <div style={{ gap: "8px", display: "flex", flexDirection: "column" }}>
                            <TextArea
                              value={editValue}
                              onChange={setEditValue}
                              styleOverrides={{
                                inputWrapper: {
                                  width: "100%",
                                },
                                textarea: {
                                  width: "100%",
                                },
                                root: {
                                  width: "100%",
                                },
                              }}
                            />
                            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                              <Button
                                variant="outlined"
                                style={{ color: "#6B7280" }}
                                onClick={() => setEditingMessageId(undefined)}
                                data-testid="cancel-edit-button"
                              >
                                {t("components.Builder.cancel")}
                              </Button>
                              <Button onClick={handleEditSave} data-testid="save-edit-button">
                                {t("components.Builder.save")}
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <Markdown
                            components={{
                              li({ node: _node, ...rest }) {
                                return <Li {...rest} />;
                              },
                            }}
                          >
                            {msg.content}
                          </Markdown>
                        )}
                      </Styled.UserChat>
                      {exampleIsDraft && (
                        <Styled.UserChatActions>
                          <TooltipIconButton
                            title={t("components.Builder.editMessage")}
                            tooltipMessage={t("components.Builder.editMessage")}
                            icon={<EditOutlined color="var(--flo-sem-color-icon-muted)" />}
                            size="md"
                            data-testid="edit-message-button"
                            onClick={() => {
                              setSelectedMessageId(msg.id);
                              _setConfirmationDialogAction("edit");
                              setIsConfirmationDialogOpen(true);
                            }}
                            disabled={assistantMessageIsPending || deletionIsPending}
                          />
                          <TooltipIconButton
                            title={t("components.Builder.regenerateMessage")}
                            tooltipMessage={t("components.Builder.regenerateMessage")}
                            icon={<SyncOutlined color="var(--flo-sem-color-icon-muted)" />}
                            size="md"
                            data-testid="regenerate-message-button"
                            onClick={() => {
                              setSelectedMessageId(msg.id);
                              _setConfirmationDialogAction("regenerate");
                              setIsConfirmationDialogOpen(true);
                            }}
                            disabled={assistantMessageIsPending || deletionIsPending}
                          />
                          <TooltipIconButton
                            title={t("components.Builder.deleteMessage")}
                            tooltipMessage={t("components.Builder.deleteMessage")}
                            icon={<DeleteOutlined color="var(--flo-sem-color-icon-muted)" />}
                            size="md"
                            data-testid="delete-message-button"
                            onClick={() => {
                              setSelectedMessageId(msg.id);
                              _setConfirmationDialogAction("delete");
                              setIsConfirmationDialogOpen(true);
                            }}
                            disabled={assistantMessageIsPending || deletionIsPending}
                          />
                        </Styled.UserChatActions>
                      )}
                    </Styled.UserChatContainer>
                  );
                })

                .with({ role: "assistant" }, (msg) => (
                  <Styled.AssistantChat key={index}>
                    <Markdown
                      components={{
                        li({ node: _node, ...rest }) {
                          return <Li {...rest} />;
                        },
                      }}
                    >
                      {msg.content}
                    </Markdown>
                  </Styled.AssistantChat>
                ))
                .otherwise(() => null);
            })}
            {createMessageMutation.isPending ? (
              <>
                <Styled.UserChatContainer>
                  <Styled.UserChat>
                    <Markdown
                      components={{
                        li({ node: _node, ...rest }) {
                          return <Li {...rest} />;
                        },
                      }}
                    >
                      {createMessageMutation.variables}
                    </Markdown>
                  </Styled.UserChat>
                </Styled.UserChatContainer>
                {incomingMessage ? (
                  <Styled.AssistantChat>
                    <Markdown
                      components={{
                        li({ node: _node, ...rest }) {
                          return <Li {...rest} />;
                        },
                      }}
                    >
                      {incomingMessage}
                    </Markdown>
                  </Styled.AssistantChat>
                ) : (
                  <Styled.AssistantChat>
                    <div style={{ display: "flex", flexDirection: "row", gap: "2px" }}>
                      <Skeleton variant="circle" radius="4" />
                      <Skeleton variant="circle" radius="4" />
                      <Skeleton variant="circle" radius="4" />
                    </div>
                  </Styled.AssistantChat>
                )}
              </>
            ) : null}
            {shouldShowAssistantMessageSkeleton && (
              <Styled.AssistantChat>
                <div style={{ display: "flex", flexDirection: "row", gap: "2px" }}>
                  <Skeleton variant="circle" radius="4" />
                  <Skeleton variant="circle" radius="4" />
                  <Skeleton variant="circle" radius="4" />
                </div>
              </Styled.AssistantChat>
            )}
            {shouldShowWarningMessage(exampleSet, dismissedWarning) && <StepWarningMessage />}
            <div ref={messagesEndRef} style={{ height: "0" }}></div>
          </Styled.ChatLogMessages>
          {exampleIsDraft && (
            <Styled.ChatBox>
              <Styled.OptimizationStatusFrame isVisible={optimizeMessageMutation.isPending}>
                <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  <MagicAIStar />
                  <Styled.EnhancingText>
                    {t("components.Builder.enhancingWithAI")}
                  </Styled.EnhancingText>
                </div>
                <Styled.StatusText>{t("components.Builder.couldTakeAMinute")}</Styled.StatusText>
              </Styled.OptimizationStatusFrame>
              <div style={{ position: "relative" }}>
                <Styled.StyledTextArea
                  ref={inputContainerRef}
                  aria-label={t("components.Builder.chatMSGTextArea")}
                  placeholder={t("components.Builder.typeCommand")}
                  value={optimizeMessageMutation.isPending ? incomingOptimizedMessage : message}
                  disabled={createMessageMutation.isPending}
                  onChange={(text: string) => {
                    setMessage(text);
                  }}
                  styleOverrides={{
                    textarea: {
                      width: "100%",
                      borderBottomRightRadius: "0",
                      borderBottomLeftRadius: "0",
                      borderTopRightRadius: "8px",
                      borderTopLeftRadius: "8px",
                      border: "1px solid var(--flo-sem-color-border)",
                      borderBottom: "none",
                    },
                  }}
                />
                {optimizeMessageMutation.isPending && <Styled.GradientOverlay />}
              </div>
              <Styled.TextAreaActionBar>
                {getFlag("enable-builder-input-editing") ? (
                  <Popover
                    open={actionsMenuOpen}
                    onOpenChange={(isOpen: boolean) => {
                      setActionsMenuOpen(isOpen);
                    }}
                  >
                    <Popover.Trigger>
                      <Button
                        variant="outlined"
                        color="dark"
                        data-testid="actions-menu-button"
                        onClick={() => {
                          setActionsMenuOpen(!actionsMenuOpen);
                        }}
                      >
                        <Add color="currentColor" />
                        {t("components.Builder.actions")}
                      </Button>
                    </Popover.Trigger>
                    <Popover.Content side="top" sideOffset={chatBoxHeight}>
                      <Card>{t("components.Builder.TODO")}</Card>
                    </Popover.Content>
                  </Popover>
                ) : (
                  <div></div>
                )}
                <ButtonGroup>
                  {message === optimizedContent &&
                    message !== originalMessage &&
                    !optimizeMessageMutation.isPending &&
                    !createMessageMutation.isPending && (
                      <Tooltip>
                        <Tooltip.Trigger>
                          <Styled.UndoButton
                            onClick={() => {
                              setMessage(originalMessage);
                              setOptimizedContent("");
                            }}
                            disabled={message !== optimizedContent}
                            data-testid="undo-optimize-button"
                            data-tracking-id="builder-chat-undo-optimize-button"
                            color="dark"
                            variant="ghost"
                          >
                            <Replay />
                          </Styled.UndoButton>
                        </Tooltip.Trigger>
                        <Styled.StyledTooltipContent>
                          <Styled.TooltipContent>
                            <span>{t("components.Builder.undo")}</span>
                            <CtrlZ />
                          </Styled.TooltipContent>
                        </Styled.StyledTooltipContent>
                      </Tooltip>
                    )}
                  <Tooltip>
                    <Tooltip.Trigger>
                      <Styled.OptimizePromptButton
                        onClick={() => {
                          optimizeMessageMutation.mutate(message);
                        }}
                        disabled={
                          !message ||
                          optimizeMessageMutation.isPending ||
                          createMessageMutation.isPending
                        }
                        data-testid="optimize-prompt-button"
                        data-tracking-id="builder-chat-optimize-prompt-button"
                        color="dark"
                        variant="outlined"
                      >
                        {optimizeMessageMutation.isPending ? <Spinner /> : <AutoFix />}
                      </Styled.OptimizePromptButton>
                    </Tooltip.Trigger>
                    <Styled.StyledTooltipContent>
                      <Styled.TooltipContent>
                        <TransformAIStar />
                        <span>{t("components.Builder.enhanceWithAI")}</span>
                        <CtrlE />
                      </Styled.TooltipContent>
                    </Styled.StyledTooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <Tooltip.Trigger>
                      <Styled.SubmitChatButton
                        onClick={() => {
                          createMessageMutation.mutate(message);
                        }}
                        disabled={
                          !message ||
                          createMessageMutation.isPending ||
                          optimizeMessageMutation.isPending
                        }
                        data-testid="submit-chat-button"
                      >
                        <ArrowWarmUpRun />
                      </Styled.SubmitChatButton>
                    </Tooltip.Trigger>
                    <Styled.StyledTooltipContent>
                      <Styled.TooltipContent>
                        <span>{t("components.Builder.submit")}</span>
                        <CtrlEnter />
                      </Styled.TooltipContent>
                    </Styled.StyledTooltipContent>
                  </Tooltip>
                </ButtonGroup>
              </Styled.TextAreaActionBar>
            </Styled.ChatBox>
          )}
        </Styled.ChatLog>
      </Styled.ChatContent>
    </>
  );
};
