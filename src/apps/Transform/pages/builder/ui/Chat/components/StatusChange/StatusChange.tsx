import { Dialog } from '@floqastinc/flow-ui_core';
import { match } from 'ts-pattern';
import { ExampleSetStatus } from '@floqastinc/transform-v3';
import { useMutation } from '@tanstack/react-query';
import * as Styled from './styles';
import { t } from '@/utils/i18n';
import { queryKeys } from '@BuilderV3/api/query-keys';
import { capitalize } from '@/utils/string';
import v3 from '@/services/v3';
import { queryClient } from '@/components';
import { AGENT } from '@/constants';
import { useFeatureFlags } from '@/components/FeatureFlag';

const statusMap: Record<string, string> = {
  PUBLISHED: capitalize('Saved'),
  DRAFT: capitalize('Draft'),
  ARCHIVED: capitalize('Archived'),
  DEFAULT: capitalize('Active'),
};
type StatusChangeState = {
  fromStatus: ExampleSetStatus | null;
  toStatus: ExampleSetStatus | null;
  exampleSetId: string | null;
};

type StatusChangeDialogProps = {
  isStatusChangeDialogOpen: boolean;
  statusChange: StatusChangeState;
  workflowId: string;
  taskId: string;
  setStatusChange: (state: StatusChangeState) => void;
  makeActive: boolean;
};

export const StatusChangeDialog = ({
  isStatusChangeDialogOpen,
  statusChange,
  workflowId,
  taskId,
  setStatusChange,
  makeActive,
}: StatusChangeDialogProps) => {
  const { getFlag } = useFeatureFlags();
  const updateExampleMutation = useMutation({
    mutationFn: ({ exampleSetId, status }: { exampleSetId: string; status: ExampleSetStatus }) => {
      return v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId,
        example: {
          status,
        },
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExamples({
          workflowId,
          taskId,
        }),
      });
      // What was this refetch for?
      // if (getIsChatBasedStrategy(data.data?.strategy) && data.data?.status === 'ACTIVE') {
      //   setRefetchTasksOverride(true);
      //   queryClient.invalidateQueries({
      //     queryKey: queryKeys.tasks.byWorkflow(workflowId),
      //   });
      // }
      if (data?.data?.id) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.examples.getExample({
            workflowId,
            taskId,
            exampleSetId: data?.data?.id,
          }),
        });
      }
      setStatusChange({
        fromStatus: null,
        toStatus: null,
        exampleSetId: null,
      });
    },
  });
  const bodyText = match(statusChange)
    .with({ fromStatus: 'ACTIVE', toStatus: 'DRAFT' }, () =>
      t('components.Builder.statusChange1', { AGENT }),
    )
    .with({ fromStatus: 'DRAFT', toStatus: 'ACTIVE' }, () => t('components.Builder.statusChange2'))
    .with({ fromStatus: 'ARCHIVED', toStatus: 'ACTIVE' }, () =>
      t('components.Builder.statusChange3'),
    )
    .with({ fromStatus: 'ARCHIVED', toStatus: 'DRAFT' }, () =>
      t('components.Builder.statusChange4'),
    )
    .with({ fromStatus: 'DRAFT', toStatus: 'ARCHIVED' }, () =>
      t('components.Builder.statusChange5'),
    )
    .with({ fromStatus: 'ACTIVE', toStatus: 'ARCHIVED' }, () =>
      t('components.Builder.statusChange6', { AGENT }),
    )
    .with({ fromStatus: 'ACTIVE', toStatus: 'PUBLISHED' }, () =>
      t('components.Builder.statusChange7'),
    )
    .with({ fromStatus: 'PUBLISHED', toStatus: 'ACTIVE' }, () =>
      t('components.Builder.statusChange8'),
    )
    .otherwise(() => '');

  return statusChange.fromStatus &&
    statusChange.toStatus &&
    statusChange.exampleSetId &&
    makeActive ? (
    <Dialog
      open={isStatusChangeDialogOpen}
      onOpenChange={(isOpen: boolean) => {
        if (!isOpen) {
          setStatusChange({
            fromStatus: null,
            toStatus: null,
            exampleSetId: null,
          });
        }
      }}
      type={false}
    >
      <Dialog.Header>
        {t('components.Builder.changingFrom')} {capitalize(statusMap[statusChange.fromStatus])}{' '}
        {t('generics.to')} {capitalize(statusChange.toStatus)}
      </Dialog.Header>
      <Dialog.Body>{bodyText}</Dialog.Body>
      <Dialog.Footer style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Dialog.FooterCancelBtn
          onClick={() => {
            setStatusChange({
              fromStatus: null,
              toStatus: null,
              exampleSetId: null,
            });
          }}
        >
          {t('components.Builder.cancel')}
        </Dialog.FooterCancelBtn>
        <Styled.FooterActionContainer>
          <Dialog.FooterActionBtn
            onClick={() => {
              if (statusChange.toStatus && statusChange.exampleSetId) {
                updateExampleMutation.mutate({
                  exampleSetId: statusChange.exampleSetId,
                  status: statusChange.toStatus,
                });
              }
            }}
            variant={'outlined'}
          >
            {t('components.Builder.activateOnly')}
          </Dialog.FooterActionBtn>
          {getFlag('enable-activate-test') ? (
            <Dialog.FooterActionBtn
              onClick={() => {
                // if (statusChange.toStatus && statusChange.exampleSetId) {
                //   updateExampleMutation.mutate({
                //     exampleSetId: statusChange.exampleSetId,
                //     status: statusChange.toStatus,
                //   });
                // }
                // TODO: Hook up testing functionality
                console.log('Activate & Test');
              }}
            >
              {t('components.Builder.activateTest')}
            </Dialog.FooterActionBtn>
          ) : null}
        </Styled.FooterActionContainer>
      </Dialog.Footer>
    </Dialog>
  ) : statusChange.fromStatus && statusChange.toStatus && statusChange.exampleSetId ? (
    <Dialog
      open={isStatusChangeDialogOpen}
      onOpenChange={(isOpen: boolean) => {
        if (!isOpen) {
          setStatusChange({
            fromStatus: null,
            toStatus: null,
            exampleSetId: null,
          });
        }
      }}
      type={false}
    >
      <Dialog.Header>{t('components.Builder.deleteVersion')}</Dialog.Header>
      <Dialog.Body>{t('components.Builder.deleteVersionBody')}</Dialog.Body>
      <Dialog.Footer style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Dialog.FooterCancelBtn
          onClick={() => {
            setStatusChange({
              fromStatus: null,
              toStatus: null,
              exampleSetId: null,
            });
          }}
        >
          {t('components.Builder.cancel')}
        </Dialog.FooterCancelBtn>
        <Styled.FooterActionContainer>
          <Dialog.FooterActionBtn
            color="danger"
            onClick={() => {
              if (statusChange.toStatus && statusChange.exampleSetId) {
                updateExampleMutation.mutate({
                  exampleSetId: statusChange.exampleSetId,
                  status: statusChange.toStatus,
                });
              }
            }}
          >
            {t('components.Builder.delete')}
          </Dialog.FooterActionBtn>
        </Styled.FooterActionContainer>
      </Dialog.Footer>
    </Dialog>
  ) : null;
};
