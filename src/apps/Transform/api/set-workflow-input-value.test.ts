import v3 from '@/services/v3';
import type {
  DataType,
  DatetimeArgument,
  FileArgument,
  TextArgument,
} from '@floqastinc/transform-v3';
import axios from 'axios';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { setWorkflowInputValue } from './set-workflow-input-value';

vi.mock('@/services/v3', () => ({
  default: {
    workflowInputs: {
      setWorkflowInputText: vi.fn(),
      setWorkflowInputDatetime: vi.fn(),
      setWorkflowInputFile: vi.fn(),
    },
  },
}));
vi.mock('axios');

const mockSetWorkflowInputText = v3.workflowInputs.setWorkflowInputText as unknown as ReturnType<
  typeof vi.fn
>;
const mockSetWorkflowInputDatetime = v3.workflowInputs
  .setWorkflowInputDatetime as unknown as ReturnType<typeof vi.fn>;
const mockSetWorkflowInputFile = v3.workflowInputs.setWorkflowInputFile as unknown as ReturnType<
  typeof vi.fn
>;
const mockAxiosPut = axios.put as unknown as ReturnType<typeof vi.fn>;

describe('setWorkflowInputValue', () => {
  const workflowId = 'workflow-1';
  const workflowInputId = 'input-1';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('calls setWorkflowInputText for TEXT input', async () => {
    const input = {
      type: 'TEXT' as DataType,
      name: 'Input Name',
      description: 'Input Description',
      value: { kind: 'TEXT', value: 'hello' } as TextArgument,
    };
    mockSetWorkflowInputText.mockResolvedValueOnce('text-result');

    const result = await setWorkflowInputValue({ workflowId, workflowInputId, input });

    expect(mockSetWorkflowInputText).toHaveBeenCalledWith({
      workflowId,
      workflowInputId,
      value: input.value,
    });
    expect(result).toBe('text-result');
  });

  it('calls setWorkflowInputDatetime for DATETIME input', async () => {
    const input = {
      type: 'DATETIME' as DataType,
      name: 'Datetime Input',
      description: 'Input Description',
      value: { kind: 'DATETIME', value: new Date() } as DatetimeArgument,
    };
    mockSetWorkflowInputDatetime.mockResolvedValueOnce('datetime-result');

    const result = await setWorkflowInputValue({ workflowId, workflowInputId, input });

    expect(mockSetWorkflowInputDatetime).toHaveBeenCalledWith({
      workflowId,
      workflowInputId,
      value: {
        kind: 'DATETIME',
        value: new Date(input.value.value),
      },
    });
    expect(result).toBe('datetime-result');
  });

  it('calls setWorkflowInputFile and uploads file for FILE input', async () => {
    const fakeFile = new File(['file-content'], 'test.txt', { type: 'text/plain' });
    const input = {
      type: 'FILE' as DataType,
      name: 'File Input',
      description: 'Input Description',
      value: { kind: 'FILE', value: fakeFile } as FileArgument & { value: File },
    };
    mockSetWorkflowInputFile.mockResolvedValueOnce({ data: { url: 'https://upload-url' } });
    mockAxiosPut.mockResolvedValueOnce({ status: 200 });

    await setWorkflowInputValue({ workflowId, workflowInputId, input });

    expect(mockSetWorkflowInputFile).toHaveBeenCalledWith({
      workflowId,
      workflowInputId,
      value: {
        kind: 'FILE',
        mimetype: fakeFile.type,
        name: fakeFile.name,
      },
    });
    expect(mockAxiosPut).toHaveBeenCalledWith('https://upload-url', fakeFile, {
      headers: { 'Content-Type': fakeFile.type },
    });
  });

  it('throws if setWorkflowInputFile returns no data', async () => {
    const fakeFile = new File(['file-content'], 'test.txt', { type: 'text/plain' });
    const input = {
      type: 'FILE' as DataType,
      name: 'File Input',
      description: 'Input Description',
      value: { kind: 'FILE', value: fakeFile } as FileArgument & { value: File },
    };
    mockSetWorkflowInputFile.mockResolvedValueOnce({});

    await expect(setWorkflowInputValue({ workflowId, workflowInputId, input })).rejects.toThrow(
      'Unexpected error: Failed to set workflow input file value, but no errors were generated',
    );
  });

  it('throws if file input value is missing', async () => {
    const input = {
      type: 'FILE' as DataType,
      name: 'File Input',
      description: 'Input Description',
      value: { kind: 'FILE', value: null } as FileArgument & { value: File | null },
    };

    await expect(setWorkflowInputValue({ workflowId, workflowInputId, input })).rejects.toThrow(
      'File input value is required for FILE type inputs',
    );
  });

  it('throws if file upload fails', async () => {
    const fakeFile = new File(['file-content'], 'test.txt', { type: 'text/plain' });
    const input = {
      type: 'FILE' as DataType,
      name: 'File Input',
      description: 'Input Description',
      value: { kind: 'FILE', value: fakeFile } as FileArgument & { value: File },
    };
    mockSetWorkflowInputFile.mockResolvedValueOnce({ data: { url: 'https://upload-url' } });
    mockAxiosPut.mockResolvedValueOnce({ status: 500, statusText: 'Internal Server Error' });

    await expect(setWorkflowInputValue({ workflowId, workflowInputId, input })).rejects.toThrow(
      'Failed to upload file: Internal Server Error',
    );
  });

  it('warns for unsupported input type', async () => {
    const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    const input = {
      type: 'UNSUPPORTED',
      value: { kind: 'UNSUPPORTED', value: 'foo' },
    } as any;

    await setWorkflowInputValue({ workflowId, workflowInputId, input });

    expect(warnSpy).toHaveBeenCalledWith('Unsupported input type for setting value: UNSUPPORTED');
    warnSpy.mockRestore();
  });
});
