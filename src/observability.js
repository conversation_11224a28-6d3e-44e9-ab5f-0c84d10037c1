let observability;

const getObservability = () => {
    return observability;
};

const setObservability = (value) => {
    observability = value;
};

const setTag = (name, value) => {
    if (observability && observability.setTag) {
        observability.setTag(name, value);
    }
};

const removeTag = (name) => {
    if (observability && observability.removeTag) {
        observability.removeTag(name);
    }
};

const captureException = (error, tags, captureContext) => {
    if (observability && observability.captureException) {
        observability.captureException(error, tags, captureContext);
    }
};

export default {
    captureException,
    getObservability,
    setObservability,
    setTag,
    removeTag,
};
