import styled from 'styled-components';

export const StepContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 48px;
  padding: 24px;
`;

export const Step = styled.div<{ active: boolean }>`
  display: flex;
  flex-direction: row;
  height: 88px;
  width: 100%;
  border: ${(props) =>
    props.active
      ? '1px solid var(--flo-sem-color-primary-default)'
      : '1px solid #e1e6ef'};
  border-radius: 6px;
  padding: 24px;
  align-items: center;
  justify-content: space-between;

  &:focus {
    border: 1px solid #3d7bf7;
  }
`;

export const StepHeading = styled.div`
  display: flex;
  flex-direction: row;
  gap: 4px;
`;

export const TaskInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`;

export const TaskDescription = styled.p`
  font-size: 12px;
`;

export const AddStep = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 48px;
  border: 1px solid #e1e6ef;
  border-radius: 6px;
  &:focus {
    border: 1px solid #3d7bf7;
  }
`;

export const VersionDropdownButton = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

export const Controls = styled.div`
  display: flex;
  flex-direction: row;
  gap: 12px;
`;

export const DeleteOption = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
`;

export const DeleteText = styled.p`
  color: #d24747;
`;
