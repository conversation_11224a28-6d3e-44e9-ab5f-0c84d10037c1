import React, { useCallback } from 'react';
import { t } from '@/utils/i18n';
import {
  Button,
  EmptyState,
  Flex,
  Heading,
  Input,
  Table,
  THead,
  TR,
  TH,
  TD,
  TBody,
  useToast,
  TableStatusBadge,
  // @ts-ignore
} from '@floqastinc/flow-ui_core';
// @ts-ignore
import PersonAddOutlined from '@floqastinc/flow-ui_icons/material/PersonAddOutlined';
import { useNavigate } from 'react-router-dom';
import { User, UserRole } from '@floqastinc/transform-v0';
import v0 from '@/services/v0';
import { useCurrentUser } from '@/utils/principal';

export const UsersTabContents: React.FC<{
  teamId: string;
  users: User[];
  onInvalidateQuery: () => Promise<void>;
}> = ({ teamId, users, onInvalidateQuery }) => {
  const { showToast, Toast } = useToast();
  const [disableInvite, setDisableInvite] = React.useState(false);
  const handleInvite = useCallback(
    async ({ email, role }: { email: string; role: UserRole }) => {
      console.log('submit', email);

      setDisableInvite(true);
      const res = await v0.users.createUser({
        user: { email, role, teamId },
      });
      setDisableInvite(false);

      if (res.errors.length) {
        console.error(res.errors);
        showToast(
          <Toast type="error">
            <Toast.Title>{t('components.Admin.somethingWentWrong')}</Toast.Title>
            <Toast.Message>
              {t('components.Admin.unableToInviteUser')} (
              {res.errors.map((e) => e.detail).join('; ')})
            </Toast.Message>
          </Toast>,
          { position: 'top-right' },
        );
      } else {
        showToast(
          <Toast type="success">
            <Toast.Title>{t('components.Admin.inviteSent')}</Toast.Title>
            <Toast.Message>{t('components.Admin.sentInvitationTo', { email })}</Toast.Message>
          </Toast>,
          { position: 'top-right' },
        );
      }

      await onInvalidateQuery(); // queryClient.invalidateQueries({ queryKey, refetchType: 'all' });

      console.log(res);
    },
    [onInvalidateQuery, showToast, teamId],
  );

  const navigate = useNavigate();
  const currentUser = useCurrentUser();

  return (
    <>
      {users.length > 0 && (
        <Flex direction="column" gap={24} style={{ marginTop: '3rem' }}>
          <Heading variant="h2">{t('components.Admin.users')}</Heading>
          <Table fixed middleBorders>
            <THead>
              <TR>
                <TH width="50%">{t('generics.email')}</TH>
                <TH width="25%">{t('generics.role')}</TH>
                <TH width="25%">{t('generics.status')}</TH>
              </TR>
            </THead>
            <TBody>
              {users.map((user) => (
                <TR
                  key={user.id}
                  onClick={() => navigate(`/admin/users/${user.id}`)}
                  style={{ cursor: 'pointer' }}
                >
                  <TD>
                    <Flex gap="8">
                      <span>{user.email}</span>
                      {user.id === currentUser?.id && (
                        <TableStatusBadge>{t('generics.you')}</TableStatusBadge>
                      )}
                    </Flex>
                  </TD>
                  <TD>{user.role}</TD>
                  <TD>{user.status}</TD>
                </TR>
              ))}
            </TBody>
          </Table>
          <Heading variant="h2">{t('components.Admin.inviteMoreUsers')}</Heading>
          <Flex direction="column">
            <p>{t('components.Admin.getStartedInviting')}</p>
          </Flex>
          <InviteForm disabled={disableInvite} handleInvite={handleInvite} />
        </Flex>
      )}

      {users.length === 0 && (
        <Flex align="center" direction="column" gap={24} style={{ marginTop: '4rem' }}>
          <Heading variant="h2">{t('components.Admin.noOneHere')}</Heading>
          <EmptyState />
          <Flex align="center" direction="column">
            <p>{t('components.Admin.getStartedInviting')}</p>
          </Flex>
          <InviteForm disabled={disableInvite} handleInvite={handleInvite} />
        </Flex>
      )}
    </>
  );
};

const InviteForm: React.FC<{
  disabled?: boolean;
  handleInvite(invitation: { email: string; role: UserRole }): void;
}> = ({ disabled, handleInvite }) => {
  const [email, setEmail] = React.useState('');
  const [role, setRole] = React.useState<UserRole>('USER');

  const handleChangeRole = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setRole(e.target.value as UserRole);
  }, []);

  const handleClick = useCallback(() => {
    handleInvite({ email, role });
    setEmail('');
    setRole('USER');
  }, [email, role, handleInvite]);

  const handleSubmit = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      handleInvite({ email, role });
      setEmail('');
      setRole('USER');
    },
    [email, role, handleInvite],
  );

  return (
    <form onSubmit={handleSubmit} style={{ maxWidth: '600px', width: '100%' }}>
      <Flex align="center" gap={16} style={{ width: '100%' }}>
        <Input
          aria-label={t('components.Admin.inviteByEmail')}
          disabled={disabled}
          defaultValue={email}
          onChange={setEmail}
          value={email}
          placeholder="Invite by email"
        />

        <select
          disabled={disabled}
          name="userRole"
          id="userRole"
          onChange={handleChangeRole}
          value={role}
        >
          {Object.entries(options).map(([key, value]) => (
            <option key={key} value={key}>
              {value}
            </option>
          ))}
        </select>

        <Button
          disabled={disabled}
          onClick={handleClick}
          type="submit"
          style={{ paddingLeft: '1.5rem', paddingRight: '1.5rem' }}
        >
          <PersonAddOutlined style={{ flexShrink: 0 }} color="currentColor" />
          <span style={{ flexShrink: 0 }}>{t('components.Admin.inviteUser')}</span>
        </Button>
      </Flex>
    </form>
  );
};

const options: Record<UserRole, string> = {
  ADMIN: 'ADMIN',
  OMNI: 'OMNI',
  SUPER: 'SUPER',
  USER: 'USER',
};
