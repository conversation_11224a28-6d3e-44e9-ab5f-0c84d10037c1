import { Tooltip, IconButton } from '@floqastinc/flow-ui_core';

type TooltipIconButtonProps = {
  title: string;
  tooltipMessage: string;
  icon: React.ReactNode;
  onClick: () => void;
  disabled?: boolean;
} & React.ComponentProps<typeof IconButton>;
export const TooltipIconButton = ({ title, tooltipMessage, icon, onClick, disabled, ...rest }: TooltipIconButtonProps) => {
  return (
    <Tooltip title={title}>
      <Tooltip.Trigger>
        <IconButton
          size="sm"
          onClick={onClick}
          disabled={disabled}
          {...rest}
        >
          {icon}
        </IconButton>
      </Tooltip.Trigger>
      <Tooltip.Content>
        {tooltipMessage}
      </Tooltip.Content>
    </Tooltip>
  )
}
