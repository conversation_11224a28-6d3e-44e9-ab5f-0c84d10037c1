import * as Styled from './StepResults.styled';
import { ExternalJemLink } from './ExternalJemLink';
import { JournalEntriesIcon } from './JournalEntriesIcon';
type StepResultJEMLinkProps = {
  outputValue: {
    value: string;
    name: string;
    stepNumber: number;
    kind:
      | 'JEM_EXPORT'
      | 'LLM_PROMPT'
      | 'LLM_THREAD'
      | 'PDF_TO_XLSX'
      | 'SCRIPT'
      | 'LLM_SCRIPT'
      | 'REVIEW'
      | 'JEM_TEMPLATE_FETCH'
      | 'NO_OP'
      | 'FLOLAKE';
  };
  'data-testid'?: string;
};
export const StepResultJemLink: React.FC<StepResultJEMLinkProps> = ({
  outputValue,
  'data-testid': dataTestId,
}: StepResultJEMLinkProps) => {
  return (
    <>
      <Styled.FileWrapper data-testid={dataTestId}>
        <Styled.FileContainer>
          <Styled.IconContainer>
            <JournalEntriesIcon />
          </Styled.IconContainer>
          <ExternalJemLink outputValue={outputValue} />
        </Styled.FileContainer>
      </Styled.FileWrapper>
    </>
  );
};
