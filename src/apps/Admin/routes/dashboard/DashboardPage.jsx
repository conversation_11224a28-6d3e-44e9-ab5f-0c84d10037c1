import { Suspense } from 'react';
import { useSuspenseInfiniteQuery, useSuspenseQueries } from '@tanstack/react-query';
import { t } from '@/utils/i18n';
import { Heading, Table, THead, TBody, TH, TR, TD } from '@floqastinc/flow-ui_core';
import v3 from '@/services/v3';
import { getWorkflowsQuery } from '@BuilderV3/api/workflows';
import { Loading } from '@/components/Loading';
import styled from 'styled-components';

const HeadingWithMargin = styled(Heading)`
  margin: 15px 0;
`;

const isTerminalStatus = (status) => ['COMPLETED', 'FAILED', 'CANCELED'].includes(status);

export const DashboardPage = () => {
  const {
    data: { workflows },
    isSuccess: workflowsIsSuccess,
  } = useSuspenseInfiniteQuery(getWorkflowsQuery());

  const workflowRuns = useSuspenseQueries({
    queries: workflows?.length
      ? workflows.map((workflow) => {
          return {
            queryKey: ['workflowRuns', workflow.id],
            queryFn: () => v3.runs.getWorkflowRuns({ workflowId: workflow.id, first: 100 }),
          };
        })
      : [],
    combine: (results) => {
      return results.flatMap((result) => result.data?.data).filter(Boolean);
    },
    enabled: workflowsIsSuccess,
  });

  const taskRuns = useSuspenseQueries({
    queries: workflowRuns?.length
      ? workflowRuns.map((run) => {
          return {
            queryKey: ['taskRuns', run.id],
            queryFn: () => v3.runs.getTaskRuns({ workflowRunId: run.id, first: 100 }),
          };
        })
      : [],
    combine: (results) => {
      return results.flatMap((result) => result.data?.data).filter(Boolean);
    },
    enabled: workflowsIsSuccess,
  });

  const totalRuns = workflowRuns.length;
  const runsByStatus = workflowRuns.reduce((acc, run) => {
    acc[run.status] = (acc[run.status] || 0) + 1;
    return acc;
  }, {});

  const taskStatsByStrategy = taskRuns.reduce((acc, run) => {
    if (isTerminalStatus(run.status)) {
      const kind = run.strategy.kind;
      const { total, completed } = acc[kind] || { total: 0, completed: 0 };
      const newTotal = total + 1;
      const newCompleted = run.status === 'COMPLETED' ? completed + 1 : completed;
      acc[kind] = {
        total: newTotal,
        completed: newCompleted,
        completionRate: Math.round((newCompleted / newTotal) * 100),
      };
    }
    return acc;
  }, {});

  const taskStatsByDateAndStrategy = taskRuns.reduce((acc, run) => {
    if (isTerminalStatus(run.status)) {
      const kind = run.strategy.kind;
      const date = run.createdAt?.toLocaleDateString();
      if (!acc[kind]) {
        acc[kind] = {};
      }
      if (!acc[kind][date]) {
        acc[kind][date] = {
          total: 0,
          completed: 0,
        };
      }
      acc[kind][date].total += 1;
      acc[kind][date].completed += run.status === 'COMPLETED' ? 1 : 0;
      // calculate completion rate
      acc[kind][date].completionRate = Math.round(
        (acc[kind][date].completed / acc[kind][date].total) * 100,
      );
    }
    return acc;
  }, {});

  // only terminal runs are included in the completion rate calculation
  const totalAttemptedRuns = workflowRuns.filter((run) => isTerminalStatus(run.status)).length;

  const runsByDateAndStatus = workflowRuns.reduce((acc, run) => {
    if (!acc[run.createdAt?.toLocaleDateString()]) {
      acc[run.createdAt?.toLocaleDateString()] = {};
    }
    acc[run.createdAt?.toLocaleDateString()][run.status] =
      (acc[run.createdAt?.toLocaleDateString()][run.status] || 0) + 1;
    return acc;
  }, {});

  const completionRateByDate = Object.entries(runsByDateAndStatus).reduce(
    (acc, [date, statusObject]) => {
      const totalAttemptedRunsForDate = Object.entries(statusObject)
        .filter(([status]) => isTerminalStatus(status)) // only terminal runs are included in the completion rate calculation
        .reduce((acc, [, count]) => acc + count, 0);
      const completedRuns = statusObject['COMPLETED'] || 0;
      acc[date] = totalAttemptedRunsForDate
        ? `${Math.round((completedRuns / totalAttemptedRunsForDate) * 100)}%`
        : '-';
      return acc;
    },
    {},
  );

  const completionRate = totalAttemptedRuns
    ? `${Math.round(((runsByStatus['COMPLETED'] ?? 0) / totalAttemptedRuns) * 100)}%`
    : '-';

  return (
    <div>
      <HeadingWithMargin variant="h1">{t('generics.dashboard')}</HeadingWithMargin>
      <div>
        <Suspense fallback={<Loading />}>
          <div>
            <HeadingWithMargin variant="h2">{t('components.Admin.runStats')}</HeadingWithMargin>
            <HeadingWithMargin variant="h5">
              {t('components.Admin.totalRuns', { totalRuns })}
            </HeadingWithMargin>
            <HeadingWithMargin variant="h5">
              {t('components.Admin.attemptedRuns', { totalAttemptedRuns })}
            </HeadingWithMargin>
            <HeadingWithMargin variant="h5">
              {t('components.Admin.completionRate', { completionRate })}
              {t('components.Admin.completedAttempted')}
            </HeadingWithMargin>
            <HeadingWithMargin variant="h5">{t('components.Admin.runsByStatus')}</HeadingWithMargin>
            <Table topBorder middleBorders outerRoundedBorder>
              <THead>
                <TR>
                  <TH>{t('generics.status')}</TH>
                  <TH>{t('generics.count')}</TH>
                </TR>
              </THead>
              <TBody>
                {Object.entries(runsByStatus).map(([status, count]) => (
                  <TR key={status}>
                    <TD>{status}</TD>
                    <TD>{count}</TD>
                  </TR>
                ))}
              </TBody>
            </Table>

            <HeadingWithMargin variant="h5">{t('components.Admin.runsByDate')}</HeadingWithMargin>
            <Table topBorder middleBorders outerRoundedBorder>
              <THead>
                <TR>
                  <TH>{t('generics.status')}</TH>
                  <TH>{t('generics.count')}</TH>
                </TR>
              </THead>
              <TBody>
                {Object.entries(runsByDateAndStatus)
                  // sort by date, ascending
                  .sort(([date1], [date2]) => new Date(date1) - new Date(date2))
                  .map(([date, innerObject]) => (
                    <TR key={date}>
                      <TD colSpan={2}>
                        <strong>{date}</strong>
                        <Table nested>
                          <TBody>
                            {Object.entries(innerObject).map(([status, count]) => (
                              <TR key={`${date}_${status}`}>
                                <TD>{status}</TD>
                                <TD>{count}</TD>
                              </TR>
                            ))}
                            <TR>
                              <TD colSpan={2}>
                                <strong>{t('components.Admin.completionRate')}</strong>{' '}
                                {completionRateByDate[date]}
                              </TD>
                            </TR>
                          </TBody>
                        </Table>
                      </TD>
                    </TR>
                  ))}
              </TBody>
            </Table>

            <HeadingWithMargin variant="h2">{t('components.Admin.taskRunStats')}</HeadingWithMargin>

            <HeadingWithMargin variant="h5">
              {t('components.Admin.taskRunStatsByStrat')}
            </HeadingWithMargin>
            <Table topBorder middleBorders outerRoundedBorder>
              <THead>
                <TR>
                  <TH>{t('generics.strategy')}</TH>
                  <TH>{t('generics.total')}</TH>
                  <TH>{t('generics.completion')} %</TH>
                </TR>
              </THead>
              <TBody>
                {Object.entries(taskStatsByStrategy).map(
                  ([strategy, { total, completionRate }]) => (
                    <TR key={strategy}>
                      <TD>{strategy}</TD>
                      <TD>{total}</TD>
                      <TD>{completionRate}%</TD>
                    </TR>
                  ),
                )}
              </TBody>
            </Table>

            <HeadingWithMargin variant="h5">
              {t('components.Admin.taskRunStatsByDate')}
            </HeadingWithMargin>
            <Table topBorder outerRoundedBorder>
              <THead>
                <TR>
                  <TH>{t('generics.date')}</TH>
                  <TH>{t('generics.total')}</TH>
                  <TH>{t('generics.completion')} %</TH>
                </TR>
              </THead>
              <TBody>
                {Object.entries(taskStatsByDateAndStrategy).map(([strategy, innerObject]) => (
                  <>
                    <TR thickBottomBorder>
                      <TD colSpan={3}>{strategy}</TD>
                    </TR>
                    {Object.entries(innerObject)
                      // sort by date, ascending
                      .sort(([date1], [date2]) => new Date(date1) - new Date(date2))
                      .map(([date, { total, completionRate }]) => (
                        <TR key={`${date}_${strategy}`}>
                          <TD style={{ paddingLeft: 22 }}>{date}</TD>
                          <TD style={{ paddingLeft: 22 }}>{total}</TD>
                          <TD style={{ paddingLeft: 22 }}>{completionRate}</TD>
                        </TR>
                      ))}
                  </>
                ))}
              </TBody>
            </Table>

            <HeadingWithMargin variant="h2">
              {t('components.Admin.workflowStats')}
            </HeadingWithMargin>
            <Table topBorder middleBorders outerRoundedBorder>
              <THead>
                <TR>
                  <TH>{t('components.Admin.workflowID')}</TH>
                  <TH>{t('generics.name')}</TH>
                  <TH>{t('components.Admin.totalRuns')}</TH>
                  <TH>{t('components.Admin.completionRate')}</TH>
                  <TH>{t('components.Admin.runDetails')}</TH>
                </TR>
              </THead>
              <TBody>
                <TR>
                  <TD colSpan={4} />
                  <TD>
                    <Table nested>
                      <TBody>
                        <TR>
                          <TH>{t('components.Admin.workflowRunID')}</TH>
                          <TH>{t('generics.status')}</TH>
                          <TH>{t('components.Admin.createdAt')}</TH>
                        </TR>
                      </TBody>
                    </Table>
                  </TD>
                </TR>
                {workflows?.map((workflow) => {
                  const thisWorkflowsRuns = workflowRuns.filter(
                    (run) => run.workflowId === workflow.id,
                  );
                  const thisWorkflowsRunsCompletionRate = thisWorkflowsRuns.filter((run) =>
                    isTerminalStatus(run.status),
                  ).length
                    ? `${Math.round((thisWorkflowsRuns.filter((run) => run.status === 'COMPLETED').length / thisWorkflowsRuns.filter((run) => isTerminalStatus(run.status)).length) * 100)}%`
                    : '-';
                  return (
                    <TR key={workflow.id}>
                      <TD>{workflow.id}</TD>
                      <TD>{workflow.name}</TD>
                      <TD>{thisWorkflowsRuns.length}</TD>
                      <TD>{thisWorkflowsRunsCompletionRate}</TD>
                      <TD>
                        <Table nested>
                          <TBody>
                            {thisWorkflowsRuns.map((run) => (
                              <TR key={run.id}>
                                <TD>{run.id}</TD>
                                <TD>{run.status}</TD>
                                <TD>{new Date(run.createdAt).toLocaleDateString()}</TD>
                              </TR>
                            ))}
                          </TBody>
                        </Table>
                      </TD>
                    </TR>
                  );
                })}
              </TBody>
            </Table>
          </div>
        </Suspense>
      </div>
    </div>
  );
};
