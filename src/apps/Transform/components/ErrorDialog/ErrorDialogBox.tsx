import React, { useState } from 'react';
import { Dialog } from '@floqastinc/flow-ui_core';
import { match } from 'ts-pattern';
import * as Styled from './ErrorDialogBox.styled';

type ErrorStepModalProps = {
  errorMessage: string;
  errorDetails?: object;
  children: React.ReactNode;
};

type copyStatus = 'idle' | 'success' | 'error';

export const ErrorDialogBox = ({
  errorMessage,
  errorDetails = {},
  children,
}: ErrorStepModalProps) => {
  const [isOpen, setIsErrorModalOpen] = useState(false);
  const [copyStatus, setCopyStatus] = useState<copyStatus>('idle');

  const handleCopyToClipboard = async () => {
    try {
      const textToCopy = JSON.stringify(
        { ...errorDetails, errorMessage },
        null,
        2,
      );
      await navigator.clipboard.writeText(textToCopy);
      setCopyStatus('success');
      setTimeout(() => {
        setCopyStatus('idle');
      }, 2000);
    } catch {
      setCopyStatus('error');
      setTimeout(() => {
        setCopyStatus('idle');
      }, 2000);
    }
  };

  const detailsList = Object.entries(errorDetails).map(([key, value]) => (
    <li key={key}>
      {key}: {value}
    </li>
  ));

  return (
    <Dialog open={isOpen} onOpenChange={setIsErrorModalOpen} type="warning">
      <Dialog.Trigger>{children}</Dialog.Trigger>
      <Dialog.Header>Something Went Wrong</Dialog.Header>
      <Dialog.Body>
        Try submitting again or contact <strong><EMAIL></strong> if
        the error persists.
        <Styled.ErrorCodeBox>
          {detailsList.length > 0 && (
            <>
              <strong>Error Details</strong>
              <Styled.ErrorList>{detailsList}</Styled.ErrorList>
            </>
          )}
          <strong>Error Message</strong>
          {errorMessage}
        </Styled.ErrorCodeBox>
      </Dialog.Body>
      <Dialog.Footer>
        <Dialog.FooterCancelBtn
          onClick={handleCopyToClipboard}
          onKeyDown={() => {}}
        >
          {match(copyStatus)
            .with('idle', () => 'Copy Error to Clipboard')
            .with('success', () => 'Copied!')
            .with('error', () => 'Error copying')
            .run()}
        </Dialog.FooterCancelBtn>
        <Dialog.FooterActionBtn onClick={() => setIsErrorModalOpen(false)}>
          Close
        </Dialog.FooterActionBtn>
      </Dialog.Footer>
    </Dialog>
  );
};
