import { InfiniteQueryObserverResult } from '@tanstack/react-query';
import { useCallback, useRef } from 'react';

interface UseInfiniteScrollObserverProps {
  onIntersect: () => Promise<InfiniteQueryObserverResult>;
  isFetching: boolean;
  hasMore: boolean;
}

export function useInfiniteScrollObserver({
  onIntersect,
  isFetching,
  hasMore,
}: UseInfiniteScrollObserverProps) {
  const intersectionObserverRef = useRef<IntersectionObserver | null>(null);
  const lastObservedRowRef = useRef<HTMLElement | null>(null);

  const lastRowRef = useCallback(
    (row: HTMLDivElement | null) => {
      if (!row || isFetching) return;

      if (lastObservedRowRef.current === row) return;

      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }

      const observer = new IntersectionObserver(async (entries) => {
        const entry = entries[0];

        if (entry.isIntersecting && hasMore && !isFetching) {
          observer.disconnect(); // Prevent duplicate fetches
          intersectionObserverRef.current = null;

          await onIntersect();
        }
      });

      observer.observe(row);
      intersectionObserverRef.current = observer;
      lastObservedRowRef.current = row;
    },
    [isFetching, hasMore, onIntersect],
  );

  return lastRowRef;
}
