import { AGENTS, B<PERSON>LDER, <PERSON><PERSON><PERSON><PERSON><PERSON>, STEPS, V3 } from '@/constants';
import { useExamples } from '@v3/examples';
import { useTask, useTasks } from '@v3/tasks';
import { sortBy } from 'es-toolkit';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

type UseRedirectToActiveExampleSetArgs =
  | {
      disabled?: boolean;
    }
  | undefined;

/**
 * Loads the default task and default exampleSet for that task from a
 *  given workflowId.
 *
 * TODO: This is purposefully named rather... suspiciously.
 *  Our loading pattern here is a bit messy since it requires a
 *  waterfall of requests if we tried to implement this from the AgentsList.
 *  Ideally, we move this to a BFF endpoint,
 *  but for now, we have this sus loader which is a good candidate
 *  for refactor/rethinking.
 *
 *  The main idea is to move this to a `loader`, but also to move to
 *   TanStack Router (most likely) which has some better integrations
 *   with react-query and has better support for typesafe routes/searchparams.
 */
export const useRedirectToActiveExampleSet = ({
  disabled = false,
}: UseRedirectToActiveExampleSetArgs = {}) => {
  const { workflowId = '', taskId, exampleSetId } = useParams();
  const navigate = useNavigate();

  // Used to disable queries to prevent overfetching. Queries should
  //  not run when all params are present. Otherwise, if any is not present,
  //  the UI should be updated do have a specific example set.
  // TODO: This will _not_ hold true when global inputs need to be implemented.
  const anyQueryShouldBeDisabled = (workflowId && taskId && exampleSetId) || disabled;

  // Only run if a specified taskId is not provided. This can happen
  // from the AgentList page.
  const { data: tasks, isPending: isTasksPending } = useTasks(
    { workflowId },
    {
      enabled: !taskId && !anyQueryShouldBeDisabled,
    },
  );
  const isTasksLoading = isTasksPending && !taskId;

  const { data: task, isPending: isTaskPending } = useTask(
    { workflowId, taskId: taskId as string },
    {
      enabled: !!taskId && !anyQueryShouldBeDisabled,
    },
  );
  const isTaskLoading = isTaskPending && !!taskId;

  const latestTask = tasks ? tasks[tasks.length - 1] : undefined;
  const taskToUse = task || latestTask;

  // This will run if a taskId is provided or if a latestTask exists.
  const { data: examples, isPending: isExamplesPending } = useExamples(
    { workflowId, taskId: taskToUse?.id as string },
    {
      enabled: !!taskToUse?.id && !anyQueryShouldBeDisabled,
    },
  );
  const isExamplesLoading = isExamplesPending && !!taskToUse?.id;

  const isPending = isTasksLoading || isTaskLoading || isExamplesLoading;

  useEffect(() => {
    if (!isPending && !disabled) {
      if (examples && !exampleSetId) {
        const activeExample = examples
          ? examples.find((example) => example.status === 'ACTIVE')
          : undefined;
        const latestExample = sortBy(examples, ['createdAt']).reverse()[0];

        const exampleToUse = activeExample ?? latestExample;
        // Mismatch: the exampleSet should match up to the current task
        const exampleToRouteTo = exampleToUse.taskId === taskToUse?.id ? exampleToUse : undefined;

        if (examples && !exampleToRouteTo) {
          console.error(examples?.length > 0 ? 'No active example found' : 'No examples found');
          navigate('/builder');
        }

        if (workflowId && (taskId || latestTask?.id) && exampleToRouteTo?.id) {
          const taskIdToUse = taskId || latestTask?.id;
          // If taskId is provided in params (i.e. in case of Preview Step or Restore Step)
          // then we default tothat.
          const url = `/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${taskIdToUse}/${EXAMPLES}/${exampleToRouteTo.id}`;
          navigate(url, { replace: true });
        }
      }
    }
  }, [
    workflowId,
    latestTask?.id,
    taskId,
    examples,
    navigate,
    taskToUse?.id,
    isPending,
    disabled,
    exampleSetId,
  ]);

  return {
    isRedirecting: isPending,
  };
};
