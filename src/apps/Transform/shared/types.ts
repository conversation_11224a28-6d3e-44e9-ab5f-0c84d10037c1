import { ArgumentValue, WorkflowInput } from '@floqastinc/transform-v3';

type MapInputValue<T extends ArgumentValue> = T extends { kind: 'FILE' }
  ? { kind: 'FILE'; value: File } & WorkflowInput
  : T extends { kind: 'DATETIME' }
    ? { kind: 'DATETIME'; value: Date | undefined } & WorkflowInput
    : T extends { kind: 'TEXT' }
      ? { kind: 'TEXT'; value: string } & WorkflowInput
      : T extends { kind: 'NUMBER' }
        ? { kind: 'NUMBER'; value: number | string } & WorkflowInput
        : never;
export type InputValue = MapInputValue<ArgumentValue>;
