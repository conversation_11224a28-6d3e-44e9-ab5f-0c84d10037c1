import Styled from 'styled-components';

export const ExperimentSettings = Styled.div`
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  width: 400px;
  gap: 16px;
`;

export const ExperimentGroup = Styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 24px;
`;

export const ExperimentRow = Styled.div`
  display: flex;
  flex-direction: column;
  align-items: left;
  gap: 8px;
  width: 100%;
`;

export const ExperimentLabel = Styled.div`
  min-width: 140px; // Adjust based on longest label
  text-align: left;
  flex-shrink: 0;
`;
