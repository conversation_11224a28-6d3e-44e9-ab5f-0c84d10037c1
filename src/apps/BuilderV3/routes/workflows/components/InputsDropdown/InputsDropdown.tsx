import {
  Task,
  TaskOutput,
  TaskSource,
  WorkflowInput,
  WorkflowSource,
} from '@floqastinc/transform-v3';
// eslint-disable-next-line import-x/no-unresolved
import { TaskSourceSchema, WorkflowSourceSchema } from '@floqastinc/transform-v3/lib/v3/schemas';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { DropdownPanel, DropdownButton } from '@floqastinc/flow-ui_core';
import { useEffect, useState } from 'react';
import { capitalize } from 'lodash';
import * as Styled from './styled';
import { t } from '@/utils/i18n';
import v3, { ApiError } from '@/services/v3';
import { queryKeys } from '@BuilderV3/api/query-keys';
import { getTaskInputsQuery } from '@BuilderV3/api/task-inputs';
import { getWorkflowTasksQuery } from '@BuilderV3/api/tasks';
import { getWorkflowInputsQuery } from '@BuilderV3/api/workflow-inputs';
import { queryClient } from '@/components/queryClient';
import { sortObjectArrByKeyOrder } from '@/utils/object';

// TODO: This is a temporary solution to make task output names more readable.
// We should implement the ability to rename task outputs.
const getTaskOutputName = (task: Task, taskOutput: TaskOutput) => {
  return taskOutput.name === 'default'
    ? `${task.name} Output ${capitalize(taskOutput.type)}`
    : taskOutput.name;
};

export const InputsDropdown = () => {
  const params = useParams();
  const workflowId = params.workflowId ?? '';
  const taskId = params.taskId ?? '';
  const exampleSetId = params.exampleSetId ?? '';

  const navigate = useNavigate();
  if (!workflowId || !taskId || !exampleSetId) {
    console.warn(t('components.Builder.Errors.noIDsprovided'));
    navigate('/builder');
  }

  const [isInputsDropdownOpen, setIsInputsDropdownOpen] = useState(false);

  const workflowInputsQuery = useQuery({
    ...getWorkflowInputsQuery(workflowId),
    enabled: !!workflowId,
  });
  const tasksQuery = useQuery({
    ...getWorkflowTasksQuery(workflowId),
    enabled: !!workflowId,
  });
  const tasks = tasksQuery.data ?? [];
  const taskInputsQuery = useQuery({
    ...getTaskInputsQuery({ workflowId, taskId }),
    enabled: !!workflowId && !!taskId,
  });

  const sources = useQuery({
    queryKey: ['sources', { workflowId, taskId }],
    queryFn: async () => {
      const response = await v3.tasks.getTaskSources({
        workflowId,
        taskId,
      });

      if (response.errors?.length) {
        throw new ApiError(response.errors);
      }
      if (!response.data) {
        throw new Error(t('components.Builder.Errors.noDataReturned'));
      }
      return response.data;
    },
    enabled: !!workflowId && !!taskId,
  });

  const previousTasks = tasks.slice(
    0,
    tasks.findIndex((task) => task.id === taskId),
  );

  type DropdownOption = { id: string; name: string; type: 'INPUT' | 'OUTPUT' };
  const initialSourcesByStep = previousTasks.reduce(
    (acc, task) => {
      if (!acc[task.name]) {
        acc[task.name] = {
          inputs: [],
          outputs: [],
        };
      }

      return acc;
    },
    {} as {
      [x: string]: { inputs: DropdownOption[]; outputs: DropdownOption[] };
    },
  );

  const min = (a: number, b: number) => (a < b ? a : b);
  const earliestTaskBySource = sources.data?.reduce(
    (acc, source) => {
      const duplicateValues = sources.data?.filter((s) => s.id === source.id) ?? [];
      const allTaskInputs = source.taskInputs.concat(duplicateValues.flatMap((d) => d.taskInputs));
      const allTaskOutputs = source.taskOutputs.concat(
        duplicateValues.flatMap((d) => d.taskOutputs),
      );

      const minTaskIndexForInputs = allTaskInputs
        .map((ti) => ti.taskId)
        .map((taskId) => tasks.findIndex((task) => task.id === taskId))
        .filter((taskIndex) => taskIndex !== -1)
        .reduce(min, Infinity);
      const minTaskIndexForOutputs = allTaskOutputs
        .map((to) => to.taskId)
        .map((taskId) => tasks.findIndex((task) => task.id === taskId))
        .filter((taskIndex) => taskIndex !== -1)
        .reduce(min, Infinity);

      const earliestTask =
        minTaskIndexForInputs < minTaskIndexForOutputs
          ? tasks[minTaskIndexForInputs]
          : tasks[minTaskIndexForOutputs];

      acc[source.id] = earliestTask;

      return acc;
    },
    {} as { [valueId: string]: Task },
  );

  const sourcesByStep = sources.data?.reduce((acc, source) => {
    // Values with at least one taskInput and no taskOutputs should
    //  be a workflowInput
    if (source.taskInputs.length > 0 && source.taskOutputs.length === 0) {
      source.taskInputs.forEach((taskInput) => {
        const task = tasks.find((task) => task.id === taskInput.taskId);
        if (!task) {
          throw new Error(t('components.Builder.Errors.taskNotFound'));
        }
        if (taskInput.source && 'workflowInputId' in taskInput.source) {
          if (!acc[task.name]) {
            acc[task.name] = {
              inputs: [],
              outputs: [],
            };
          }
          if (earliestTaskBySource?.[source.id].id === task.id) {
            acc[task.name].inputs.push({
              id: taskInput.source.workflowInputId,
              name: taskInput.name,
              type: 'INPUT',
            });
          }
        }
      });
    } else if (source.taskOutputs.length > 0) {
      // Values that are referenced by taskOutputs should be task outputs.
      source.taskOutputs.forEach((taskOutput) => {
        const task = tasks.find((task) => task.id === taskOutput.taskId);
        if (!task) {
          throw new Error(t('components.Builder.Errors.taskNotFound'));
        }
        if (!acc[task.name]) {
          acc[task.name] = {
            inputs: [],
            outputs: [],
          };
        }
        if (earliestTaskBySource?.[source.id].id === task.id) {
          const name = getTaskOutputName(task, taskOutput);
          acc[task.name].outputs.push({
            id: taskOutput.id,
            name,
            type: 'OUTPUT',
          });
        }
      });
    }
    return acc;
  }, initialSourcesByStep);

  // The sortObjectArrByKeyOrder is used here to order the steps
  //  in the input dropdown in the correct order.
  const inputOptionsByStep = sortObjectArrByKeyOrder(
    sourcesByStep
      ? Object.entries(sourcesByStep).map(([step, { inputs, outputs }]) => {
          const inputsNotAlreadyIncluded = inputs.filter((inputOption) => {
            const taskInputIncludeWorkflowInput = taskInputsQuery.data?.find(
              (taskInput) =>
                WorkflowSourceSchema.safeParse(taskInput.source).success &&
                taskInput.source.workflowInputId === inputOption.id,
            );
            return !taskInputIncludeWorkflowInput;
          });

          const outputsNotAlreadyIncluded = outputs.filter((outputOption) => {
            const taskInputIncludesTaskOutput = taskInputsQuery.data?.find(
              (taskInput) =>
                TaskSourceSchema.safeParse(taskInput.source).success && taskInput.source.taskOutputId === outputOption.id,
            );
            return !taskInputIncludesTaskOutput;
          });

          return {
            step,
            inputs: [...inputsNotAlreadyIncluded, ...outputsNotAlreadyIncluded],
          };
        })
      : [],
    previousTasks.map((task) => task.name),
    'step',
  );
  const [allInputOptionsEmpty, setAllInputOptionsEmpty] = useState(true);
  useEffect(() => {
    if (inputOptionsByStep.length > 0) {
      setAllInputOptionsEmpty(inputOptionsByStep.every(({ inputs }) => inputs.length === 0));
    }
  }, [inputOptionsByStep]);

  const allTaskOutputs = sources.data?.flatMap((source) => source.taskOutputs);

  const addInputToTask = useMutation({
    mutationKey: ['addInputToTask', { workflowId, taskId }],
    mutationFn: async (params: { workflowId: string; option: DropdownOption }) => {
      const { workflowId, option } = params;
      const { type, id: optionId } = option;
      let input: WorkflowInput | TaskOutput | undefined;
      let sourceTask: Task | undefined;
      if (type === 'INPUT') {
        input = workflowInputsQuery.data?.find((workflowInput) => workflowInput.id === optionId);
      } else if (type === 'OUTPUT') {
        input = allTaskOutputs?.find((taskOutput) => taskOutput.id === optionId);
        sourceTask = tasks.find((task) => task.id === (input as TaskOutput).taskId);
      }
      if (!input) {
        throw new Error(t('components.Builder.Errors.inputNotFound') + optionId);
      }
      const source: WorkflowSource | TaskSource =
        type === 'INPUT'
          ? { workflowInputId: input.id }
          : { taskId: (input as TaskOutput).taskId, taskOutputId: input.id };
      const taskInputResponse = await v3.taskInputs.createTaskInput({
        workflowId,
        taskId,
        input: {
          name:
            type === 'INPUT'
              ? input.name
              : getTaskOutputName(sourceTask as Task, input as TaskOutput),
          type: input.type,
          source,
        },
      });
      if (taskInputResponse.errors?.length) {
        throw new ApiError(taskInputResponse.errors);
      }
      if (!taskInputResponse.data) {
        throw new Error(t('components.Builder.Errors.noDataReturnedCTI'));
      }

      const exampleInputResponse = await v3.exampleInputs.createExampleInput({
        workflowId,
        taskId,
        exampleSetId,
        input: {
          taskInputId: taskInputResponse.data.id,
        },
      });
      if (exampleInputResponse.errors?.length) {
        throw new ApiError(exampleInputResponse.errors);
      }
      if (!exampleInputResponse.data) {
        throw new Error(t('components.Builder.Errors.noDataReturnedCEI'));
      }
      const exampleInput = exampleInputResponse.data;

      let inputResponse;
      const setExampleInputParams = {
        workflowId,
        taskId,
        exampleSetId,
        exampleInputId: exampleInput.id,
        value: source,
      };
      if (input.type === 'FILE') {
        inputResponse = await v3.exampleInputs.setExampleInputFileValue(setExampleInputParams);
      } else if (input.type === 'TEXT') {
        inputResponse = await v3.exampleInputs.setExampleInputText(setExampleInputParams);
      } else if (input.type === 'NUMBER') {
        inputResponse = await v3.exampleInputs.setExampleInputNumber(setExampleInputParams);
      } else if (input.type === 'DATETIME') {
        inputResponse = await v3.exampleInputs.setExampleInputDatetime(setExampleInputParams);
      } else {
        throw new Error(t('components.Builder.Errors.unexpectedWorkflowInput') + input.type);
      }

      if (inputResponse.errors?.length) {
        throw new ApiError(inputResponse.errors);
      }
      if (!inputResponse.data) {
        throw new Error(t('components.Builder.Errors.noDataReturnedSEIFV'));
      }

      return {
        taskInput: taskInputResponse.data,
        exampleInput: exampleInputResponse.data,
      };
    },
    onSuccess: (_data) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.taskInputs.byTask({ workflowId, taskId }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleInputs.getExampleInputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: ['sources', { workflowId, taskId }],
      });
      setIsInputsDropdownOpen(false);
    },
    onError: (err) => {
      console.error(err);
    },
  });

  return (
    <Styled.TaskInputDropdown>
      <DropdownPanel
        isOpen={isInputsDropdownOpen}
        disableFilter={true}
        onChange={(option: DropdownOption) => {
          addInputToTask.mutate({
            workflowId,
            option,
          });
        }}
        onOpenChange={(isOpen: boolean) => {
          setIsInputsDropdownOpen(isOpen);
        }}
      >
        <DropdownPanel.Trigger>
          <DropdownButton disabled={allInputOptionsEmpty}>
            {t('components.Builder.addInputsPrevSteps')}
          </DropdownButton>
        </DropdownPanel.Trigger>
        <DropdownPanel.Content>
          {inputOptionsByStep.map(({ step, inputs }) => (
            <DropdownPanel.Group key={step} title={step}>
              {inputs.map((input) => (
                <DropdownPanel.Option value={input} key={input.id}>
                  {input.name}
                </DropdownPanel.Option>
              ))}
            </DropdownPanel.Group>
          ))}
        </DropdownPanel.Content>
      </DropdownPanel>
    </Styled.TaskInputDropdown>
  );
};
