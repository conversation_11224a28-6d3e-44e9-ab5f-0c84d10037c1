import { describe, expect, test, vi } from 'vitest';
import { userEvent } from '@vitest/browser/context';
import { ExampleSet, Task } from '@floqastinc/transform-v3';
import { Chat } from '.';
import { customRender, createBrowserWrapper } from '@/utils/testing';
import { useExample } from '@v3/examples';
import { useTask } from '@v3/tasks';
import { useMessages } from '@v3/messages';
import { createMessage } from '@/api/bff/messages';

vi.mock('@v3/examples');
vi.mock('@v3/tasks');
vi.mock('@v3/messages');
vi.mock('i18next');
vi.mock('@/utils/i18n', () => ({
  t: vi.fn((key) => {
    if (key === 'components.Builder.typeCommand') {
      return 'Type Command';
    }
    return key;
  }),
}));

vi.mock('@/api/bff/messages');

const MOCK_WORKFLOW_ID = 'test-workflow-id';
const MOCK_TASK_ID = 'test-task-id';
const MOCK_EXAMPLE_SET_ID = 'test-example-set-id';
const MOCK_DRAFT_THREAD_EXAMPLE: ExampleSet = {
  id: 'example-id',
  status: 'DRAFT',
  workflowId: MOCK_WORKFLOW_ID,
  taskId: MOCK_TASK_ID,
  name: 'Example Name',
  createdAt: new Date('2023-10-01T00:00:00Z'),
  strategy: {
    conversationStatus: 'READY',
    kind: 'LLM_THREAD',
  },
};

const MOCK_TASK: Task = {
  id: MOCK_TASK_ID,
  name: 'Test Task',
  description: 'This is a test task',
  workflowId: MOCK_EXAMPLE_SET_ID,
  createdAt: new Date('2023-10-01T00:00:00Z'),
  strategy: {
    kind: 'LLM_THREAD',
    status: 'READY',
  },
};

vi.mocked(useTask).mockReturnValue({
  data: MOCK_TASK,
} as ReturnType<typeof useTask>);

vi.mocked(useMessages).mockReturnValue({
  data: undefined,
} as ReturnType<typeof useMessages>);

describe('Chat', () => {
  test('GIVEN a user is viewing an example in draft, THEN the chat textarea should be visible', async () => {
    // Arrange
    vi.mocked(useExample).mockReturnValue({
      data: MOCK_DRAFT_THREAD_EXAMPLE,
    } as ReturnType<typeof useExample>);

    const screen = customRender(
      <Chat
        workflowId={MOCK_WORKFLOW_ID}
        taskId={MOCK_TASK_ID}
        exampleSetId={MOCK_EXAMPLE_SET_ID}
      />,
      {
        wrapper: createBrowserWrapper(),
      },
    );

    // Assert
    await expect.element(screen.getByPlaceholder('Type Command')).toBeVisible();
  });

  test('GIVEN a user is viewing an example not in draft, THEN the chat textarea should not to be rendered', async () => {
    // Arrange
    vi.mocked(useExample).mockReturnValue({
      data: {
        ...MOCK_DRAFT_THREAD_EXAMPLE,
        status: 'ACTIVE',
      },
    } as ReturnType<typeof useExample>);
    vi.mocked(createMessage).mockReturnValue(new Promise(() => {}));

    const screen = customRender(
      <Chat
        workflowId={MOCK_WORKFLOW_ID}
        taskId={MOCK_TASK_ID}
        exampleSetId={MOCK_EXAMPLE_SET_ID}
      />,
      {
        wrapper: createBrowserWrapper(),
      },
    );

    // Assert
    await expect.element(screen.getByPlaceholder('Type Command')).not.toBeInTheDocument();
  });

  test('GIVEN a user sends a chat to the LLM, THEN the chat textarea should be cleared', async () => {
    // Arrange
    vi.mocked(useExample).mockReturnValue({
      data: MOCK_DRAFT_THREAD_EXAMPLE,
    } as ReturnType<typeof useExample>);
    vi.mocked(createMessage).mockReturnValue(new Promise(() => {}));

    const screen = customRender(
      <Chat
        workflowId={MOCK_WORKFLOW_ID}
        taskId={MOCK_TASK_ID}
        exampleSetId={MOCK_EXAMPLE_SET_ID}
      />,
      {
        wrapper: createBrowserWrapper(),
      },
    );

    const textArea = screen.getByPlaceholder('Type Command');
    const submitButton = screen.getByTestId('submit-chat-button');

    // User actions
    await userEvent.fill(textArea, 'Some prompt.');
    await userEvent.click(submitButton);

    // Assert
    await expect.element(textArea).toHaveValue('');
  });
});
