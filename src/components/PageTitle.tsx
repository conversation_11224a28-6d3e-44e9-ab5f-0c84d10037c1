import React from 'react';
import { useEffect } from 'react';

type PageTitleProps = {
  title: string;
};

// sets page title, based on component from here https://dev.to/rohitnirban/adding-page-titles-to-react-app-23oe
export const PageTitle = ({ title }: PageTitleProps) => {
  const appName = 'FloQast Transform';
  const hostname = window.location.hostname.split('.')[0];

  let env = '';
  if (hostname === 'localhost') {
    env = ' [local]';
  }
  if (hostname === 'dev') {
    env = ' [dev]';
  }

  const fullTitle = `${appName}${env} - ${title}`;

  return <title>{fullTitle}</title>;
};
