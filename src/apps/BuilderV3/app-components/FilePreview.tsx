import React from 'react';
import { t } from '@/utils/i18n';
import { Heading } from '@floqastinc/flow-ui_core';
import { ErrorBoundary } from 'react-error-boundary';
import { SpreadsheetWithStateProvider } from './Spreadsheet';

type AnyFunction = (...args: any[]) => any;
export interface FilePreviewProps {
  file: File;
  setSelectedRange?: AnyFunction; // TODO: use the correct type!
  setActiveSheet?: AnyFunction; // TODO: use the correct type!
}

export const FilePreview: React.FC<FilePreviewProps> = (props) => {
  return (
    <ErrorBoundary
      fallback={
        <Heading variant="body-base">{t('components.Builder.Errors.unexpectedFileError')}</Heading>
      }
    >
      <FilePreviewInternals {...props} />
    </ErrorBoundary>
  );
};

const noop = () => {};

const FilePreviewInternals: React.FC<FilePreviewProps> = ({
  file,
  setSelectedRange,
  setActiveSheet,
}) => {
  if (!file) return null;

  const fileExtension = getFileExtension(file.name);

  switch (fileExtension) {
    case 'csv':
    case 'xls':
    case 'xlsx':
      return (
        <SpreadsheetWithStateProvider
          file={file}
          setSelectedRange={setSelectedRange ?? noop}
          setActiveSheet={setActiveSheet ?? noop}
        />
      );
    default:
      return (
        <Heading variant="body-base">
          {t('components.Builder.fileType')} {fileExtension}{' '}
          {t('components.Builder.notSupportPreview')}
        </Heading>
      );
  }
};

const getFileExtension = (filename: string = '') => {
  return filename.split('.').pop();
};
