import { describe, test, expect, beforeEach, vi } from 'vitest';
import { ExampleSet } from '@floqastinc/transform-v3';
import { FilePreview } from './index';
import { customRender } from '@/utils/testing';

// Create a proper type mock for xlsx
vi.mock('xlsx', () => {
  return {
    read: vi.fn().mockReturnValue({
      Sheets: {
        Sheet1: {
          '!ref': 'A1:C2',
          A1: { t: 's', v: 'Header1', h: 'Header1', w: 'Header1' },
          B1: { t: 's', v: 'Header2', h: 'Header2', w: 'Header2' },
          C1: { t: 's', v: 'Header3', h: 'Header3', w: 'Header3' },
          A2: { t: 's', v: 'Data1', h: 'Data1', w: 'Data1' },
          B2: { t: 's', v: 'Data2', h: 'Data2', w: 'Data2' },
          C2: { t: 's', v: 'Data3', h: 'Data3', w: 'Data3' },
        },
      },
      SheetNames: ['Sheet1'],
    }),
    utils: {
      sheet_to_json: vi.fn().mockImplementation((sheet, options) => {
        if (options && options.header === 'A') {
          return [
            { A: 'Header1', B: 'Header2', C: 'Header3' },
            { A: 'Data1', B: 'Data2', C: 'Data3' },
          ];
        }
        return [{ Header1: 'Data1', Header2: 'Data2', Header3: 'Data3' }];
      }),
      decode_col: vi.fn().mockImplementation((col: string | number) => {
        const colMap: Record<string, number> = { A: 0, B: 1, C: 2 };
        return typeof col === 'string' ? colMap[col] || 0 : col;
      }),
      encode_col: vi.fn().mockImplementation((num: number) => {
        const numMap: Record<number, string> = { 0: 'A', 1: 'B', 2: 'C' };
        return numMap[num] || 'A';
      }),
      decode_cell: vi.fn().mockImplementation((cell) => {
        const match = cell.match(/^([A-Z]+)(\d+)$/);
        if (match) {
          const _col = match[1];
          const row = parseInt(match[2], 10);
          return { c: 0, r: row - 1 };
        }
        return { c: 0, r: 0 };
      }),
      encode_cell: vi.fn().mockImplementation(({ c, r }) => {
        const col = String.fromCharCode(65 + c);
        return `${col}${r + 1}`;
      }),
      decode_range: vi.fn().mockImplementation((_range) => {
        return { s: { c: 0, r: 0 }, e: { c: 2, r: 1 } };
      }),
    },
  };
});

describe('FilePreview', () => {
  const mockFile = new File(['test-content'], 'test.xlsx', {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const defaultProps = {
    file: mockFile,
    isLoading: false,
    mode: 'runner' as const,
    previewName: 'Test Output',
    example: {
      createdAt: new Date(),
      id: '',
      name: 'TEST_VERSION',
      status: 'PUBLISHED',
      strategy: {
        conversationStatus: 'READY',
        kind: 'SCRIPT',
      },
      taskId: '',
      workflowId: '',
    } as ExampleSet,
    fileTabs: [],
    activeFileId: '',
    onFileTabClick: (_fileId: string) => {},
    onFileTabClose: (_fileId: string) => {},
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test(`GIVEN a file and isLoading is false,
    THEN it should display the file data, sheet tabs, and name`, async () => {
    const screen = customRender(<FilePreview {...defaultProps} />);

    await expect.element(screen.getByText('Test Output')).toBeVisible();
    await expect.element(screen.getByText('Sheet1')).toBeVisible();
  });

  test(`GIVEN isLoading is true,
       THEN it should show the loading state`, async () => {
    const screen = customRender(<FilePreview {...defaultProps} isLoading={true} />);

    await expect.element(screen.getByLabelText('Loading Spinner')).toBeVisible();
  });

  test(`GIVEN file is null and isLoading is false,
       THEN it should show the empty state with the i18n text for no data preview`, async () => {
    const screen = customRender(<FilePreview {...defaultProps} file={null} />);

    // Using data-testid to find the empty state container
    await expect.element(screen.getByTestId('empty-file-preview')).toBeVisible();
  });

  // TODO: These two tests will need to be updated once the badge is implemented
  test(`GIVEN builder mode,
       THEN it should display the status badge`, async () => {
    const screen = customRender(<FilePreview {...defaultProps} mode="builder" />);

    await expect.element(screen.getByText('Test Output')).toBeVisible();
    await expect.element(screen.getByText('TEST_VERSION')).toBeVisible();
    await expect.element(screen.getByText('components.Runner.makeActive')).toBeVisible();
  });

  test(`GIVEN runner mode,
       THEN it should not display the status badge`, async () => {
    const screen = customRender(<FilePreview {...defaultProps} mode="runner" />);

    await expect.element(screen.getByText('Step 1 Output')).not.toBeInTheDocument();
    await expect.element(screen.getByText('TEST_VERSION')).not.toBeInTheDocument();
    await expect.element(screen.getByText('components.Runner.makeActive')).not.toBeInTheDocument();
  });
});
