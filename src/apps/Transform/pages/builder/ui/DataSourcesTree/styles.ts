import { Sidebar } from '@floqastinc/flow-ui_core';
import { styled } from 'styled-components';
export const SearchContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 10px;
`;

export const BackgroundSearchContainer = styled.div`
  position: 'sticky',
  top: 0,
  zIndex: 0,
  backgroundColor: 'var(--flo-sem-color-surface-neutral-base)',
  `;

export const SideBar = styled(Sidebar)`
  border-right: none;
  max-width: none;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 8px 0px 8px 8px;
`;

export const SkeletonContainer = styled.div`
  padding: '12px';
  display: 'flex';
  flexdirection: 'column';
`;
