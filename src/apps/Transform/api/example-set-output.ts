import { useQuery } from '@tanstack/react-query';
import v3 from '@/services/v3';
import { getFileFromUri } from '@BuilderV3/api/files';

/**
 * Hook to retrieve the preview file for an example set.
 * First tries to get an output file, and if none exists, falls back to an input file.
 *
 * @param workflowId The workflow ID
 * @param taskId The task ID
 * @param exampleSetId The example set ID
 * @returns Query result containing { file, type } or null if no file found
 */
export const useExampleSetPreviewFile = (
  workflowId: string,
  taskId: string,
  exampleSetId: string,
) => {
  return useQuery({
    queryKey: ['exampleSetPreviewFile', workflowId, taskId, exampleSetId],
    queryFn: async () => {
      if (!workflowId || !taskId || !exampleSetId) return null;

      try {
        // First try to get the output file
        const exampleOutputsRes = await v3.exampleOutputs.getExampleOutputs({
          workflowId,
          taskId,
          exampleSetId,
        });

        if (exampleOutputsRes.data?.length > 0) {
          const exampleOutput = exampleOutputsRes.data.find(
            (output) => output.value?.kind === 'FILE',
          );

          if (exampleOutput) {
            try {
              const outputUriRes = await v3.exampleOutputs.getTaskOutputExampleUri({
                workflowId,
                taskId,
                exampleSetId,
                exampleOutputId: exampleOutput.id,
              });

              if (!outputUriRes.errors.length && outputUriRes.data?.url) {
                const file = await getFileFromUri(outputUriRes.data.url);
                return { file, type: 'Output' as const };
              }
            } catch {
              // Output URI fetch failed, try to get the input file
            }
          }
        }

        // If no output file found or error occurred, try to get an input file
        const exampleInputsRes = await v3.exampleInputs.getExampleInputs({
          workflowId,
          taskId,
          exampleSetId,
        });

        if (exampleInputsRes.data?.length > 0) {
          const exampleInput = exampleInputsRes.data.find((input) => input.value?.kind === 'FILE');

          if (exampleInput) {
            try {
              const inputUriRes = await v3.exampleInputs.getTaskInputExampleUri({
                workflowId,
                taskId,
                exampleSetId,
                exampleInputId: exampleInput.id,
              });

              if (!inputUriRes.errors.length && inputUriRes.data?.url) {
                const file = await getFileFromUri(inputUriRes.data.url);
                return { file, type: 'Input' as const };
              }
            } catch {
              // Input URI fetch failed, return null
            }
          }
        }

        return null;
      } catch (error) {
        console.error('Error fetching preview file:', error);
        return null;
      }
    },
    enabled: !!workflowId && !!taskId && !!exampleSetId,
    staleTime: 1000 * 30, // 30 seconds
    gcTime: 1000 * 60, // 1 minute
  });
};
