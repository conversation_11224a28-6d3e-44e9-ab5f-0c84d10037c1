import React, { useCallback, useState } from 'react';
import { t } from '@/utils/i18n';
import { useSuspenseQuery, useQueryClient } from '@tanstack/react-query';

import {
  Button,
  Dialog,
  DropdownButton,
  DropdownPanel,
  Flex,
  Heading,
  useToast,
  TableStatusBadge,
  // @ts-ignore
} from '@floqastinc/flow-ui_core';
// @ts-ignore
import { Link, useParams } from 'react-router-dom';
import { Team, UserRole } from '@floqastinc/transform-v0';
import v0, { ApiError } from '@/services/v0';
import { useCurrentUser } from '@/utils/principal';

const options: Record<UserRole, string> = {
  ADMIN: 'ADMIN',
  OMNI: 'OMNI',
  SUPER: 'SUPER',
  USER: 'USER',
};

export const UserPage: React.FC = () => {
  const { userId } = useParams();

  if (!userId) return null;

  return <WrappedUserPage userId={userId} />;
};

const WrappedUserPage: React.FC<{ userId: string }> = ({ userId }) => {
  const queryClient = useQueryClient();
  const {
    data: { user, team },
  } = useSuspenseQuery({
    queryKey: ['users', userId],
    queryFn: async () => {
      const userRes = await v0.users.getUser({ userId });
      if (userRes.errors.length) throw new ApiError(userRes.errors);

      if (!userRes.data) return { user: undefined, team: undefined };

      const teamRes = await v0.teams.getTeam({ teamId: userRes.data.teamId });
      if (teamRes.errors.length) throw new ApiError(teamRes.errors);

      return { user: userRes.data, team: teamRes.data };
    },
  });

  const handleChangeRole = useCallback(async (e: React.ChangeEvent<HTMLSelectElement>) => {
    console.log('change role', e.target.value);

    const res = await v0.users.updateUser({
      userId,
      user: {
        role: e.target.value as UserRole,
      },
    });

    if (res.errors.length) {
      console.error(res.errors);
      showToast(
        <Toast type="error">
          <Toast.Title>{t('components.Admin.somethingWentWrong')}</Toast.Title>
          <Toast.Message>{t('components.Admin.unableToUpdateUserRole')}</Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    } else {
      showToast(
        <Toast type="success">
          <Toast.Title>{t('components.Admin.roleUpdated')}</Toast.Title>
          <Toast.Message>{t('components.Admin.userRoleUpdated')}</Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    }
  }, []);

  const { showToast, Toast } = useToast();

  const [resendingEmail, setResendingEmail] = React.useState(false);
  const handleResendEmail = useCallback(async () => {
    setResendingEmail(true);
    const res = await v0.users.resendInvite({ userId });
    setResendingEmail(false);
    if (res.errors.length) {
      console.error(res.errors);
      showToast(
        <Toast type="error">
          <Toast.Title>{t('components.Admin.somethingWentWrong')}</Toast.Title>
          <Toast.Message>
            {t('components.Admin.unableTempPassword', { userEmail: user?.email })}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    } else {
      showToast(
        <Toast type="success">
          <Toast.Title>{t('components.Admin.emailSent')}</Toast.Title>
          <Toast.Message>
            {t('components.Admin.emailedTempPassword', { userEmail: user?.email })}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    }
  }, [userId]);

  const [confirmation, setConfirmation] = React.useState('');
  const handleConfirmationChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmation(e.target.value);
  }, []);

  const [confirmingDeletion, setConfirmingDeletion] = React.useState(false);

  const handleCancelDeleteClick = useCallback(async () => {
    setConfirmation('');
    setConfirmingDeletion(false);
  }, []);

  const [deleting, setDeleting] = React.useState(false);

  const handleConfirmDeleteClick = useCallback(async () => {
    setConfirmation('');
    setConfirmingDeletion(false);
    setDeleting(true);
    const res = await v0.users.deleteUser({ userId });

    if (res.errors.length) {
      console.error(res.errors);
      showToast(
        <Toast type="error">
          <Toast.Title>{t('components.Admin.somethingWentWrong')}</Toast.Title>
          <Toast.Message>
            {t('components.Admin.unableDeleteUser', { userEmail: user?.email })}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    } else {
      showToast(
        <Toast type="success">
          <Toast.Title>{t('components.Admin.userDeleted')}</Toast.Title>
          <Toast.Message>
            {t('components.Admin.userDeletedMessage', { userEmail: user?.email })}
          </Toast.Message>
        </Toast>,
        { position: 'top-right' },
      );
    }
  }, []);

  const currentUser = useCurrentUser();

  const { data: teams } = useSuspenseQuery({
    queryKey: ['teams'],
    queryFn: async () => {
      const res = await v0.teams.getTeams({ first: 100 });
      if (res.errors.length) throw new ApiError(res.errors);
      return res.data ?? [];
    },
  });

  const [teamToSwitchTo, setTeamToSwitchTo] = useState<Team>();
  const [isConfirmingTeamSwitch, setIsConfirmingTeamSwitch] = useState<boolean>();
  const [isTeamSwitcherOpen, setIsTeamSwitcherOpen] = useState<boolean>();

  const handleConfirmSwitchTeams = useCallback(
    (teamId: string) => {
      if (user && teamId !== user.teamId) {
        setTeamToSwitchTo(teams.find((t) => t.id === teamId));
        setIsConfirmingTeamSwitch(true);
      }
      setIsTeamSwitcherOpen(false);
    },
    [user?.teamId],
  );

  const handleSwitchTeams = useCallback(
    async (teamId: string | undefined) => {
      if (!teamId) return;

      const res = await v0.users.updateUser({ userId, user: { teamId } });
      if (!res.errors.length && res.data) {
        if (userId === currentUser?.id) {
          // signOut();
        } else {
          setIsConfirmingTeamSwitch(false);
          queryClient.invalidateQueries(['users', userId] as any); // Expects a string, but this is the correct query key
        }
      } else {
        console.error(res.errors);
      }
    },
    [userId, currentUser?.id],
  );

  if (!user)
    return (
      <div>
        <Heading>Team</Heading>
        <Heading variant="h2">{t('components.Admin.notFound')}</Heading>
      </div>
    );

  return (
    <div>
      <Heading>
        <Flex gap="8">
          <span>{user.email}</span>
          {user.id === currentUser?.id && <TableStatusBadge>{t('generics.you')}</TableStatusBadge>}
        </Flex>
      </Heading>
      <div style={{ marginTop: 24 }}>
        {t('generics.role')}:{' '}
        <select name="userRole" id="userRole" onChange={handleChangeRole}>
          {Object.entries(options).map(([key, value]) => (
            <option key={key} value={key} selected={user.role === key}>
              {value}
            </option>
          ))}
        </select>
      </div>
      <div>{t('generics.userStatus', { status: user.status })}</div>
      {!!team && (
        <div>
          <Flex gap={8}>
            <span>{t('generics.team')}:</span>
            <Link to={`/admin/teams/${team.id}`}>{team.name}</Link>
            <DropdownPanel
              selectedValues={user.teamId}
              isOpen={isTeamSwitcherOpen}
              onChange={handleConfirmSwitchTeams}
              onOpenChange={setIsTeamSwitcherOpen}
              disableClear
              disableFilter
            >
              <DropdownPanel.Trigger>
                <DropdownButton>{t('components.Admin.switchTeams')}</DropdownButton>
              </DropdownPanel.Trigger>
              <DropdownPanel.Content>
                {teams.map((team) => (
                  <DropdownPanel.Option key={team.id} value={team.id}>
                    {team.name}
                  </DropdownPanel.Option>
                ))}
              </DropdownPanel.Content>
            </DropdownPanel>
            <Dialog
              open={isConfirmingTeamSwitch}
              onOpenChange={setIsConfirmingTeamSwitch}
              type="danger"
            >
              <Dialog.Header>{t('components.Admin.changeTeam')}</Dialog.Header>
              <Dialog.Body>
                {user.id === currentUser?.id ? (
                  <p>
                    {t('components.Admin.actionWillMoveYou', {
                      userEmail: user.email,
                      teamName: teamToSwitchTo?.name,
                    })}
                  </p>
                ) : (
                  <p>
                    {t('components.Admin.actionWillMoveUser', {
                      userEmail: user.email,
                      teamName: teamToSwitchTo?.name,
                    })}{' '}
                  </p>
                )}
              </Dialog.Body>
              <Dialog.Footer>
                <Dialog.FooterCancelBtn onClick={() => setIsConfirmingTeamSwitch(false)}>
                  Cancel
                </Dialog.FooterCancelBtn>
                <Dialog.FooterActionBtn
                  color="danger"
                  onClick={() => handleSwitchTeams(teamToSwitchTo?.id)}
                >
                  Change team{user.id === currentUser?.id ? ' and log out' : ''}
                </Dialog.FooterActionBtn>
              </Dialog.Footer>
            </Dialog>
          </Flex>
        </div>
      )}
      <div style={{ marginTop: 24 }}>
        <Heading variant="h2">Actions</Heading>
        <Flex gap={8} style={{ marginTop: '.5rem' }}>
          {user.status === 'FORCE_CHANGE_PASSWORD' && (
            <Button disabled={resendingEmail} onClick={handleResendEmail}>
              {t('components.Admin.resendEmail')}
            </Button>
          )}
          <Dialog open={confirmingDeletion} onOpenChange={setConfirmingDeletion} type="danger">
            <Dialog.Trigger color="danger">
              <Button disabled={deleting}>{t('components.Admin.deleteUser')}</Button>
            </Dialog.Trigger>
            <Dialog.Header>{t('components.Admin.permDeleteUser')}</Dialog.Header>
            <Dialog.Body>
              <p>{t('components.Admin.userDeleteUndone')}</p>
              <p style={{ marginTop: '8px' }}>
                {t('components.Admin.typeDelete')}:{' '}
                <input
                  style={{ border: '1px solid grey' }}
                  type="text"
                  placeholder="delete"
                  value={confirmation}
                  onChange={handleConfirmationChange}
                />
              </p>
            </Dialog.Body>
            <Dialog.Footer>
              <Dialog.FooterCancelBtn onClick={handleCancelDeleteClick}>
                {t('generics.cancel')}
              </Dialog.FooterCancelBtn>
              <Dialog.FooterActionBtn
                disabled={confirmation !== 'delete'}
                color="danger"
                onClick={handleConfirmDeleteClick}
              >
                {t('components.Admin.permanentlyDelete')}
              </Dialog.FooterActionBtn>
            </Dialog.Footer>
          </Dialog>
        </Flex>
      </div>
    </div>
  );
};
