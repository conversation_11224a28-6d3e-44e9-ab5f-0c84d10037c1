import v3, { ApiError } from '@/services/v3';
import { queryOptions } from '@tanstack/react-query';
import { t } from '@/utils/i18n';
import { getFileFromUri } from './files';
import { queryKeys } from './query-keys';
import { FileOptions, QueryContext, TaskId, WorkflowId } from './types';

// TODO: Fix task output stuff, likely just in example sets
const getTaskOutputFile = async ({
  queryKey: [
    {
      filter: { workflowId },
    },
    {
      filter: { taskId },
    },
  ],
}: QueryContext<(typeof queryKeys.taskOutputs)['byTask']>) => {
  const [taskOutputsRes, taskInputsRes] = await Promise.all([
    v3.taskOutputs.getTaskOutputs({
      workflowId,
      taskId,
    }),
    v3.taskInputs.getTaskInputs({
      workflowId,
      taskId,
    }),
  ]);
  const taskOutput = taskOutputsRes.data[0];
  const taskInput = taskInputsRes.data[0];

  const [taskOutputRes, taskInputRes] = await Promise.all([
    v3.taskOutputs.getTaskOutput({
      workflowId,
      taskId,
      taskOutputId: taskOutput.id,
    }),
    v3.taskInputs.getTaskInput({
      workflowId,
      taskId,
      taskInputId: taskInput.id,
    }),
  ]);

  if (
    taskOutputRes.errors.length > 0 &&
    !taskOutputRes.errors.some((error) => error.code === 'NOT_FOUND')
  ) {
    console.error(t('components.Builder.Errors.taskIDoutput') + `${taskId}`, taskOutputRes.errors);
    throw new ApiError(taskOutputRes.errors);
  } else if (taskOutputRes.data) {
    const file = await getFileFromUri(taskOutputRes.data);
    return {
      fileUri: taskOutputRes.data,
      file,
      isTaskOutput: true,
    };
  }

  if (taskInputRes.errors.length) {
    console.error(t('components.Builder.Errors.taskIDinput') + `${taskId}`, taskInputRes.errors);
    throw new ApiError(taskInputRes.errors);
  } else if (!taskInputRes.data) {
    throw new Error(t('components.Builder.Errors.failedGetTaskInputURI'));
  }

  const file = await getFileFromUri(taskInputRes.data);

  return {
    fileUri: taskInputRes.data,
    file,
    isTaskOutput: false,
  };
};

/**
 *  Gets task's current output file. If no output exists,
 * the input to the task is considered its output.
 *
 * Note: This retrieves the file itself, not just the presigned URI.
 */
export const getTaskOutputFileQuery = ({
  workflowId,
  taskId,
  fileOptions,
}: WorkflowId & TaskId & Partial<FileOptions>) => {
  return queryOptions({
    queryKey: queryKeys.taskOutputs.byTask({
      workflowId,
      taskId,
      ...(fileOptions ? { options: { fileOptions } } : {}),
    }),
    queryFn: getTaskOutputFile,
    retry: 0,
  });
};

const getTaskOutputs = async ({
  queryKey: [
    {
      filter: { workflowId },
    },
    {
      filter: { taskId },
    },
  ],
}: QueryContext<(typeof queryKeys.taskOutputs)['byTask']>) => {
  const { data, errors } = await v3.taskOutputs.getTaskOutputs({
    workflowId,
    taskId,
  });
  if (errors.length) {
    throw new ApiError(errors);
  }
  if (!data) {
    throw new Error(t('components.Builder.Errors.failedGetTaskOutputs'));
  }
  return data;
};

export const getTaskOutputsQuery = ({ workflowId, taskId }: WorkflowId & TaskId) => {
  return queryOptions({
    queryKey: queryKeys.taskOutputs.byTask({ workflowId, taskId }),
    queryFn: getTaskOutputs,
  });
};
