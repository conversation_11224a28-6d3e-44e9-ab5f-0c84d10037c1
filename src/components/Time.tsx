import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useEffect, useState } from 'react';
import { FQIntl } from '@floqastinc/fq-intl';

// TODO: consider moving this to a shared location
dayjs.extend(relativeTime);

export interface TimeProps {
  value: Date | undefined | null;
  relative?: boolean;
  live?: boolean;
}

/**
 * Renders a time element with a formatted date and optionally relative time.
 */
export const Time = ({ value, relative, live = true }: TimeProps) => {
  if (!value) return null;

  const formatter = new FQIntl.DateTimeFormat({
    dateStyle: 'short',
  });
  const formattedDate = formatter.format(value);

  // eslint-disable-next-line react-hooks/rules-of-hooks
  const [text, setText] = useState<string>(relative ? dayjs(value).fromNow() : formattedDate);

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    if (!relative || !live) return;

    const interval = setInterval(() => {
      setText(dayjs(value).fromNow());
    }, 5000); // TODO: exponential backoff for longer intervals

    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <time dateTime={value.toISOString()} title={formattedDate}>
      {text}
    </time>
  );
};
