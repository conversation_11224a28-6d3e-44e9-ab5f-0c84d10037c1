import React from 'react';
import { Button, Flex, Table, TBody, THead, TR, TH, TD } from '@floqastinc/flow-ui_core';
import { match } from 'ts-pattern';
import TeamDropdownPanel from './TeamDropdownPanel';
import { TEAMS_PAGE_STRINGS } from '@/utils/string';

interface TeamsListProps {
  teams: Array<{ id: string; name: string; externalId?: string }>;
  fetchNextPage: () => void;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
  status?: string;
  onEditTeam: (teamId: string, name: string, externalId?: string) => void;
  onDeleteTeam: (teamId: string, teamName: string) => void;
}

const TeamsList: React.FC<TeamsListProps> = ({
  teams,
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage,
  status,
  onEditTeam,
  onDeleteTeam,
}) => {
  return match(status)
    .with('pending', () => <div>{TEAMS_PAGE_STRINGS.PAGE_SUCCESS.LOADING}</div>)
    .with('error', () => <div>{TEAMS_PAGE_STRINGS.PAGE_ERRORS.ERROR_LOADING_TEAMS}</div>)
    .otherwise(() => (
      <Flex gap={48} wrap="wrap" style={{ marginTop: '3rem' }}>
        <Table topBorder middleBorders outerRoundedBorder>
          <THead>
            <TR>
              <TH>{TEAMS_PAGE_STRINGS.TABLE_HEADERS.NAME}</TH>
              <TH>{TEAMS_PAGE_STRINGS.TABLE_HEADERS.EXTERNAL_ID}</TH>
              <TH></TH>
            </TR>
          </THead>
          <TBody>
            {teams.length > 0 ? (
              teams.map((team) => (
                <TR key={team.id}>
                  <TD>{team.name}</TD>
                  <TD>{team.externalId}</TD>
                  <TD>
                    <TeamDropdownPanel
                      teamId={team.id}
                      name={team.name}
                      externalId={team.externalId}
                      onDeleteTeam={() => onDeleteTeam(team.id, team.name)}
                      onEditTeam={() => onEditTeam(team.id, team.name, team.externalId)}
                    />
                  </TD>
                </TR>
              ))
            ) : (
              <TR>
                <TD colSpan={4}>{TEAMS_PAGE_STRINGS.PAGE_ERRORS.NO_TEAMS}</TD>
              </TR>
            )}
          </TBody>
        </Table>
        {hasNextPage && (
          <Flex
            style={{
              justifyContent: 'center',
              marginTop: '2rem',
              width: '100%',
            }}
          >
            <Button onClick={fetchNextPage} disabled={isFetchingNextPage}>
              {isFetchingNextPage ? 'Loading more...' : 'Load More'}
            </Button>
          </Flex>
        )}
      </Flex>
    ));
};

export default TeamsList;
