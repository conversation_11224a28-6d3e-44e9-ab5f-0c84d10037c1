import { useState } from 'react';
import { Icon<PERSON><PERSON>on, DropdownPanel, Dialog, Input } from '@floqastinc/flow-ui_core';
import { t } from '@/utils/i18n';
import MoreVert from '@floqastinc/flow-ui_icons/material/MoreVert';
import { useNavigate } from 'react-router';
import { useMutation } from '@tanstack/react-query';
import { ExampleSet, ExampleSetUpdate } from '@floqastinc/transform-v3';
import { deleteExample } from '@BuilderV3/api/examples';
import v3 from '@/services/v3';
import { queryClient } from '@/components/queryClient';
import { queryKeys } from '@BuilderV3/api/query-keys';
import { useFeatureFlags } from '@/components/FeatureFlag';
import { BUILDER, STEPS, V3, AGENTS } from '@/constants';

type VersionDropdownActions = 'rename' | 'switch-active-state' | 'archive' | 'delete';
type VersionOptionsDropdownProps = {
  example: ExampleSet;
  disableDelete?: boolean;
};
export const VersionOptionsDropdown = ({ example, disableDelete }: VersionOptionsDropdownProps) => {
  if (!example) {
    console.error(t('components.Builder.Errors.noExamplePropsVOD'));
    return null;
  }
  const { workflowId, taskId } = example;

  const navigate = useNavigate();

  const { getFlag } = useFeatureFlags();

  const [isVersionOptionsOpen, setIsVersionOptionsOpen] = useState(false);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [newVersionName, setNewVersionName] = useState('');

  const updateVersionMutation = useMutation({
    mutationFn: (updatedExample: ExampleSetUpdate) =>
      v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId: example.id,
        example: updatedExample,
      }),
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExample({
          workflowId,
          taskId,
          exampleSetId: example.id,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExamples({ workflowId, taskId }),
      });
      setIsRenameDialogOpen(false);
    },
  });

  const deleteExampleMutation = useMutation({
    mutationFn: async () => {
      // Prevent having zero exampleSets
      return deleteExample({
        workflowId,
        taskId,
        exampleSetId: example.id,
      });
    },
    onSuccess: async (deletedExample) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExamples({ workflowId, taskId }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleInputs.getExampleInputs({
          workflowId,
          taskId,
          exampleSetId: deletedExample.id,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleOutputs.getExampleOutputs({
          workflowId,
          taskId,
          exampleSetId: deletedExample.id,
        }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExample({
          workflowId,
          taskId,
          exampleSetId: deletedExample.id,
        }),
      });

      navigate(`/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${taskId}/`);
    },
  });

  return (
    <>
      <Dialog type="info" open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <Dialog.Header>{t('components.Builder.renameVersion')}</Dialog.Header>
        <Dialog.Body>
          <Input
            label={t('components.Builder.newVersionName')}
            placeholder={example.name}
            value={newVersionName}
            onChange={setNewVersionName}
          />
        </Dialog.Body>
        <Dialog.Footer>
          <Dialog.FooterCancelBtn
            onClick={() => setIsRenameDialogOpen(false)}
            data-tracking-id="builder-cancel-rename-version-button"
          >
            {t('generics.cancel')}
          </Dialog.FooterCancelBtn>
          <Dialog.FooterActionBtn
            disabled={!newVersionName || updateVersionMutation.isPending}
            onClick={() => {
              updateVersionMutation.mutate({
                name: newVersionName,
              });
            }}
            data-tracking-id="builder-confirm-rename-version-button"
          >
            {t('generics.confirm')}
          </Dialog.FooterActionBtn>
        </Dialog.Footer>
      </Dialog>
      <DropdownPanel
        isOpen={isVersionOptionsOpen}
        onOpenChange={setIsVersionOptionsOpen}
        disableClear
        disableFilter
        onChange={(value: VersionDropdownActions) => {
          if (value === 'delete') {
            deleteExampleMutation.mutate();
          } else if (value === 'rename') {
            setIsRenameDialogOpen(true);
          }
        }}
      >
        <DropdownPanel.Trigger>
          <IconButton onClick={() => {}}>
            <MoreVert />
          </IconButton>
        </DropdownPanel.Trigger>
        <DropdownPanel.Content style={{ zIndex: 101 }}>
          <DropdownPanel.Option value="rename">
            {t('components.Builder.renameVersion')}
          </DropdownPanel.Option>
          {getFlag('inputs-ux') ? (
            <DropdownPanel.Option
              value="delete"
              disabled={disableDelete}
              style={{
                cursor: disableDelete ? 'not-allowed' : 'pointer',
              }}
            >
              {t('components.Builder.deleteVersion')}
            </DropdownPanel.Option>
          ) : null}
        </DropdownPanel.Content>
      </DropdownPanel>
    </>
  );
};
