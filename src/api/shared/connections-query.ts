import { useQuery, useQueryClient } from '@tanstack/react-query';
import ConnectionsAPI from './connections';
import { FlolakeResponse } from './types';

interface FlolakeResponseWrapper {
  data: FlolakeResponse;
}

export const connectionsKeys = {
  all: ['connections'] as const,
  providers: () => [...connectionsKeys.all, 'providers'] as const,
  active: () => [...connectionsKeys.all, 'active'] as const,
  flolake: () => [...connectionsKeys.all, 'flolake'] as const,
  providerDetail: (id: string) => [...connectionsKeys.providers(), id] as const,
};

export const useAvailableConnections = () => {
  return useQuery({
    queryKey: connectionsKeys.providers(),
    queryFn: ConnectionsAPI.getAvailableConnections,
  });
};

export const useActiveConnections = () => {
  return useQuery({
    queryKey: connectionsKeys.active(),
    queryFn: ConnectionsAPI.getActiveConnections,
  });
};

export const useFlolakeData = () => {
  const queryClient = useQueryClient();
  const queryKey = connectionsKeys.flolake();

  const fetchFlolakeData = async () => {
    await queryClient.prefetchQuery({
      queryKey,
      queryFn: ConnectionsAPI.getFlolakeData,
      staleTime: 5 * 60 * 1000,
      gcTime: 30 * 60 * 1000,
    });
  };

  const getCachedFlolakeData = () => {
    const cachedData = queryClient.getQueryData<FlolakeResponseWrapper>(queryKey);
    return cachedData?.data;
  };

  const queryResult = useQuery<FlolakeResponse, Error, FlolakeResponse>({
    queryKey,
    queryFn: ConnectionsAPI.getFlolakeData,
    staleTime: 5 * 60 * 1000,
    initialData: getCachedFlolakeData(),
  });

  return { ...queryResult, fetchFlolakeData };
};
