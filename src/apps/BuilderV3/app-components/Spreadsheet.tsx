import React, { useEffect, useMemo, useState } from 'react';
// @ts-ignore
import { Heading } from '@floqastinc/flow-ui_core';
import { t } from '@/utils/i18n';
import {
  SpreadsheetProvider,
  CanvasGrid,
  Sheet,
  CellData,
  Toolbar,
  ButtonRedo,
  ButtonUndo,
  ScaleSelector,
  ToolbarSeparator,
  BackgroundColorSelector,
  BorderSelector,
  ButtonBold,
  ButtonDecreaseDecimal,
  ButtonFormatCurrency,
  ButtonFormatPercent,
  ButtonIncreaseDecimal,
  ButtonInsertImage,
  ButtonItalic,
  ButtonStrikethrough,
  ButtonSwitchColorMode,
  ButtonUnderline,
  DEFAULT_FONT_SIZE_PT,
  FontFamilySelector,
  FontSizeSelector,
  MergeCellsSelector,
  TextColorSelector,
  TextFormatSelector,
  TextHorizontalAlignSelector,
  TextVerticalAlignSelector,
  TextWrapSelector,
  ThemeSelector,
  SpreadsheetTheme,
  defaultSpreadsheetTheme,
  ColorMode,
  EmbeddedChart,
  EmbeddedObject,
  NamedRange,
  TableView,
  BottomBar,
  NewSheetButton,
  SheetStatus,
  SheetSwitcher,
  SheetTabs,
  FormulaBar,
  FormulaBarInput,
  FormulaBarLabel,
  RangeSelector,
  SheetSearch,
  TableStyleSelector,
  SelectionAttributes,
} from 'rowsncolumns/packages/spreadsheet/dist/esm';
import {
  DeleteSheetConfirmation,
  NamedRangeEditor,
  pattern_currency_decimal,
  pattern_percent_decimal,
  SheetData,
  RowData,
  TableEditor,
  useSearch,
  useSpreadsheetState,
} from 'rowsncolumns/packages/spreadsheet-state/dist/esm';
import { IconButton, Separator } from 'rowsncolumns/packages/ui/dist/esm';
import { functionDescriptions, functions } from 'rowsncolumns/packages/functions/dist/esm';
import { MagnifyingGlassIcon } from 'rowsncolumns/packages/icons/dist/esm';
import { uuid } from 'rowsncolumns/packages/utils/dist/esm';
import {
  createRowDataFromExcelFile,
  createRowDataFromCSVFile,
} from 'rowsncolumns/packages/toolkit/dist/esm';
import { SelectionArea } from 'rowsncolumns/packages/grid';

const initialSheets: Sheet[] = [
  {
    title: 'Sheet1',
    sheetId: uuid(),
    rowCount: 1_000,
    columnCount: 1_00,
  },
];

interface SpreadsheetWithStateProps {
  file: File | null;
  setSelectedRange?: Function;
  setActiveSheet: Function;
}

const SpreadsheetWithState: React.FC<SpreadsheetWithStateProps> = ({
  file,
  setSelectedRange,
  setActiveSheet,
}) => {
  const [sheets, onChangeSheets] = useState<Sheet[]>(initialSheets);
  const [sheetData, onChangeSheetData] = useState<SheetData<CellData>>({});
  const [scale, onChangeScale] = useState(1);
  const [theme, onChangeTheme] = useState<SpreadsheetTheme>(defaultSpreadsheetTheme);
  const [colorMode, onChangeColorMode] = useState<ColorMode>();
  const [charts, onChangeCharts] = useState<EmbeddedChart[]>([]);
  const [embeds, onChangeEmbeds] = useState<EmbeddedObject[]>([]);
  const [tables, onChangeTables] = useState<TableView[]>([]);
  const [namedRanges, onChangeNamedRanges] = useState<NamedRange[]>([]);
  const locale = 'en-GB';
  const currency = 'USD';
  const [hasError, setHasError] = useState(false);

  const {
    activeCell,
    activeSheetId,
    selections,
    rowCount,
    getDataRowCount,
    columnCount,
    frozenColumnCount,
    frozenRowCount,
    rowMetadata,
    columnMetadata,
    merges,
    bandedRanges,
    spreadsheetColors,
    canRedo,
    canUndo,
    onUndo,
    onRedo,
    getCellData,
    getSheetName,
    getUserEnteredFormat,
    getEffectiveValue,
    onRequestCalculate,
    onChangeActiveCell,
    onChangeActiveSheet,
    onSelectNextSheet,
    onSelectPreviousSheet,
    onChangeSelections,
    onChange,
    onDelete,
    onChangeFormatting,
    onRepeatFormatting,
    onClearFormatting,
    onUnMergeCells,
    onMergeCells,
    onResize,
    onChangeBorder,
    onChangeDecimals,
    onChangeSheetTabColor,
    onRenameSheet,
    onRequestDeleteSheet,
    onDeleteSheet,
    onShowSheet,
    onHideSheet,
    onProtectSheet,
    onUnProtectSheet,
    onMoveSheet,
    onCreateNewSheet,
    onDuplicateSheet,
    onHideColumn,
    onShowColumn,
    onHideRow,
    onShowRow,
    onFill,
    onFillRange,
    onMoveChart,
    onResizeChart,
    onDeleteChart,
    onMoveEmbed,
    onResizeEmbed,
    onDeleteEmbed,
    onDeleteRow,
    onDeleteColumn,
    onDeleteCellsShiftUp,
    onDeleteCellsShiftLeft,
    onInsertCellsShiftRight,
    onInsertCellsShiftDown,
    onInsertRow,
    onInsertColumn,
    onMoveColumns,
    onMoveRows,
    onMoveSelection,
    onSortColumn,
    onSortTable,
    onFilterTable,
    onResizeTable,
    onCopy,
    onPaste,
    onCreateTable,
    onRequestEditTable,
    onUpdateTable,
    onDragOver,
    onDrop,
    onInsertFile,
    onFreezeColumn,
    onFreezeRow,
    onChangeSpreadsheetTheme,
    onUpdateNote,
    onSortRange,
    onProtectRange,
    onUnProtectRange,
    onRequestDefineNamedRange,
    onRequestUpdateNamedRange,
    onCreateNamedRange,
    onUpdateNamedRange,
    onDeleteNamedRange,

    // For charts
    getSeriesValuesFromRange,
    getDomainValuesFromRange,

    // Create a history stack
    createHistory,

    // Enqueue any calculation manually
    enqueueCalculation,
    getNonEmptyColumnCount,
    getNonEmptyRowCount,

    calculateNow,
  } = useSpreadsheetState({
    sheets,
    sheetData,
    tables,
    functions,
    namedRanges,
    theme,
    colorMode,
    locale,
    onChangeSheets,
    onChangeSheetData,
    onChangeEmbeds,
    onChangeCharts,
    onChangeTables,
    onChangeNamedRanges,
    onChangeTheme,
  });

  const {
    onSearch,
    onResetSearch,
    onFocusNextResult,
    onFocusPreviousResult,
    hasNextResult,
    hasPreviousResult,
    borderStyles,
    isSearchActive,
    onRequestSearch,
    currentResult,
    totalResults,
    searchQuery,
  } = useSearch({
    getCellData,
    sheetId: activeSheetId,
    getNonEmptyColumnCount,
    getNonEmptyRowCount,
  });

  const currentCellFormat = useMemo(
    () => getUserEnteredFormat(activeSheetId, activeCell.rowIndex, activeCell.columnIndex),
    [activeSheetId, activeCell, getUserEnteredFormat],
  );

  const loadExcelFile = async (file: File) => {
    // NOTE: Added to handle the error thrown in `createRowDataFromExcelFile`.
    //  The error gets thrown from an event emitter, and so is not caught by try/catch
    //  or error boundaries.
    // The error is not rethrown, since it will be thrown in the console regardless.
    // The `once` ensures that the listener is only added once and doesn't create
    //  a memory leak
    window.addEventListener(
      'unhandledrejection',
      (_error) => {
        // NOTE: This event listener will get called for any unhandled promise rejections even if its not from rowsncolumns
        // Add checks here to only handle the error thrown from rowsncolumns
        if (_error.reason.name === 'AxiosError') return;
        setHasError(true);
      },
      { once: true },
    );
    const { sheets, sheetData, tables } = await createRowDataFromExcelFile(file);

    onChangeSheets(sheets);
    onChangeSheetData(sheetData);
    onChangeTables(tables);
    calculateNow();
  };

  const loadCSVFile = async (file: File) => {
    const rowData = await createRowDataFromCSVFile(file, undefined, undefined);

    // SheetData must be 1 indexed
    const newRowData: RowData<CellData>[] = [];
    rowData.forEach((row, i) => {
      const newRow: RowData<CellData> = { values: [] };
      if (row.values) {
        row.values.forEach((col, j) => newRow.values && (newRow.values[j + 1] = col));
      }
      newRowData[i + 1] = newRow;
    });

    onChangeSheets(initialSheets);
    onChangeSheetData({
      [initialSheets[0].sheetId]: newRowData,
    });
  };

  const resetSpreadsheet = () => {
    onChangeSheets(initialSheets);
    onChangeSheetData({});
    onChangeTables([]);
  };

  useEffect(() => {
    if (file) {
      const splitFileName = file.name.split('.');
      const fileType = splitFileName[splitFileName.length - 1];
      if (fileType === 'csv') {
        loadCSVFile(file);
      } else if (fileType.includes('xl')) {
        loadExcelFile(file);
      } else {
        throw new Error(t('components.Builder.Errors.fileTypeUnsupported'));
      }
    } else {
      // set blank spreadsheet when switching to brand new workflow
      resetSpreadsheet();
    }
  }, [file?.lastModified]);

  // return selected values in csv format
  // keeping this here in case we want to use it
  // const getSelectedValues = () => {
  //   if (activeCell && !selections.length) {
  //     return getEffectiveValue(activeSheetId, activeCell.rowIndex, activeCell.columnIndex);
  //   } else if (selections.length) {
  //     const ranges = [];
  //     for (const selection of selections) {
  //       const { startColumnIndex, endColumnIndex} = selection.range;
  //       const rangeValues = getDomainValuesFromRange(activeSheetId, selection.range);
  //       const rows = [];
  //       const numCols = endColumnIndex + 1 - startColumnIndex;
  //       for (let i = 0; i < rangeValues.length; i += numCols) {
  //           rows.push(rangeValues.slice(i, i + numCols).join(','));
  //       }
  //       ranges.push(rows.join('\n'));
  //     }
  //     return ranges.join('\n');
  //   }
  //   return '';
  // };

  // return selected range in Excel format, i.e. A1:C10
  const getSelectedRange = (_selections: SelectionArea<SelectionAttributes>[]) => {
    function numberToExcelColumn(num: number) {
      let column = '';
      while (num > 0) {
        const remainder = (num - 1) % 26;
        column = String.fromCharCode(65 + remainder) + column;
        num = Math.floor((num - 1) / 26);
      }
      return column;
    }
    if (_selections.length) {
      const ranges: string[] = [];
      for (const selection of _selections) {
        const { startRowIndex, startColumnIndex, endRowIndex, endColumnIndex } = selection.range;
        const startColumnLetter = numberToExcelColumn(startColumnIndex);
        const endColumnLetter = numberToExcelColumn(endColumnIndex);
        ranges.push(`${startColumnLetter}${startRowIndex}:${endColumnLetter}${endRowIndex}`);
      }
      return ranges.join();
    }
    return '';
  };

  return hasError ? (
    <div
      style={{
        display: 'grid',
        placeItems: 'center',
        height: '100%',
        width: '100%',
      }}
    >
      <div style={{}}>
        <Heading variant="h5">{t('components.Builder.Errors.unexpectedFileError')}</Heading>
      </div>
    </div>
  ) : (
    <div className="flex flex-1 flex-col h-full">
      <Toolbar>
        <ButtonUndo onClick={onUndo} disabled={!canUndo} />
        <ButtonRedo onClick={onRedo} disabled={!canRedo} />
        <ToolbarSeparator />
        <ScaleSelector value={scale} onChange={onChangeScale} />
        <ToolbarSeparator />

        <ButtonFormatCurrency
          onClick={() => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'numberFormat', {
              type: 'CURRENCY',
              pattern: pattern_currency_decimal,
            });
          }}
        />
        <ButtonFormatPercent
          onClick={() => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'numberFormat', {
              type: 'PERCENT',
              pattern: pattern_percent_decimal,
            });
          }}
        />
        <ButtonDecreaseDecimal
          onClick={() => onChangeDecimals(activeSheetId, activeCell, selections, 'decrement')}
        />
        <ButtonIncreaseDecimal
          onClick={() => onChangeDecimals(activeSheetId, activeCell, selections, 'increment')}
        />
        <TextFormatSelector
          locale={locale}
          currency={currency}
          onChangeFormatting={(type, value) =>
            onChangeFormatting(activeSheetId, activeCell, selections, type, value)
          }
        />
        <ToolbarSeparator />
        <FontFamilySelector
          value={currentCellFormat?.textFormat?.fontFamily}
          theme={theme}
          onChange={(value) => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'textFormat', {
              fontFamily: value,
            });
          }}
        />
        <ToolbarSeparator />
        <FontSizeSelector
          value={currentCellFormat?.textFormat?.fontSize ?? DEFAULT_FONT_SIZE_PT}
          onChange={(value) => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'textFormat', {
              fontSize: Number(value),
            });
          }}
        />
        <ToolbarSeparator />
        <ButtonBold
          isActive={currentCellFormat?.textFormat?.bold}
          onClick={() => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'textFormat', {
              bold: !currentCellFormat?.textFormat?.bold,
            });
          }}
        />
        <ButtonItalic
          isActive={currentCellFormat?.textFormat?.italic}
          onClick={() => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'textFormat', {
              italic: !currentCellFormat?.textFormat?.italic,
            });
          }}
        />
        <ButtonUnderline
          isActive={currentCellFormat?.textFormat?.underline}
          onClick={() => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'textFormat', {
              underline: !currentCellFormat?.textFormat?.underline,
            });
          }}
        />
        <ButtonStrikethrough
          isActive={currentCellFormat?.textFormat?.strikethrough}
          onClick={() => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'textFormat', {
              strikethrough: !currentCellFormat?.textFormat?.strikethrough,
            });
          }}
        />
        <TextColorSelector
          color={currentCellFormat?.textFormat?.color}
          theme={theme}
          onChange={(color) => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'textFormat', {
              color,
            });
          }}
        />
        <ToolbarSeparator />
        <BackgroundColorSelector
          color={currentCellFormat?.backgroundColor}
          theme={theme}
          onChange={(color) => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'backgroundColor', color);
          }}
        />

        <BorderSelector
          borders={currentCellFormat?.borders}
          onChange={(location, color, style) =>
            onChangeBorder(activeSheetId, activeCell, selections, location, color, style)
          }
          theme={theme}
        />
        <MergeCellsSelector
          activeCell={activeCell}
          selections={selections}
          sheetId={activeSheetId}
          merges={merges}
          onUnMerge={onUnMergeCells}
          onMerge={onMergeCells}
        />
        <ToolbarSeparator />
        <TextHorizontalAlignSelector
          value={currentCellFormat?.horizontalAlignment}
          onChange={(value) => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'horizontalAlignment', value);
          }}
        />
        <TextVerticalAlignSelector
          value={currentCellFormat?.verticalAlignment}
          onChange={(value) => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'verticalAlignment', value);
          }}
        />
        <TextWrapSelector
          value={currentCellFormat?.wrapStrategy}
          onChange={(value) => {
            onChangeFormatting(activeSheetId, activeCell, selections, 'wrapStrategy', value);
          }}
        />
        <ToolbarSeparator />

        <ButtonInsertImage onInsertFile={onInsertFile} />

        <TableStyleSelector
          theme={theme}
          tables={tables}
          activeCell={activeCell}
          selections={selections}
          sheetId={activeSheetId}
          onCreateTable={onCreateTable}
          onUpdateTable={onUpdateTable}
        />
        <ToolbarSeparator />
        <ThemeSelector theme={theme} onChangeTheme={onChangeSpreadsheetTheme} />
        <ButtonSwitchColorMode
          colorMode={colorMode}
          onClick={() => onChangeColorMode((prev) => (prev === 'dark' ? 'light' : 'dark'))}
        />

        <IconButton onClick={onRequestSearch}>
          <MagnifyingGlassIcon />
        </IconButton>
      </Toolbar>

      <FormulaBar>
        <RangeSelector
          selections={selections}
          activeCell={activeCell}
          onChangeActiveCell={onChangeActiveCell}
          onChangeSelections={onChangeSelections}
          sheets={sheets}
          rowCount={rowCount}
          columnCount={columnCount}
          onChangeActiveSheet={onChangeActiveSheet}
          onRequestDefineNamedRange={onRequestDefineNamedRange}
          onRequestUpdateNamedRange={onRequestUpdateNamedRange}
          onDeleteNamedRange={onDeleteNamedRange}
          namedRanges={namedRanges}
          tables={tables}
          sheetId={activeSheetId}
          merges={merges}
        />
        <Separator orientation="vertical" />
        <FormulaBarLabel>fx</FormulaBarLabel>
        <FormulaBarInput activeCell={activeCell} functionDescriptions={functionDescriptions} />
      </FormulaBar>
      <CanvasGrid
        {...spreadsheetColors}
        borderStyles={borderStyles}
        scale={scale}
        sheetId={activeSheetId}
        rowCount={rowCount}
        getDataRowCount={getDataRowCount}
        columnCount={columnCount}
        frozenColumnCount={frozenColumnCount}
        frozenRowCount={frozenRowCount}
        rowMetadata={rowMetadata}
        columnMetadata={columnMetadata}
        activeCell={activeCell}
        selections={selections}
        theme={theme}
        merges={merges}
        charts={charts}
        embeds={embeds}
        tables={tables}
        bandedRanges={bandedRanges}
        functionDescriptions={functionDescriptions}
        getSheetName={getSheetName}
        getCellData={getCellData}
        onChangeActiveCell={onChangeActiveCell}
        onChangeSelections={(sheetId, _selections, finishedSelection) => {
          if (finishedSelection) {
            setActiveSheet(getSheetName(sheetId));
            setSelectedRange && setSelectedRange(getSelectedRange(_selections));
          }
          onChangeSelections(sheetId, _selections);
        }}
        onChangeActiveSheet={onChangeActiveSheet}
        onRequestCalculate={onRequestCalculate}
        onSelectNextSheet={onSelectNextSheet}
        onSelectPreviousSheet={onSelectPreviousSheet}
        onChangeFormatting={onChangeFormatting}
        onRepeatFormatting={onRepeatFormatting}
        onHideColumn={onHideColumn}
        onShowColumn={onShowColumn}
        onHideRow={onHideRow}
        onShowRow={onShowRow}
        onDelete={onDelete}
        onClearContents={onDelete}
        onFill={onFill}
        onFillRange={onFillRange}
        onResize={onResize}
        onMoveChart={onMoveChart}
        onMoveEmbed={onMoveEmbed}
        onResizeChart={onResizeChart}
        onDeleteChart={onDeleteChart}
        onResizeEmbed={onResizeEmbed}
        onDeleteEmbed={onDeleteEmbed}
        onDeleteRow={onDeleteRow}
        onDeleteColumn={onDeleteColumn}
        onDeleteCellsShiftUp={onDeleteCellsShiftUp}
        onDeleteCellsShiftLeft={onDeleteCellsShiftLeft}
        onInsertCellsShiftRight={onInsertCellsShiftRight}
        onInsertCellsShiftDown={onInsertCellsShiftDown}
        onInsertRow={onInsertRow}
        onInsertColumn={onInsertColumn}
        onMoveColumns={onMoveColumns}
        onMoveRows={onMoveRows}
        onMoveSelection={onMoveSelection}
        onCreateNewSheet={onCreateNewSheet}
        onChange={onChange}
        onUndo={onUndo}
        onRedo={onRedo}
        onSortColumn={onSortColumn}
        onSortTable={onSortTable}
        onFilterTable={onFilterTable}
        onResizeTable={onResizeTable}
        onClearFormatting={onClearFormatting}
        onCopy={onCopy}
        onPaste={onPaste}
        onDragOver={onDragOver}
        onDrop={onDrop}
        onCreateTable={onCreateTable}
        onRequestEditTable={onRequestEditTable}
        onRequestDefineNamedRange={onRequestDefineNamedRange}
        onFreezeColumn={onFreezeColumn}
        onFreezeRow={onFreezeRow}
        onUpdateNote={onUpdateNote}
        onSortRange={onSortRange}
        onProtectRange={onProtectRange}
        onUnProtectRange={onUnProtectRange}
        namedRanges={namedRanges}
        licenseKey="examples-personal-4149-12be-c879-ca36-5714-7185-71ac-9ab5-909e-90f9-4108-45bb-d606-d17a-1b79-6c65"
        onRequestSearch={onRequestSearch}
      />

      <BottomBar>
        <NewSheetButton onClick={onCreateNewSheet} />

        <SheetSwitcher
          sheets={sheets}
          activeSheetId={activeSheetId}
          onChangeActiveSheet={onChangeActiveSheet}
          onShowSheet={onShowSheet}
        />

        <SheetTabs
          sheets={sheets}
          activeSheetId={activeSheetId}
          theme={theme}
          onChangeActiveSheet={onChangeActiveSheet}
          onRenameSheet={onRenameSheet}
          onChangeSheetTabColor={onChangeSheetTabColor}
          onDeleteSheet={onRequestDeleteSheet}
          onHideSheet={onHideSheet}
          onMoveSheet={onMoveSheet}
          onProtectSheet={onProtectSheet}
          onUnProtectSheet={onUnProtectSheet}
          onDuplicateSheet={onDuplicateSheet}
        />

        <SheetStatus
          sheetId={activeSheetId}
          activeCell={activeCell}
          selections={selections}
          onRequestCalculate={onRequestCalculate}
          rowCount={rowCount}
          columnCount={columnCount}
          merges={merges}
        />
      </BottomBar>

      <TableEditor
        sheetId={activeSheetId}
        rowCount={rowCount}
        columnCount={columnCount}
        getSheetName={getSheetName}
        onSubmit={onUpdateTable}
        theme={theme}
        merges={merges}
      />
      <DeleteSheetConfirmation sheetId={activeSheetId} onDeleteSheet={onDeleteSheet} />
      <NamedRangeEditor
        sheetId={activeSheetId}
        rowCount={rowCount}
        columnCount={columnCount}
        getSheetName={getSheetName}
        onCreateNamedRange={onCreateNamedRange}
        onUpdateNamedRange={onUpdateNamedRange}
        merges={merges}
      />
      <SheetSearch
        isActive={isSearchActive}
        onSubmit={onSearch}
        onReset={onResetSearch}
        onNext={onFocusNextResult}
        onPrevious={onFocusPreviousResult}
        disableNext={!hasNextResult}
        disablePrevious={!hasPreviousResult}
        currentResult={currentResult}
        totalResults={totalResults}
        searchQuery={searchQuery}
      />
    </div>
  );
};

interface SpreadsheetWithStateProviderProps {
  file: File | null;
  setSelectedRange?: Function;
  setActiveSheet: Function;
}

// Always wrap the component with SpreadsheetProvider
export const SpreadsheetWithStateProvider: React.FC<SpreadsheetWithStateProviderProps> = ({
  file,
  setSelectedRange,
  setActiveSheet,
}) => (
  <SpreadsheetProvider>
    <SpreadsheetWithState
      file={file}
      setSelectedRange={setSelectedRange}
      setActiveSheet={setActiveSheet}
    />
  </SpreadsheetProvider>
);
