import { AdpIcon } from './assets/icons/adp';
import { PlaceHolderIcon } from './assets/icons/placeholder';
import { BambooHr } from './assets/icons/bambooHr';
import { BillIcon } from './assets/icons/bill';
import { BrexIcon } from './assets/icons/brex';
import { CoupaIcon } from './assets/icons/coupa';
import { EmploymentHeroIcon } from './assets/icons/employmentHero';
import { ExpensifyIcon } from './assets/icons/expensify';
import { IntacctIcon } from './assets/icons/intacct';
import { MSDynamicsIcon } from './assets/icons/msdynamics';
import { NavanIcon } from './assets/icons/navan';
import { NetsuiteIcon } from './assets/icons/netsuite';
import { QuickbooksIcon } from './assets/icons/quickbooks';
import { RampIcon } from './assets/icons/ramp';
import { RecurlyIcon } from './assets/icons/recurly';
import { RipplingIcon } from './assets/icons/rippling';
import { SAPIcon } from './assets/icons/sap';
import { SAPConcurIcon } from './assets/icons/sapConcur';
import { TipaltiIcon } from './assets/icons/tipalti';
import { TrovataIcon } from './assets/icons/trovata';
import { UKGProIcon } from './assets/icons/ukgPro';
import { WorkdayIcon } from './assets/icons/workday';
import { XeroIcon } from './assets/icons/xero';
import { ZipIcon } from './assets/icons/zip';
import { ZohoBooksIcon } from './assets/icons/zohoBooks';
import { ZuoraIcon } from './assets/icons/zuora';

import { FQMatching } from './assets/icons/fq-matching';
import { FQReconciliationsIcon } from './assets/icons/fq-reconciliations';
import { FQTransformIcon } from './assets/icons/fq-transform';

const brandIcons = {
  adp: AdpIcon,
  bambooHr: BambooHr,
  bill: BillIcon,
  brex: BrexIcon,
  coupa: CoupaIcon,
  employmentHero: EmploymentHeroIcon,
  expensify: ExpensifyIcon,
  intacct: IntacctIcon,
  msdynamics: MSDynamicsIcon,
  navan: NavanIcon,
  netsuite: NetsuiteIcon,
  quickbooks: QuickbooksIcon,
  ramp: RampIcon,
  recurly: RecurlyIcon,
  rippling: RipplingIcon,
  sap: SAPIcon,
  sapConcur: SAPConcurIcon,
  tipalti: TipaltiIcon,
  trovata: TrovataIcon,
  ukgPro: UKGProIcon,
  workday: WorkdayIcon,
  xero: XeroIcon,
  zip: ZipIcon,
  zohoBooks: ZohoBooksIcon,
  zuora: ZuoraIcon,
};

const BRAND_MAPPINGS = {
  adp: 'adp',
  'adp-workforce-now': 'adp',
  bambooHr: 'bambooHr',
  billCom: 'bill',
  brex: 'brex',
  business_central: 'msdynamics',
  coupa: 'coupa',
  employmentHero: 'employmentHero',
  expensify: 'expensify',
  intacct: 'intacct',
  intacctEnhanced: 'intacct',
  netsuite: 'netsuite',
  netsuiteEnhanced: 'netsuite',
  qboEnhanced: 'quickbooks',
  quickbooks: 'quickbooks',
  ramp: 'ramp',
  recurly: 'recurly',
  rippling: 'rippling',
  sapConcur: 'sapConcur',
  sap_primary: 'sap',
  sap_secondary: 'sap',
  tavan: 'navan',
  tipalti: 'tipalti',
  trovata: 'trovata',
  'trovata-v2': 'trovata',
  ukgPro: 'ukgPro',
  workdayEnhanced: 'workday',
  xero: 'xero',
  zip: 'zip',
  zohoBooks: 'zohoBooks',
  zuora: 'zuora',
};

const PRODUCT_MAPPINGS = {
  'fq-matching': 'matching',
  'fq-reconciliations': 'reconciliations',
  'fq-transform': 'transform',
};

const productIcons = {
  transform: FQTransformIcon,
  reconciliations: FQReconciliationsIcon,
  matching: FQMatching,
};

export function getProductIcon(product = '') {
  const key = PRODUCT_MAPPINGS[product];
  return productIcons[key] || PlaceHolderIcon;
}

export function getBrandIcon(service = '') {
  const key = BRAND_MAPPINGS[service];
  return brandIcons[key] || PlaceHolderIcon;
}
