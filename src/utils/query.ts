import { QueryFunction, QueryKey } from '@tanstack/react-query';

type ParamsType<T> = T extends (params: infer P) => any ? P : never;

/**
 * NOTE: The params in the return object are optional to prevent weird query invalidation bugs.
 *
 * However, the type signature will make it look like the params may be undefined
 * even when they should be defined. I couldn't find an easy way to make it work.
 */
type QueryKeyReturn<TResource extends string, T> = {
  [K in keyof T]: (params: Partial<ParamsType<T[K]>>) => [
    {
      resource: TResource;
      action: K;
      params?: Partial<ParamsType<T[K]>>;
    },
  ];
};

/**
 * Helper to create query keys for a given fetching function.
 *
 * This structure of query key allows for a few different invalidation methods:
 * - by resource (i.e. all workflow queries)
 * - by action (i.e. by specific API call)
 * - by params (i.e. all queries for any resource that uses workflowId='123')
 * - by any combination of these (i.e. workflow queries where workflowId='123')
 *
 * @example
 * ```
 * const workflowQueryKeys = createQueryKeys('workflows', v2.workflows)
 * // Produces:
 * {
 *   getWorkflow: ({ workflowId: string }) => [{
 *     resource: 'workflow',
 *     action: 'getWorkflow',
 *     params: { workflowId }
 *   }],
 *   createWorkflow: ({ workflow: { name: string, description?: string }}) => [{
 *     resource: 'workflow',
 *     action: 'createWorkflow',
 *     params: { workflow },
 *   }]
 * }
 * ```
 */
export const createQueryKeys = <
  TResource extends string,
  T extends Record<string, any>,
>(
  resource: TResource,
  sdk: T,
): QueryKeyReturn<TResource, T> & { resource: TResource } => {
  const sdkMethods = getAllClassMethods(sdk);
  return sdkMethods.reduce(
    (acc, action) => {
      acc[action as keyof T] = (
        params: Partial<ParamsType<T[typeof action]>>,
      ) => [
        {
          resource,
          action,
          // If params are undefined, don't include them. Requiring
          //  an undefined value will disrupt the partial object matching
          //  of queryKey invalidation
          // i.e. [{ resource: 'a', action: 'b', params: undefined }]
          // wont match:
          //      [{ resource: 'a', action: 'b', params: { someOption: 'c' }}]
          // when logically we want it to be able to.
          ...(params ? { params } : {}),
        },
      ];
      return acc;
    },
    { resource } as QueryKeyReturn<TResource, T> & { resource: TResource },
  );
};

/**
 * Gets all methods on a class, inherited, enumerable, or otherwise.
 *
 * Ref:
 * https://stackoverflow.com/questions/31054910/get-functions-methods-of-a-class
 */
const getAllClassMethods = (cls: any): any[] => {
  const methods = new Set();
  let obj = cls;
  while ((obj = Reflect.getPrototypeOf(obj))) {
    const keys = Reflect.ownKeys(obj);
    keys.forEach((k) => methods.add(k));
  }

  return Array.from(methods);
};

/** Helper type to extract the params from a query key function */
type ExtractParams<T> = T extends (params: infer P) => any ? P : never;

/** Helper type to represent the structure of our query keys */
type QueryKeyType<T> = T extends (params: any) => [infer R] ? R : never;

/**
 * Helper function to create typed query functions easily
 * from a query key. Supplying a query key will provide
 * the correct parameters to the fetchFn.
 *
 * NOTE: Technically, you can supply a mismatched query function
 * i.e.
 * ```ts
 * // Both expect only a `workflowId` param
 * //  so this will type-check correctly.
 * const query = createQueryFunction(
 *   workflowKeys.getWorkflow,
 *   v3.workflowInputs.getWorkflowInputs,
 * );
 * ```
 * This will have the consequence of having both of these
 * invalidate-able via the query key `{ resource: 'workflows' }`,
 * which may cause unintended behavior.
 */
export const createQueryFunction = <
  TQueryKey extends (
    params: any,
  ) => [{ resource: string; action: string; params?: any }],
  TData,
>(
  _queryKeyFn: TQueryKey,
  fetchFn: (params: ExtractParams<TQueryKey>) => Promise<TData>,
): QueryFunction<TData, QueryKey> => {
  return async ({ queryKey }) => {
    if (queryKey.length === 0) {
      console.debug(queryKey);
      throw new Error('Query key is empty');
    }
    // Bit of a hack; we have the fetchFn match the params of the
    // query key, but we can't (easily) force the query key to
    // type-check correctly. We know this will match the
    // fetchFn params and that TQueryKey will guarantee the
    // existence of `.params`
    const [{ params }] = queryKey as [QueryKeyType<TQueryKey>];
    return fetchFn(params);
  };
};

export const createInfiniteQueryFunction = <
  TQueryKey extends (
    params: any,
  ) => [{ resource: string; action: string; params?: any }],
  TData,
>(
  _queryKeyFn: TQueryKey,
  fetchFn: (
    params: ExtractParams<TQueryKey> & { pageParam?: any },
  ) => Promise<TData>,
): QueryFunction<TData, QueryKey, any> => {
  return async ({ queryKey, pageParam }) => {
    if (queryKey.length === 0) {
      console.debug(queryKey);
      throw new Error('Query key is empty');
    }

    // Bit of a hack; we have the fetchFn match the params of the
    // query key, but we can't (easily) force the query key to
    // type-check correctly. We know this will match the
    // fetchFn params and that TQueryKey will guarantee the
    // existence of `.params`
    const [{ params }] = queryKey as [QueryKeyType<TQueryKey>];
    return fetchFn({ ...params, pageParam });
  };
};
