import { I<PERSON><PERSON><PERSON><PERSON>, <PERSON>ing, DropdownPanel, Tooltip } from '@floqastinc/flow-ui_core';
import AddIcon from '@floqastinc/flow-ui_icons/material/Add';
import { useParams } from 'react-router-dom';
import { useCallback, useState } from 'react';
import { DataType, WorkflowInput } from '@floqastinc/transform-v3';
import * as styledBuilder from '../../index.styles';
import {
  InputForm,
  InputFormData,
} from '../BuilderSectionTabs/InputsTabSection/components/InputForm';
import { InputIcon } from '../BuilderSectionTabs/InputsTabSection/components/InputForm/InputIcon';
import * as styled from './index.styled';
import { t } from '@/utils/i18n';
import { useWorkflowInputExampleUri, useWorkflowInputs } from '@v3/workflow-inputs';
import { WorkflowInputData } from '@/api/bff/taskInputs';
import { InputsList } from '@Transform/components/InputsList';
import { FilePreview } from '@Transform/pages/runner/ui/FilePreview';
import { useFile } from '@/hooks/useFile';
import { useCreateWorkflowInputAndSetValue } from '@/hooks/useCreateWorkflowInputAndSetValue';
import { useUpdateWorkflowInputAndSetValue } from '@/hooks/useUpdateWorkflowInputAndSetValue';

const DEFAULT_INPUT = {
  name: '',
  description: '',
  type: undefined,
} as const;

export default function GlobalInputs() {
  const { workflowId = '' } = useParams();
  const [showNewInputForm, setShowNewInputForm] = useState(false);
  const [showInputTypeDropdown, setShowInputTypeDropdown] = useState(false);
  const [hasUnsavedInputChanges, setHasUnsavedInputChanges] = useState(false);
  const [newInput, setNewInput] = useState<Partial<WorkflowInputData>>(DEFAULT_INPUT);
  const [selectedFileInputId, setSelectedFileInputId] = useState<string | null>(null);
  const { data: inputs } = useWorkflowInputs({ workflowId });
  const { data: selectedFileUri } = useWorkflowInputExampleUri(
    { workflowId, workflowInputId: selectedFileInputId as string },
    {
      enabled: typeof selectedFileInputId === 'string',
    },
  );
  const { file, isFileLoading } = useFile(selectedFileUri?.url);
  const createInputMutation = useCreateWorkflowInputAndSetValue();
  const updateInputMutation = useUpdateWorkflowInputAndSetValue();

  // Helper function to check if input has meaningful changes
  const hasInputChanges = useCallback((input: Partial<WorkflowInputData>) => {
    return !!(
      (input.name && input.name.trim()) ||
      (input.description && input.description.trim()) ||
      input.value
    );
  }, []);

  const handleSaveNewInput = useCallback(
    (input: InputFormData) => {
      if (input.value === undefined || input.value === null) {
        console.error('Input value is required for saving');
        return;
      }

      setShowNewInputForm(false);
      setHasUnsavedInputChanges(false);
      createInputMutation.mutate(
        {
          workflowId,
          input: {
            ...input,
            value: input.value,
          },
        },
        {
          onSuccess: (workflowInput) => {
            setSelectedFileInputId(workflowInput.id);
          },
        },
      );
      setNewInput(DEFAULT_INPUT);
    },
    [createInputMutation, workflowId],
  );

  const handleChangeNewInput = useCallback(
    (input: Partial<WorkflowInputData>) => {
      setNewInput((prev) => {
        const updatedInput = {
          ...prev,
          ...input,
        };
        setHasUnsavedInputChanges(hasInputChanges(updatedInput));
        return updatedInput;
      });
    },
    [hasInputChanges],
  );

  const handleCancelNewInput = useCallback(() => {
    setShowNewInputForm(false);
    setHasUnsavedInputChanges(false);
    setNewInput(DEFAULT_INPUT);
  }, []);

  const handleExistingInputSave = (input: WorkflowInput) => {
    if (input.value === undefined) {
      console.error('Input value is required for saving');
      return;
    }

    updateInputMutation.mutate({
      workflowId,
      workflowInputId: input.id,
      input: { ...input, value: input.value },
    });
  };

  const handleOpenFile = useCallback(
    (fileId: string) => {
      setSelectedFileInputId(fileId);
    },
    [setSelectedFileInputId],
  );

  const renderIconButton = () => {
    return (
      <IconButton
        aria-label={t('components.Builder.GlobalInputs.addInput')}
        disabled={hasUnsavedInputChanges}
      >
        <AddIcon
          onClick={() => {
            if (!hasUnsavedInputChanges) {
              setShowInputTypeDropdown(true);
            }
          }}
        />
      </IconButton>
    );
  };

  return (
    <>
      <styledBuilder.BuilderSection>
        <styledBuilder.BuilderSectionContainer>
          <styled.GlobalInputsHeader>
            <Heading variant="h5" weight="regular">
              {t('components.Builder.GlobalInputs.dataInput')}
            </Heading>
            <DropdownPanel
              aria-label={t('components.Builder.GlobalInputs.addInput')}
              disableClear={true}
              disableFilter={true}
              selectionMode="single"
              selectedValues={newInput.type ? [newInput.type] : []}
              open={showInputTypeDropdown}
              onChange={(inputType: DataType) => {
                setShowInputTypeDropdown(false);
                setNewInput({
                  type: inputType,
                });
                setShowNewInputForm(true);
              }}
              onOpenChange={setShowInputTypeDropdown}
            >
              <DropdownPanel.Trigger>
                {hasUnsavedInputChanges ? (
                  <Tooltip placement="bottom">
                    <Tooltip.Trigger>{renderIconButton()}</Tooltip.Trigger>
                    <Tooltip.Content>
                      {hasUnsavedInputChanges && t('components.Builder.GlobalInputs.cancelOrSave')}
                    </Tooltip.Content>
                  </Tooltip>
                ) : (
                  renderIconButton()
                )}
              </DropdownPanel.Trigger>
              <DropdownPanel.Content align="end" style={{ zIndex: 2 }}>
                <DropdownPanel.Option value="FILE">
                  <InputIcon variant="FILE" />
                  {t('components.Builder.file')}
                </DropdownPanel.Option>
                <DropdownPanel.Option value="DATETIME">
                  <InputIcon variant="DATETIME" />
                  {t('components.Builder.date')}
                </DropdownPanel.Option>
                <DropdownPanel.Option value="TEXT">
                  <InputIcon variant="TEXT" />
                  {t('components.Builder.text')}
                </DropdownPanel.Option>
              </DropdownPanel.Content>
            </DropdownPanel>
          </styled.GlobalInputsHeader>
          <styled.GlobalInputsSectionContent>
            {showNewInputForm ? (
              <styled.InputFormWrapper>
                <InputForm
                  title={t('components.Builder.GlobalInputs.newInput')}
                  input={newInput}
                  mode="create"
                  onSave={handleSaveNewInput}
                  onChange={handleChangeNewInput}
                  onCancel={handleCancelNewInput}
                />
                {showInputTypeDropdown && <styled.InputFormOverlay />}
              </styled.InputFormWrapper>
            ) : null}
            <InputsList
              inputs={inputs ?? []}
              onExistingInputSave={handleExistingInputSave}
              onOpenFile={handleOpenFile}
              onEditFile={(fileId) => setSelectedFileInputId(fileId)}
            />
          </styled.GlobalInputsSectionContent>
        </styledBuilder.BuilderSectionContainer>
      </styledBuilder.BuilderSection>
      <styledBuilder.Output>
        <FilePreview
          previewName={
            selectedFileInputId
              ? (inputs?.find((i) => i.id === selectedFileInputId)?.name ?? '')
              : ''
          }
          file={file}
          isLoading={isFileLoading}
          mode="builder"
          example={undefined}
          activeFileId={selectedFileInputId ?? ''}
          fileTabs={[]}
        />
      </styledBuilder.Output>
    </>
  );
}
