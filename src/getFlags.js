const getFlags = () =>
    window.location.hash
        .slice(1)
        .split('&')
        .reduce((acc, flagPair) => {
            const [name, value] = flagPair.split('=');
            if (name) {
                if (value === 'true') {
                    acc[name] = true;
                } else if (value === 'false') {
                    acc[name] = false;
                } else {
                    // name but no value => set value to true
                    acc[name] = value || true;
                }
            }
            return acc;
        }, {});

export default getFlags;
