import { useParams } from "react-router-dom";
import { match } from "ts-pattern";
import CheckCircleIcon from "@floqastinc/flow-ui_icons/material/CheckCircle";
import AutoRenewIcon from "@floqastinc/flow-ui_icons/material/Autorenew";
import StepIcon from "../StepIcon";
import { StepFailedWarningToast } from "../StepFailedWarningToast";
import * as Styled from "./index.styles";
import { useTask } from "@v3/tasks";
import WarningIcon from "@/components/WarningIcon";

type StepProps = {
  taskId: string;
  stepNum: number;
};

export const Step = ({ taskId, stepNum }: StepProps) => {
  const { workflowId = "" } = useParams();

  // TODO: add optimistic update for script status using
  //  the updateExample mutation.
  const taskQuery = useTask(
    { workflowId: workflowId, taskId: taskId },
    {
      refetchInterval: (query) => {
        if (query.state.data?.data?.strategy?.kind === "SCRIPT") {
          if (query.state.data?.data?.strategy?.buildStatus === "BUSY") {
            return 2000;
          }
        }

        return undefined;
      },
      enabled: !!workflowId && !!taskId,
    },
  );

  const buildStatus =
    taskQuery.data?.strategy?.kind === "SCRIPT" ? taskQuery.data?.strategy?.buildStatus : undefined;

  return (
    <>
      {buildStatus === "FAILED" ? (
        <WarningIcon className="failed-icon" />
      ) : (
        <StepIcon taskKind={taskQuery.data?.strategy?.kind} stepNum={stepNum} />
      )}
      <Styled.StatusIndicator>
        {match(buildStatus)
          .with("BUSY", () => (
            <div style={{ top: "-4px", position: "absolute" }}>
              <Styled.RotatingIcon>
                <AutoRenewIcon className="busy-icon" size={12} color="var(--flo-sem-color-info)" />
              </Styled.RotatingIcon>
            </div>
          ))
          .with("COMPILED", () => (
            <CheckCircleIcon
              className="compiled-icon"
              size={12}
              color="var(--flo-sem-color-success)"
            />
          ))
          .with("NONE", undefined, () => null)
          .otherwise(() => null)}
      </Styled.StatusIndicator>
      <StepFailedWarningToast task={taskQuery.data} stepNum={stepNum} />
    </>
  );
};
