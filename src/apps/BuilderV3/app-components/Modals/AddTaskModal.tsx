import { useState } from 'react';
import { string, func } from 'prop-types';
import { t } from '@/utils/i18n';
import { Button, DropdownButton, DropdownPanel, Heading } from '@floqastinc/flow-ui_core';
import { styled } from 'styled-components';
import { Strategy, WorkflowTaskStrategy } from '@floqastinc/transform-v3';
import { match } from 'ts-pattern';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Overlay, ModalWrapper, ModalHeader, Form, TextInput } from './styled';
import v3, { ApiError } from '@/services/v3';
import { tempAssistantId } from '@/utils/assistant';
import { useModal } from '@/components';

const ButtonRow = styled.div`
  display: flex;
  flex-direction: row;
`;
const DummyDiv = styled.div`
  flex-grow: 1;
`;

interface AddTaskModalProps {
  action: 'ADD' | 'EDIT';
  onSave: (task: { name: string; strategy?: Strategy }) => void;
  shouldNavigateOnCancel?: boolean;
}

const strategyMap: { [key: string]: Strategy } = {
  JEM_EXPORT: { kind: 'JEM_EXPORT' },
  LLM_THREAD: {
    kind: 'LLM_THREAD',
    messages: [],
    status: 'READY',
    assistantId: tempAssistantId(),
  },
  FLOLAKE: {
    kind: 'FLOLAKE',
    statement: '',
    sources: [],
  },
  JEM_TEMPLATE_FETCH: { kind: 'JEM_TEMPLATE_FETCH' },
  REVIEW: { kind: 'REVIEW' },
};

export const AddTaskModal = ({
  action = 'ADD',
  onSave,
  shouldNavigateOnCancel = false,
}: AddTaskModalProps) => {
  const navigate = useNavigate();
  const defaultStrategy = undefined;

  const [name, setName] = useState('');
  const [strategy, setStrategy] = useState<Strategy | undefined>(undefined);
  const [dropdownIsOpen, setDropdownIsOpen] = useState(false);
  const { closeModal } = useModal();

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (action === 'EDIT') {
      // TODO: Handle strategy changes
      onSave({ name });
    } else {
      onSave({ name, strategy });
    }
    setName('');
    setStrategy(defaultStrategy);
    closeModal();
  };

  const handleStrategyChange = (value: string) => {
    const newStrategy = strategyMap[value];

    if (!newStrategy) {
      throw new Error(`${value}: ${t('components.Builder.Errors.unsupportedStrat')}`);
    }

    setStrategy(newStrategy);
    setDropdownIsOpen(false);
  };

  const {
    isLoading,
    isError,
    error,
    data: strategyOptions = [],
  } = useQuery<WorkflowTaskStrategy[], Error>({
    queryKey: ['strategies', 'list'],
    queryFn: async () => {
      const response = await v3.strategies.getWorkflowTaskStrategies({});
      if (response.errors.length) {
        throw new ApiError(response.errors);
      }
      if (!response.data) {
        throw new Error(t('components.Builder.Errors.noData'));
      }
      return response.data;
    },
    retry: false,
  });

  const getStrategyDropdown = () => {
    if (action === 'ADD') {
      return (
        <DropdownPanel
          onChange={handleStrategyChange}
          onOpenChange={setDropdownIsOpen}
          isOpen={dropdownIsOpen}
          selectedValue={strategy}
          disableClear={true}
        >
          <DropdownPanel.Trigger>
            <DropdownButton
              isRequired
              label={t('generics.strategy')}
              variant="default"
              open={dropdownIsOpen}
            >
              {match({ isLoading, isError, data: strategyOptions })
                .with({ isLoading: true }, () => <>{t('components.Builder.loading')}</>)
                .with({ data: [] }, () => <>{t('components.Builder.noDataAvailable')}</>)
                .with({ isError: true }, () => (
                  <>
                    {t('components.Builder.Errors.errorFetchingStrat')} {error?.message}
                  </>
                ))
                .otherwise(() =>
                  strategy
                    ? strategyOptions.find((option) => option.kind === strategy.kind)?.name
                    : t('components.Builder.selectStrat'),
                )}
            </DropdownButton>
          </DropdownPanel.Trigger>

          <DropdownPanel.Content portal={false} style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {match({ isLoading, isError, data: strategyOptions })
              .with({ isLoading: true }, () => (
                <DropdownPanel.Option value="loading" disabled>
                  {t('components.Builder.loading')}
                </DropdownPanel.Option>
              ))
              .with({ isError: true }, () => (
                <DropdownPanel.Option value="error" disabled>
                  {t('components.Builder.Errors.errorFetchingStrat')} {error?.message}
                </DropdownPanel.Option>
              ))
              .otherwise(() =>
                strategyOptions.length > 0 ? (
                  strategyOptions.map((strategy) => (
                    <DropdownPanel.Option key={strategy.kind} value={strategy.kind}>
                      {strategy.name}
                    </DropdownPanel.Option>
                  ))
                ) : (
                  <DropdownPanel.Option value="no-data" disabled>
                    {t('components.Builder.Errors.noStratAvailable')}
                  </DropdownPanel.Option>
                ),
              )}
          </DropdownPanel.Content>
        </DropdownPanel>
      );
    }
    return null;
  };

  return (
    <Overlay onClick={closeModal}>
      <ModalWrapper onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <Heading variant="h2">
            {action === 'EDIT' ? t('components.Builder.editStep') : t('components.Builder.addStep')}
          </Heading>
        </ModalHeader>
        <Form onSubmit={handleSubmit}>
          <TextInput
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder={t('generics.name')}
            required
          />
          {getStrategyDropdown()}
          <ButtonRow>
            <DummyDiv />
            <Button
              type="button"
              color="dark"
              data-tracking-id="builder-strategy-selection-modal-cancel"
              variant="ghost"
              onClick={() => {
                closeModal();
                if (shouldNavigateOnCancel) navigate(-1);
              }}
            >
              {t('generics.cancel')}
            </Button>
            <Button
              type="submit"
              disabled={!name || (action === 'ADD' && !strategy)}
              data-tracking-id="builder-strategy-selection-modal-saved"
            >
              {t('generics.save')}
            </Button>
          </ButtonRow>
        </Form>
      </ModalWrapper>
    </Overlay>
  );
};

AddTaskModal.propTypes = {
  action: string,
  onSave: func.isRequired,
};
