import { ExampleSet, NewWorkflowInput, Task, Workflow } from '@floqastinc/transform-v3';
import { useMutation, useQuery } from '@tanstack/react-query';
import { t } from '@/utils/i18n';
import axios from 'axios';
import { useAtom } from 'jotai';
import { useNavigate, useParams } from 'react-router-dom';
import { shouldRefetchTasksAtom } from './BuilderPage.store';
import v3, { ApiError } from '@/services/v3';
import { getExampleInputsQuery } from '@BuilderV3/api/example-inputs';
import { getExampleOutputsQuery } from '@BuilderV3/api/example-outputs';
import { createExample, getExamplesQuery } from '@BuilderV3/api/examples';
import { queryKeys } from '@BuilderV3/api/query-keys';
import { getTaskInputsQuery } from '@BuilderV3/api/task-inputs';
import { createTask, getWorkflowTasksQuery, updateTask } from '@BuilderV3/api/tasks';
import { useModal } from '@/components/ModalContext';
import { queryClient } from '@/components/queryClient';
import { useFeatureFlags } from '@/components/FeatureFlag';
import { AGENTS, BUILDER, EXAMPLES, STEPS, V3 } from '@/constants';
import { getIsChatBasedStrategy } from '@/utils/strategy';

export const useTaskQueries = () => {
  const { getFlag } = useFeatureFlags();
  const { workflowId, taskId } = useParams();
  const navigate = useNavigate();
  const { openModal } = useModal();

  if (!workflowId) {
    console.error(t('components.Builder.Errors.noWorkflowID'));
    throw new Error(t('components.Builder.Errors.noWorkflowID'));
  }

  const tasksQuery = useQuery(getWorkflowTasksQuery(workflowId));

  const tasks = tasksQuery.data ?? [];
  const currentTask = taskId ? tasks.find((task) => task.id === taskId) : (tasks[0] ?? {});

  const handleTaskNameEdit = () => {
    openModal('AddTask', {
      action: 'EDIT',
      onSave: (update: Task) => updateTaskMutation.mutate(update),
    });
  };

  const createTaskMutation = useMutation({
    mutationKey: ['createTask'],
    mutationFn: async (taskData: Task) => {
      const { task } = await createTask({
        workflowId,
        task: taskData,
        previousTaskId: taskId,
      });

      const example = await createExample({
        workflowId,
        taskId: task.id,
        example: {
          status: 'DRAFT',
        },
      });
      if (taskData.strategy.kind === 'JEM_TEMPLATE_FETCH') {
        try {
          const jemTemplate = await v3.jemTemplate.getJemTemplate({
            workflowId,
            taskId: task.id,
            exampleSetId: example.id,
          });
          if (jemTemplate.errors?.length) {
            throw new ApiError(jemTemplate.errors);
          }
        } catch (error) {
          console.error(t('components.Builder.Errors.fetchJEMTemplate'), error);
        }
      }
      // if transform-file-sample is undefined or true, create a file sample
      if (getFlag('transform-file-sample', false) !== false) {
        try {
          const exampleInputs = await v3.exampleInputs.getExampleInputs({
            workflowId,
            taskId: task.id,
            exampleSetId: example.id,
          });
          if (exampleInputs.errors?.length) {
            throw new ApiError(exampleInputs.errors);
          }
          await Promise.all(
            exampleInputs.data
              .filter((input) => input.value?.kind === 'FILE')
              .map((exampleInput) => {
                return v3.fileSamples.createFileSample({
                  workflowId,
                  taskId: task.id,
                  exampleSetId: example.id,
                  exampleInputId: exampleInput.id,
                });
              }),
          );
        } catch (e) {
          console.error(e);
        }
      }

      return { task, example };
    },
    onSuccess: ({ task, example }) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.byId(workflowId),
      });
      queryClient.invalidateQueries({
        queryKey: [
          {
            resource: queryKeys.examples.resource,
            params: { workflowId, taskId: task.id },
          },
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          {
            resource: queryKeys.exampleInputs.resource,
            params: { workflowId, taskId: task.id },
          },
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          {
            resource: queryKeys.exampleOutputs.resource,
            params: { workflowId, taskId: task.id },
          },
        ],
      });
      navigate(
        `/${BUILDER}/${V3}/${AGENTS}/${workflowId}/${STEPS}/${task.id}/${EXAMPLES}/${example.id}`,
      );
    },
    onError: (error) => {
      console.error('error', error);
    },
  });

  const updateTaskMutation = useMutation({
    mutationFn: (task: Task) => updateTask({ workflowId, taskId, task }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflows.byId(workflowId),
      });
    },
    onError: (error) => {
      console.error('error', error);
    },
  });

  const taskInputsQuery = useQuery(getTaskInputsQuery({ workflowId, taskId }));

  return {
    tasksQuery,
    currentTask,
    createTaskMutation,
    updateTaskMutation,
    handleTaskNameEdit,
    taskInputsQuery,
  };
};

export const useExampleQueries = () => {
  const { workflowId, taskId, exampleSetId } = useParams();

  if (!workflowId || !taskId || !exampleSetId) {
    console.error(t('components.Builder.Errors.noIDsprovided'));
    throw new Error(t('components.Builder.Errors.noIDsprovided'));
  }

  const examplesQuery = useQuery(getExamplesQuery({ workflowId, taskId }));
  const exampleInputsQuery = useQuery(getExampleInputsQuery({ workflowId, taskId, exampleSetId }));
  const exampleOutputsQuery = useQuery(
    getExampleOutputsQuery({ workflowId, taskId, exampleSetId }),
  );

  const [_, setRefetchTasksOverride] = useAtom(shouldRefetchTasksAtom);
  const updateExampleMutation = useMutation({
    mutationFn: (example: Partial<ExampleSet>) => {
      return v3.examples.updateExample({
        workflowId,
        taskId,
        exampleSetId,
        example,
      });
    },
    onSuccess: (exampleResponse) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.examples.getExample({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
      if (
        getIsChatBasedStrategy(exampleResponse.data?.strategy) &&
        exampleResponse.data.status === 'ACTIVE'
      ) {
        setRefetchTasksOverride(true);
        queryClient.invalidateQueries({
          queryKey: queryKeys.tasks.byWorkflow(workflowId),
        });
      }
    },
  });

  return {
    examplesQuery,
    exampleInputsQuery,
    exampleOutputsQuery,
    updateExampleMutation,
  };
};

type WorkflowInputData = NewWorkflowInput & {
  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  value: any;
};
export const useInputQueries = () => {
  const { getFlag } = useFeatureFlags();
  const { workflowId, taskId, exampleSetId } = useParams();
  if (!workflowId || !taskId || !exampleSetId) {
    console.error(t('components.Builder.Errors.noIDsprovided'));
    throw new Error(t('components.Builder.Errors.noIDsprovided'));
  }
  const addInputMutation = useMutation({
    mutationKey: [
      'addInput',
      {
        workflowId,
        taskId,
        exampleSetId,
      },
    ],
    mutationFn: async (input: WorkflowInputData) => {
      const workflowInputRes = await v3.workflowInputs.createWorkflowInput({
        workflowId,
        input: {
          name: input.name,
          type: input.type,
        },
      });
      if (workflowInputRes.errors?.length) {
        throw new ApiError(workflowInputRes.errors);
      }
      if (!workflowInputRes.data) {
        throw new Error(t('components.Builder.Errors.noDataReturnedCWI'));
      }

      const { data: workflowInput } = workflowInputRes;

      const taskInputRes = await v3.taskInputs.createTaskInput({
        workflowId,
        taskId,
        input: {
          name: input.name,
          type: input.type,
          description: input.description,
          source: {
            workflowInputId: workflowInput.id,
          },
        },
      });
      if (taskInputRes.errors?.length) {
        throw new ApiError(taskInputRes.errors);
      }
      if (!taskInputRes.data) {
        throw new Error(t('components.Builder.Errors.noDataReturnedCTI'));
      }

      const exampleInputResponse = await v3.exampleInputs.createExampleInput({
        workflowId,
        taskId,
        exampleSetId,
        input: {
          taskInputId: taskInputRes.data?.id,
        },
      });
      if (exampleInputResponse.errors?.length) {
        throw new ApiError(exampleInputResponse.errors);
      }
      if (!exampleInputResponse.data) {
        throw new Error(t('components.Builder.Errors.noDataReturnedCEI'));
      }
      const exampleInput = exampleInputResponse.data;

      if (input.type === 'DATETIME') {
        const setDateInputRes = await v3.exampleInputs.setExampleInputDatetime({
          workflowId,
          value: {
            kind: 'DATETIME',
            value: new Date(input.value),
          },
          taskId,
          exampleSetId,
          exampleInputId: exampleInput.id,
        });
        if (setDateInputRes.errors?.length) {
          throw new ApiError(setDateInputRes.errors);
        }

        return setDateInputRes;
      }

      if (input.type === 'TEXT') {
        const setTextInputRes = await v3.exampleInputs.setExampleInputText({
          workflowId,
          taskId,
          exampleSetId,
          exampleInputId: exampleInput.id,
          value: {
            kind: 'TEXT',
            value: input.value,
          },
        });
        if (setTextInputRes.errors?.length) {
          throw new ApiError(setTextInputRes.errors);
        }

        return setTextInputRes;
      }

      if (input.type === 'FILE') {
        const inputResponse = await v3.exampleInputs.setExampleInputFileValue({
          workflowId,
          taskId,
          exampleSetId,
          exampleInputId: exampleInputResponse.data.id,
          value: {
            kind: 'FILE',
            mimetype: input.value.type,
            name: input.value.name,
          },
        });
        if (inputResponse.errors.length > 0) {
          return inputResponse.errors;
        }
        if (!inputResponse.data) {
          throw new Error(t('components.Builder.Errors.failedSetExampleInput'));
        }

        const { url } = inputResponse.data;
        const s3Res = await axios.put(url, input.value, {
          headers: {
            'Content-Type': input.value.type,
          },
        });
        if (s3Res.status !== 200) {
          const { errors: deleteWorkflowInputErrors } = await v3.workflowInputs.deleteWorkflowInput(
            {
              workflowId,
              workflowInputId: workflowInput.id,
            },
          );
          if (deleteWorkflowInputErrors.length) {
            throw new ApiError(deleteWorkflowInputErrors);
          }
          const { errors: deleteTaskInputErrors } = await v3.taskInputs.deleteTaskInput({
            workflowId,
            taskId,
            taskInputId: taskInputRes.data.id,
          });
          if (deleteTaskInputErrors.length) {
            throw new ApiError(deleteTaskInputErrors);
          }
          throw new Error(t('components.Builder.Errors.failedUploadFile') + s3Res.statusText);
        }
        // TODO: Guard against API Timeout: GUCCI-865
        if (getFlag('file-context-enabled')) {
          try {
            await v3.fileContext.createFileContext({
              workflowId,
              taskId,
              exampleSetId,
              exampleInputId: exampleInputResponse.data.id,
            });
          } catch (e) {
            console.error(
              t('components.Builder.Errors.errorCreatingContext') +
                `${workflowId},` +
                t('generics.task') +
                `${taskId}:`,
              e,
            );
          }
        } else {
          console.log(t('components.Builder.Errors.fileContextDisabled'));
        }
        // if transform-file-sample is undefined or true, create a file sample
        if (getFlag('transform-file-sample', false) !== false) {
          try {
            await v3.fileSamples.createFileSample({
              workflowId,
              taskId,
              exampleSetId,
              exampleInputId: exampleInput.id,
            });
          } catch (e) {
            console.error(e);
          }
        }

        return;
      }

      console.error(t('components.Builder.Errors.unsupportedInputType'), input.type);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.workflowInputs.byWorkflow(workflowId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.taskInputs.byTask({ workflowId, taskId }),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.exampleInputs.getExampleInputs({
          workflowId,
          taskId,
          exampleSetId,
        }),
      });
    },
    onError: (error) => {
      console.error('error', error);
    },
  });
  return {
    addInputMutation,
  };
};

export const useUpdateWorkflow = () => {
  return useMutation({
    mutationFn: async (workflow: Workflow) => {
      const { errors } = await v3.workflows.updateWorkflow({
        workflowId: workflow.id,
        workflow,
      });
      if (errors.length) {
        throw new Error(errors.map((error) => error.detail).join(', '));
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.workflows.all() });
    },
    onError: (error) => {
      console.error('Error:', error);
    },
  });
};
