const indent = '&nbsp;&nbsp;&nbsp;&nbsp;';
export const DEFAULT_USE_CASES = [
  {
    name: 'Accruals',
    content: `
**Accruals**\n\n
${indent}The accruals process ensures that revenue and expenses are recognized in the period they are incurred, even if cash has not yet been exchanged. This process is critical for accurate financial reporting and adherence to accounting standards.

1. **Identify Transactions Requiring Accruals**:
Review contracts, agreements, or recurring expenses/revenues to identify costs or revenues incurred in the current period but not yet invoiced or paid.

2. **Estimate the Accrual Amount**:
Calculate the expected amount based on prior invoices, contract terms, or historical trends. For costs, confirm service delivery or product receipt as the basis for accrual.

3. **Record the Accrual Entry**:
Create journal entries to recognize the accrual in the general ledger. Typically, debit the expense or revenue account and credit an accrued liability or accrued revenue account.

4. **Review and Adjust**:
Continuously compare the accruals to actual invoices received or payments made. Adjust the entries in subsequent periods to align with the actual amounts.
`.trim(),
  },
  {
    name: 'Allocations',
    content: `
**Allocations**\n\n
${indent}The allocations process systematically distributes costs or revenues across departments, business units, or projects to accurately represent financial performance and ensure proper expense tracking.

1. **Define Allocation Basis**:
Establish the methodology for allocation (e.g., headcount, square footage, usage metrics). This ensures consistency and fairness in distribution.

2. **Identify Costs/Revenues for Allocation**:
Aggregate shared costs (e.g., utilities, IT expenses) or revenues requiring allocation.

3. **Perform the Allocation**:
Use the defined basis to calculate the portion of costs or revenues applicable to each entity or unit. Create the necessary journal entries to reallocate these amounts in the ledger.

4. **Review Allocations Regularly**:
Periodically review the allocation basis and amounts for accuracy and relevance. Adjust as business needs or operational structures change.
`.trim(),
  },
  {
    name: 'Cash Reconciliation & Entries',
    content: `
**Cash Reconciliation & Entries**\n\n
${indent}The cash reconciliation and entries process ensures that the cash ledger accurately reflects bank account activity, identifying discrepancies for resolution.

1. **Gather Bank Statements and Ledger Records**:
Obtain bank statements and compare them with the cash ledger for the relevant period.

2. **Identify and Investigate Discrepancies**:
Highlight mismatches such as unrecorded deposits, outstanding checks, or incorrect transactions. Investigate the root cause of each discrepancy.

3. **Prepare Adjusting Entries**:
Record journal entries to account for any necessary corrections, such as bank fees, interest income, or errors in ledger posting.

4. **Finalize Reconciliation**:
Ensure the adjusted ledger balances match the bank statement. Document the reconciliation process for audit and compliance purposes.
`.trim(),
  },
  {
    name: 'Revenue Recognition',
    content: `
**Revenue Recognition**\n\n
${indent}The revenue recognition process aligns revenue recognition with the delivery of goods or services as per applicable accounting standards (e.g., ASC 606).

1. **Identify Performance Obligations**:
Review customer contracts to determine distinct performance obligations that must be met to recognize revenue.

2. **Determine Transaction Price**:
Allocate the total contract value to each performance obligation based on standalone selling prices or other acceptable methods.

3. **Recognize Revenue**:
Recognize revenue as each performance obligation is satisfied, either over time or at a point in time. Update the ledger accordingly.

4. **Monitor for Changes**:
Adjust revenue recognition for contract modifications, refunds, or contingencies to ensure compliance with accounting rules.
`.trim(),
  },
  {
    name: 'Commission Management',
    content: `
**Commission Management**\n\n
${indent}The commission management process ensures that commissions are accurately calculated and recorded in alignment with compensation agreements and company policies.

1. **Review Commission Agreements**:
Analyze commission plans to determine eligibility, rates, and payment triggers for each salesperson or partner.

2. **Calculate Commissions**:
Aggregate sales data and apply commission rates to determine individual payouts. Adjust for returns, discounts, or adjustments as necessary.

3. **Record and Pay Commissions**:
Create journal entries to accrue commission expenses and process payments through payroll or accounts payable systems.

4. **Reconcile and Audit**:
Periodically reconcile commission calculations with sales records and audit payouts to ensure accuracy and fairness.
`.trim(),
  },
  {
    name: 'Expense Management',
    content: `
**Expense Management**\n\n
${indent}The expense management process tracks, approves, and records company expenditures to ensure compliance with budgets and policies.

1. **Submit Expense Reports**:
Employees or vendors submit expense reports with receipts and justifications for incurred costs.

2. **Review and Approve**:
Supervisors or finance personnel review submitted expenses for policy compliance and accuracy. Approved expenses move to payment processing.

3. **Record Expenses**:
Enter approved expenses into the accounting system, categorizing them correctly for reporting purposes.

4. **Analyze and Report**:
Regularly review expense trends and compare them to budgets. Highlight variances for management action.
`.trim(),
  },
  {
    name: 'Intercompany Entries',
    content: `
**Intercompany Entries**\n\n
${indent}The intercompany entries process ensures accurate recording and elimination of transactions between related entities to avoid duplication in consolidated financial statements.

1. **Identify Intercompany Transactions**:
Capture transactions between entities, such as loans, sales, or cost allocations.

2. **Record Initial Entries**:
Book transactions in each entity's general ledger, ensuring consistency in amounts and descriptions across both entities.

3. **Reconcile Intercompany Balances**:
Match balances between entities to identify discrepancies. Investigate and resolve mismatches to ensure alignment.

4. **Eliminate in Consolidation**:
During financial statement preparation, eliminate intercompany entries to avoid double counting. Document the eliminations for transparency.

Each of these processes helps maintain financial accuracy, transparency, and compliance with standards and internal policies.
`.trim(),
  },
] as const;

// Had to do a bit of a dance here to get the correct typing here.
//  There's probably a better way to do this that I'm just not thinking of right now.
const defaultUseCasesByName = Object.groupBy(
  DEFAULT_USE_CASES,
  (useCase) => useCase.name,
);
export type UseCaseName = keyof typeof defaultUseCasesByName;
export const DEFAULT_USE_CASES_BY_NAME = DEFAULT_USE_CASES.reduce(
  (acc, useCase) => ({ ...acc, [useCase.name]: useCase }),
  {} as Record<UseCaseName, (typeof DEFAULT_USE_CASES)[number]>,
);
