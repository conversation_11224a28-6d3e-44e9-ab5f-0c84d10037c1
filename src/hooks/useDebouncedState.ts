import { useEffect, useState } from 'react';

type DebouncedState<T> = [
  state: T,
  setState: (newValue: T) => void,
  debouncedValue: T,
];

/**
 * useState alternative that includes a debounced value as part of the state.
 *
 * Updates via the setState will change the debounced value after `delayMs`.
 *
 * @example
 * ```tsx
 * const ExampleSearchBar = ({ onSearch }) => {
 *   const [search, setSearch, debouncedSearch] = useDebouncedState('', 500);
 *
 *   useEffect(() => {
 *     onSearch(debouncedSearch);
 *   }, [debouncedSearch, onSearch])
 *
 *   return (
 *     <input
 *       type="text"
 *       value={search}
 *       placeholder="Search..."
 *       onChange={e => setSearch(e.target.value)}
 *     />
 *   )
 * }
 * ```
 */
export const useDebouncedState = <T>(
  value: T,
  delayMs = 500,
): DebouncedState<T> => {
  const [state, setState] = useState(value);
  const [debouncedValue, setDebouncedValue] = useState(state);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(state);
    }, delayMs);

    return () => {
      clearTimeout(handler);
    };
  }, [state, delayMs]);

  return [state, setState, debouncedValue];
};