import { Modal, ProgressSteps, Wizard } from "@floqastinc/flow-ui_core";
import { Workflow } from "@floqastinc/transform-v3";
import { useState } from "react";
import { WorkflowForm } from "./components/WorkflowForm";
import { UseCaseForm } from "./components/UseCaseForm";
import { InputSelectionForm } from "./components/InputSelectionForm";
import { t } from "@/utils/i18n";
import { useFeatureFlags } from "@/components/FeatureFlag";

type OnCompletionArgs = {
  workflow: Pick<Workflow, "name" | "description" | "entityId" | "settings">;
  useCase: { name: string; content: string };
  experimentAssignments: Record<string, string>;
  initialInput: string | null;
};

type CreateWorkflowWizardProps = {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  trigger: React.ReactNode;
  onCompletion: (args: OnCompletionArgs) => void;
  isWorkflowLoading: boolean;
};

const defaultWorkflow = {
  id: "",
  name: "",
  description: undefined,
  entityId: "",
  settings: {
    formulas: false,
    highCapacity: false,
  },
};

export const CreateWorkflowWizard = ({
  isOpen,
  onOpenChange,
  trigger,
  onCompletion,
  isWorkflowLoading,
}: CreateWorkflowWizardProps) => {
  const [activeStep, setActiveStep] = useState(0);
  const { getFlag } = useFeatureFlags();
  const isPreCreateAgentStepEnabled = getFlag("enable-pre-create-agent-step");
  const [workflow, setWorkflow] =
    useState<Pick<Workflow, "name" | "description" | "entityId" | "settings">>(defaultWorkflow);
  const [useCase, setUseCase] = useState({ name: "", content: "" });
  const [experimentAssignments, setExperimentAssignments] = useState<
    Record<string, string> // experimentName -> variantId
  >({});
  const [initialInput, setInitialInput] = useState<string | null>(null);

  const isLastStep = activeStep === 2;
  const isFirstStep = activeStep === 0;

  const isNextButtonDisabled = (() => {
    switch (activeStep) {
      case 0:
        return !workflow.name || !workflow.entityId;
      case 1:
        return false;
      case 2:
        return !initialInput;
      default:
        return false;
    }
  })();

  const resetState = () => {
    setWorkflow(defaultWorkflow);
    setUseCase({ name: "", content: "" });
    setInitialInput(null);
  };

  const getProgressStepStatus = (index: number) => {
    if (index < activeStep) {
      return "completed";
    }
    if (index === activeStep) {
      return "incomplete";
    }
    return null;
  };

  return (
    <Wizard
      activeStep={activeStep}
      style={{ minWidth: "unset" }}
      onCompletion={() => {
        onCompletion({
          workflow,
          useCase,
          experimentAssignments,
          initialInput,
        });
      }}
      onStepChange={(step: number) => {
        setActiveStep(step);
      }}
      propOverrides={{
        modalHeader: {
          children: "Create Agent",
        },
        modalRoot: {
          onOpenChange: (isOpen: boolean) => {
            setActiveStep(0);
            if (!isOpen) {
              resetState();
              onOpenChange(false);
            }
          },
          open: isOpen,
          size: "lg",
        },
        modalBody: {
          style: {
            overflow: "hidden",
          },
        },
        modalFooter: {
          style: {
            justifyContent: "end",
          },
        },
      }}
    >
      <Modal.Trigger>{trigger}</Modal.Trigger>
      <Wizard.Steps>
        <Wizard.Step style={{ gap: "0px", width: "100%" }}>
          <Wizard.StepContent>
            <WorkflowForm
              workflow={workflow}
              setWorkflow={setWorkflow}
              experimentAssignments={experimentAssignments}
              setExperimentAssignments={setExperimentAssignments}
            />
          </Wizard.StepContent>
        </Wizard.Step>

        <Wizard.Step style={{ gap: "0px", width: "100%" }}>
          <Wizard.StepContent>
            <UseCaseForm useCase={useCase} setUseCase={setUseCase} />
          </Wizard.StepContent>
        </Wizard.Step>

        {isPreCreateAgentStepEnabled ? (
          <Wizard.Step style={{ gap: "0px", width: "100%" }}>
            <Wizard.StepContent>
              <InputSelectionForm initialInput={initialInput} setInitialInput={setInitialInput} />
            </Wizard.StepContent>
          </Wizard.Step>
        ) : null}
      </Wizard.Steps>

      {isFirstStep ? (
        <Wizard.PreviousButton
          variant="ghost"
          color="dark"
          data-tracking-id="builder-wizard-cancel-create-agent-button"
          disabled={false}
          onClick={() => {
            resetState();
            onOpenChange(false);
          }}
        >
          {t("generics.cancel")}
        </Wizard.PreviousButton>
      ) : (
        <Wizard.PreviousButton
          variant="ghost"
          color="dark"
          data-tracking-id="builder-wizard-back-create-agent-button"
        >
          {t("generics.back")}
        </Wizard.PreviousButton>
      )}

      <Wizard.NextButton
        disabled={isNextButtonDisabled || isWorkflowLoading}
        data-tracking-id={
          !isLastStep
            ? "builder-wizard-create-agent-next-step-button"
            : "builder-wizard-create-agent-button"
        }
      >
        {t("components.Builder.next")}
      </Wizard.NextButton>

      <ProgressSteps style={{ width: "50%", margin: "0 auto" }}>
        <ProgressSteps.Step status={getProgressStepStatus(0)}>
          <ProgressSteps.StepTitle>
            {t("components.Builder.nameAndDescription")}
          </ProgressSteps.StepTitle>
        </ProgressSteps.Step>

        <ProgressSteps.Step status={getProgressStepStatus(1)}>
          <ProgressSteps.StepTitle>
            {t("components.Builder.selectAgentType")}
          </ProgressSteps.StepTitle>
        </ProgressSteps.Step>

        {isPreCreateAgentStepEnabled ? (
          <ProgressSteps.Step status={getProgressStepStatus(2)}>
            <ProgressSteps.StepTitle>{t("components.Builder.selectInput")}</ProgressSteps.StepTitle>
          </ProgressSteps.Step>
        ) : null}
      </ProgressSteps>
    </Wizard>
  );
};
