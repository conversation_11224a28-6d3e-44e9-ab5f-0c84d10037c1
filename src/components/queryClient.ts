import { MutationCache, QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // 1 second
      staleTime: 1000 * 1,
    },
  },
  mutationCache: new MutationCache({
    onSuccess: () => {
      // #invalidateQueries
      // NOTE: This is here to invalidate queries after _all_ mutations.
      // This follows the same pattern as react-router/remix
      // and optimizes towards preventing missing invalidations for complex
      // operations that may operate on a host of different collections/sub-documents.
      // Rationale and some alternatives are listed here:
      //  https://tkdodo.eu/blog/automatic-query-invalidation-after-mutations
      queryClient.invalidateQueries();
    },
  }),
});
