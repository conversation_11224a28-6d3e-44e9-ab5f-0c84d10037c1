import styled from 'styled-components';

export const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); // Semi-transparent black
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; // Ensure it's above other content
`;

export const ModalWrapper = styled.div`
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  margin: 0 auto;
`;

export const ModalHeader = styled.h2`
  margin-top: 0;
  color: #333;
`;

export const ModalBody = styled.p`
  font: Inter;
  font-weight: 400;
  font-size: 12px;
`;

export const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;
`;

export const TextInput = styled.input`
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
`;

export const Button = styled.button`
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  color: white;
  background-color: ${(props) =>
    props.$primary ? 'var(--flo-sem-color-primary-default)' : '#6c757d'};
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${(props) =>
      props.$primary ? 'var(--flo-sem-color-primary-hover)' : '#5a6268'};
  }
`;

export const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  width: 100%;
  margin: 20px 0px;
`;

export const LoadingIndicator = styled.div`
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 2s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`;

export const ExperimentSettings = styled.div`
  display: flex;
  flex-direction: column;
  width: 312px;
  margin: 0 auto;
  gap: 16px;
`;

export const ExperimentGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
`;

export const ExperimentRow = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
`;

export const ExperimentLabel = styled.div`
  min-width: 140px; // Adjust based on longest label
  text-align: left;
  flex-shrink: 0;
`;
