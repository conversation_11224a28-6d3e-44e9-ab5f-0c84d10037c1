import styled from 'styled-components';
import { Link as ReactRouterLink } from 'react-router-dom';
import { Button } from '@floqastinc/flow-ui_core';
import ArrowForward from '@floqastinc/flow-ui_icons/material/ArrowForward';
import CalendarMonthOutlined from '@floqastinc/flow-ui_icons/material/CalendarMonthOutlined';

export const Link = styled(ReactRouterLink)`
  text-decoration: none;
`;

export const AgentHistoryButton = styled(Button)`
  height: 74px;
  max-height: 74px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0px;
  text-decoration: none;
`;

export const RunInfo = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
`;

export const RunByContainer = styled.span`
  margin-right: 5px;
  color: var(--flo-base-color-neutral-600);
  font-weight: 400;
  text-decoration: none;

  strong {
    color: var(--flo-base-color-neutral-800);
  }
`;

export const ArrowForwardNeutral = styled(ArrowForward)`
  fill: var(--flo-base-color-neutral-500);
`;

export const RunTimeStamp = styled.div`
  display: flex;
  align-items: center;
  padding-top: 2px;
  font-weight: var(--flo-base-font-weight-4);
`;

export const SmallCalendarMonth = styled(CalendarMonthOutlined)`
  height: 12px !important;
  width: 12px !important;
  margin-right: 5px;
  padding-top: 0;
`;

export const RunDate = styled.span`
  margin-right: 5px;
  padding-right: 5px;
  border-right: 1px solid var(--flo-base-color-neutral-400);
`;
