import { useState, useCallback } from 'react';
import { Input, Flex } from '@floqastinc/flow-ui_core';
import { Search } from '@floqastinc/flow-ui_icons';
import { Styled_SectionTitle } from '../../styles/text.styles';
import { Styled_Header } from '../../styles/container.styles';
import { t } from '@/utils/i18n';
import { useDebounce } from '@/hooks/useDebounce';

interface ConnectionsHeaderProps {
  setSearchTerm: (term: string) => void;
}

export const ConnectionsHeader = ({ setSearchTerm }: ConnectionsHeaderProps) => {
  const [inputValue, setInputValue] = useState<string>('');
  const debounce = useDebounce();

  const handleSearch = useCallback(
    (searchValue: string) => {
      debounce(() => {
        setSearchTerm(searchValue);
      }, 500)();
    },
    [debounce, setSearchTerm],
  );

  const handleInputChange = useCallback(
    (value: string) => {
      setInputValue(value);
      handleSearch(value);
    },
    [handleSearch],
  );

  return (
    <Styled_Header>
      <Styled_SectionTitle>{t('components.Connections.title')}</Styled_SectionTitle>
      <Flex align="center" justify="center" gap={16}>
        <Input
          styleOverrides={{ root: { width: '312px' } }}
          aria-label={t('components.Connections.Header.searchPlaceholder')}
          isSearchable
          onChange={handleInputChange}
          value={inputValue}
          placeholder={t('components.Connections.Header.searchPlaceholder')}
        >
          <Input.LeftItem>
            <Search size={20} />
          </Input.LeftItem>
        </Input>
      </Flex>
    </Styled_Header>
  );
};
