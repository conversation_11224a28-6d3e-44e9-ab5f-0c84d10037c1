import { Fragment, useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Table, THead, TR, TH, TBody } from '@floqastinc/flow-ui_core';
import {
  GetWorkflowsSortOn,
  GetWorkflowsSortOrder,
  WorkflowStatus,
} from '@floqastinc/transform-v3';
import { useInfiniteQuery } from '@tanstack/react-query';
import { t } from '@/utils/i18n';
import { ProtectedComponent } from '@floqastinc/auth-module-client';
import { match, P } from 'ts-pattern';
import { Row } from './Row';
import { EmptyState } from './EmptyState';
import { USER_ACTION_KEYS } from '@/authorization';
import { getWorkflowsQuery } from '@BuilderV3/api/workflows';
import { useInfiniteScrollObserver as useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { OverlayLoading } from '@/components/Loading';

type SortOrder = 'asc' | 'desc';

const defaultSortBy: GetWorkflowsSortOn = 'LATEST_RUN';
const defaultSortOrder: SortOrder = 'desc';

const validSortBy = new Set<GetWorkflowsSortOn>(['NAME', 'LATEST_RUN', 'UPDATED_AT']);
const validSortOrder = new Set<SortOrder>(['asc', 'desc']);

const isValidSortBy = (sortOn: string | null): sortOn is GetWorkflowsSortOn => {
  return validSortBy.has(sortOn as GetWorkflowsSortOn);
};

const isValidSortOrder = (sortOrder: string | null): sortOrder is SortOrder => {
  return validSortOrder.has(sortOrder as SortOrder);
};

type SortState = {
  current?: {
    field: GetWorkflowsSortOn;
    direction: SortOrder;
  };
  previous?: {
    field: GetWorkflowsSortOn;
    direction: SortOrder;
  };
};

type RunsTableProps = {
  search: string;
  activeTab: WorkflowStatus;
};
export const AgentsTable = ({ search, activeTab }: RunsTableProps) => {
  const defaultSort: SortState = {
    current: {
      field: defaultSortBy,
      direction: defaultSortOrder,
    },
  };

  const [searchParams, setSearchParams] = useSearchParams();
  const [sort, setSort] = useState<SortState>(defaultSort);

  useEffect(() => {
    const sortByParam = searchParams.get('sortBy');
    const sessionSortBy = sessionStorage.getItem('sortBy');
    const sortOrderParam = searchParams.get('sortOrder');
    const sessionSortOrder = sessionStorage.getItem('sortOrder');

    const sortBy = isValidSortBy(sortByParam)
      ? sortByParam
      : isValidSortBy(sessionSortBy)
        ? sessionSortBy
        : defaultSortBy;

    const sortOrder = isValidSortOrder(sortOrderParam)
      ? sortOrderParam
      : isValidSortOrder(sessionSortOrder)
        ? sessionSortOrder
        : defaultSortOrder;

    setSort({
      current: {
        field: sortBy,
        direction: sortOrder,
      },
      previous: sort.current,
    });

    sessionStorage.setItem('sortBy', sortBy);
    sessionStorage.setItem('sortOrder', sortOrder);

    setSearchParams({
      sortBy: sortBy,
      sortOrder: sortOrder,
    });
  }, [searchParams, setSearchParams]);

  const handleSort = ({ current }: Partial<Pick<SortState, 'current'>>) => {
    setSearchParams({
      sortBy: current?.field ?? '',
      sortOrder: current?.direction ?? '',
    });
  };

  const {
    data,
    fetchNextPage,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    // TODO: Handle the error state
    error: _error,
  } = useInfiniteQuery(
    getWorkflowsQuery({
      search,
      status: activeTab,
      sortOn: sort.current?.field,
      // NOTE: This is a bit hacky, but since the API types and the frontend types from flo-ui
      // are a bit different here, this is what is currently happening for 'asc' to 'ASC', etc.
      sortOrder: sort.current?.direction?.toUpperCase() as GetWorkflowsSortOrder,
      first: 25,
    }),
  );

  const lastRowRef = useInfiniteScroll({
    onIntersect: fetchNextPage,
    isFetching: isFetchingNextPage,
    hasMore: hasNextPage,
  });

  const HEADER_CONTENT_HEIGHT = 72 + 40; // height of agents title + tabs
  const maxTableHeight = (window.innerHeight - HEADER_CONTENT_HEIGHT) * 0.9;
  const ROW_HEIGHT = 64 + 1; // height of each row + bottom border
  const tableHeight = Math.floor(maxTableHeight / ROW_HEIGHT) * ROW_HEIGHT;

  const hasData = (
    data_: typeof data,
  ): data_ is typeof data & { pages: { data: { length: number }[] }[] } =>
    !!data_?.pages[0]?.data.length;
  const notHasData = (data_: typeof data) => !hasData(data_);

  return (
    <div
      style={{
        height: `${tableHeight / 16}rem`,
        width: '100%',
        overflow: 'none',
      }}
    >
      {match({ data, isFetching })
        .with({ isFetching: true }, { data: P.nullish }, () => <OverlayLoading />)
        .with({ data: P.when(notHasData), isFetching: false }, () => <EmptyState />)
        .with({ data: P.when(hasData), isFetching: false }, ({ data }) => {
          return data.pages[0]?.data.length ? (
            <Table stickyheader middleBorders outerRoundedBorder>
              <THead>
                <TR>
                  <TH
                    alignLeft
                    width="auto"
                    sortConfig={{
                      sortOrder: sort.current?.field === 'NAME' ? sort.current.direction : 'none',
                      onSort: (nextSortOrder: 'asc' | 'desc' | 'none') => {
                        if (nextSortOrder === 'none') {
                          const oppositeDirection =
                            sort.current?.direction === 'asc' ? 'desc' : 'asc';
                          handleSort({
                            current: { field: 'NAME', direction: oppositeDirection },
                          });
                        } else {
                          handleSort({
                            current: { field: 'NAME', direction: nextSortOrder },
                          });
                        }
                      },
                    }}
                  >
                    {t('generics.name')}
                  </TH>
                  <TH alignLeft width="30%">
                    {t('generics.description')}
                  </TH>
                  <TH alignLeft width="auto">
                    {t('components.AgentList.connectedData')}
                  </TH>
                  <TH
                    alignLeft
                    width="auto"
                    sortConfig={{
                      sortOrder:
                        sort.current?.field === 'LATEST_RUN' ? sort.current.direction : 'none',
                      onSort: (nextSortOrder: 'asc' | 'desc' | 'none') => {
                        if (nextSortOrder === 'none') {
                          const oppositeDirection =
                            sort.current?.direction === 'asc' ? 'desc' : 'asc';
                          handleSort({
                            current: {
                              field: 'LATEST_RUN',
                              direction: oppositeDirection,
                            },
                          });
                        } else {
                          handleSort({
                            current: {
                              field: 'LATEST_RUN',
                              direction: nextSortOrder,
                            },
                          });
                        }
                      },
                    }}
                  >
                    {t('components.AgentList.lastRun')}
                  </TH>
                  <TH
                    alignLeft
                    width="auto"
                    sortConfig={{
                      sortOrder:
                        sort.current?.field === 'UPDATED_AT' ? sort.current.direction : 'none',
                      onSort: (nextSortOrder: 'asc' | 'desc' | 'none') => {
                        if (nextSortOrder === 'none') {
                          const oppositeDirection =
                            sort.current?.direction === 'asc' ? 'desc' : 'asc';
                          handleSort({
                            current: {
                              field: 'UPDATED_AT',
                              direction: oppositeDirection,
                            },
                          });
                        } else {
                          handleSort({
                            current: {
                              field: 'UPDATED_AT',
                              direction: nextSortOrder,
                            },
                          });
                        }
                      },
                    }}
                  >
                    {t('components.AgentList.lastEdited')}
                  </TH>
                  <ProtectedComponent actionKey={USER_ACTION_KEYS.TRANSFORM_WORKFLOW_WRITE}>
                    <TH aria-label="Build Button Header" width={90} style={{ minWidth: 90 }} />
                  </ProtectedComponent>
                  <TH width={80} style={{ minWidth: 80 }} />
                  <TH width={90} style={{ minWidth: 90 }} />
                  <TH width={0} style={{ minWidth: 0 }} />
                  <ProtectedComponent actionKey={USER_ACTION_KEYS.TRANSFORM_WORKFLOW_FULL}>
                    <TH aria-label="More Options Header" width={0} style={{ minWidth: 0 }} />
                  </ProtectedComponent>
                </TR>
              </THead>
              <TBody>
                {isFetching ? <OverlayLoading /> : null}
                {data?.pages.map((page) => (
                  <Fragment key={page.pageInfo.startCursor}>
                    {page.data.map((workflow, i) =>
                      i === page.data.length - 1 ? (
                        <Row key={workflow.id} ref={lastRowRef} workflow={workflow} />
                      ) : (
                        <Row key={workflow.id} workflow={workflow} />
                      ),
                    )}
                  </Fragment>
                ))}
              </TBody>
            </Table>
          ) : null;
        })
        .otherwise(() => {
          console.error(t('components.AgentList.unexpectedFallthrough'));
          return null;
        })}
    </div>
  );
};
