import React from 'react';
import { Card, IconButton, Heading, Text, Spinner } from '@floqastinc/flow-ui_core';
import Close from '@floqastinc/flow-ui_icons/material/Close';
import { useMutationState } from '@tanstack/react-query';
import { Slideout } from '../Slideout/Slideout';
import * as Styled from './DetailsSlideout.styles';
import { t } from '@/utils/i18n';

type DetailsSlideoutProps = {
  isOpen: boolean;
  onClose?: () => void;
  onTransitionEnd?: () => void;
  header?: React.ReactNode | null;
  steps?: React.ReactNode | null;
};
export const DetailsSlideout = ({
  isOpen,
  onTransitionEnd,
  onClose,
  header,
  steps,
}: DetailsSlideoutProps) => {
  return (
    <Slideout
      isOpen={isOpen}
      expandedWidth={500}
      onTransitionEnd={onTransitionEnd ?? (() => {})}
      style={{
        boxShadow: '0 8px 16px 0 rgb(0 0 0 / 5%)',
      }}
    >
      <Styled.DetailCard>
        <Card.Header>
          <Styled.DetailHeader>
            {header}
            <IconButton onClick={onClose}>
              <Close />
            </IconButton>
          </Styled.DetailHeader>
        </Card.Header>
        <Card.Description></Card.Description>
        {steps}
      </Styled.DetailCard>
    </Slideout>
  );
};

type HeaderProps = {
  stepName: string;
  workflowId: string;
  taskId: string;
};
const Header = ({ stepName, workflowId, taskId }: HeaderProps) => {
  const createTaskDescriptionMutation = useMutationState({
    filters: {
      mutationKey: ['taskDescription', 'create', { workflowId, taskId }],
    },
  });
  return (
    <>
      <Heading variant="h4" weight="medium">
        {stepName} {t('generics.details')}
      </Heading>
      {createTaskDescriptionMutation[0]?.status === 'pending' ? (
        <Styled.GeneratingText>
          <Text truncate={false}>{t('components.Runner.generatingDescription')}</Text>
          <Spinner size={24} />
        </Styled.GeneratingText>
      ) : null}
    </>
  );
};

DetailsSlideout.Header = Header;
