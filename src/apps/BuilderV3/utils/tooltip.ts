import { hoverTooltip } from '@codemirror/view';
import { createSystemKey } from './conversionFunctions';
import { FlolakeConnectionData } from '@/api/shared/types';

export let hasTooltipWarning = false;
export let tooltipErrorMessage: string | null = null;

// Helper functions for tooltip creation
// ------------------------------------------------------
// Simple regex to match any of: {word}, {word}.{word}, or {word}.{word}.{word}
const regex = /{([^}]+)}(?:\.{([^}]+)})?(?:\.{([^}]+)})?/g;

const createTooltipDOM = (content: string, className: string, useInnerHTML = false) => {
  const dom = document.createElement('div');
  dom.className = className;
  if (useInnerHTML) {
    dom.innerHTML = content;
  } else {
    dom.textContent = content;
  }
  return { dom };
};

const createWarningTooltip = (content: string) => createTooltipDOM(content, 'cm-tooltip-warning');

const createInfoTooltip = (content: string) => createTooltipDOM(content, 'cm-tooltip-info', true);

const tooltipContent = {
  schema: (systemName: string) => `<div class="cm-schema-tooltip">${systemName} system</div>`,
  table: (tableName: string) => `<div class="cm-table-tooltip">${tableName} table</div>`,
  column: (columnName: string) =>
    `<div class="cm-column-tooltip"><div>${columnName} column</div></div>`,
};

function createTooltip(pos: number, end: number, content: { dom: HTMLElement }) {
  return {
    pos,
    end,
    above: true,
    create: () => content,
  };
}

function validateReference(
  system: string,
  flolakeData: FlolakeConnectionData[],
  createSystemKey: (sys: FlolakeConnectionData) => string,
  table?: string,
  column?: string,
) {
  // Find the system match
  const systemMatch = flolakeData.find((sys) => {
    const systemKey = createSystemKey(sys);
    // Direct comparison or normalized comparison if needed
    return systemKey === system || systemKey.toLowerCase() === system.toLowerCase();
  });

  if (!systemMatch) {
    return { valid: false, message: `No system "${system}" found` };
  }

  if (table) {
    const tableMatch = systemMatch.tableData.find((t) => t.tableName.toUpperCase() === table);

    if (!tableMatch) {
      return { valid: false, message: `No table "${table}" found in ${system}` };
    }

    if (column) {
      const columnMatch = tableMatch.columns.find((c) => c.name === column);

      if (!columnMatch) {
        return { valid: false, message: `No column "${column}" found in ${table}` };
      }
    }
  }

  return {
    valid: true,
    systemMatch,
    tableMatch: table
      ? systemMatch.tableData.find((t) => t.tableName.toUpperCase() === table)
      : undefined,
    columnMatch:
      column && table
        ? systemMatch.tableData
            .find((t) => t.tableName.toUpperCase() === table)
            ?.columns.find((c) => c.name === column)
        : undefined,
  };
}
// ------------------------------------------------------

export const createSchemaToolTip = (flolakeData: FlolakeConnectionData[] = []) => {
  return hoverTooltip((view, pos) => {
    const { from, text } = view.state.doc.lineAt(pos);
    const offset = pos - from;

    let match;
    while ((match = regex.exec(text)) !== null) {
      const matchStart = match.index; // Start position relative to the line
      const system = match[1];
      const table = match[2];
      const column = match[3];
      // Calculate positions of each part (relative to line start)
      const systemStart = matchStart + 1; // +1 for {
      const systemEnd = systemStart + system.length;

      let tableStart, tableEnd, columnStart, columnEnd;

      if (table) {
        tableStart = text.indexOf(`{${table}}`, match.index) + 1; // +1 for {
        tableEnd = tableStart + table.length;
      }

      if (column) {
        columnStart = text.indexOf(`{${column}}`, match.index) + 1; // +1 for {
        columnEnd = columnStart + column.length;
      }

      // Handle system part hover
      if (offset >= systemStart && offset <= systemEnd) {
        const result = validateReference(
          system,
          flolakeData,
          createSystemKey,
          undefined,
          undefined,
        );
        return createTooltip(
          from + systemStart,
          from + systemEnd,
          result.valid
            ? createInfoTooltip(tooltipContent.schema(system))
            : createWarningTooltip(result.message ?? ''),
        );
      }

      // Handle table part hover
      if (
        table &&
        tableStart !== undefined &&
        tableEnd !== undefined &&
        offset >= tableStart &&
        offset <= tableEnd
      ) {
        const result = validateReference(system, flolakeData, createSystemKey, table, undefined);
        return createTooltip(
          from + tableStart,
          from + tableEnd,
          result.valid
            ? createInfoTooltip(tooltipContent.table(table))
            : createWarningTooltip(result.message ?? ''),
        );
      }

      // Handle column part hover
      if (
        column &&
        columnStart !== undefined &&
        columnEnd !== undefined &&
        offset >= columnStart &&
        offset <= columnEnd
      ) {
        const result = validateReference(system, flolakeData, createSystemKey, table, column);
        return createTooltip(
          from + columnStart,
          from + columnEnd,
          result.valid
            ? createInfoTooltip(tooltipContent.column(column))
            : createWarningTooltip(result.message ?? ''),
        );
      }
    }

    return null;
  });
};

export const scanQueryForWarnings = (
  query: string,
  flolakeData: FlolakeConnectionData[] = [],
): void => {
  hasTooltipWarning = false;
  tooltipErrorMessage = null;

  let match;
  while ((match = regex.exec(query)) !== null) {
    const system = match[1];
    const table = match[2];
    const column = match[3];

    const result = validateReference(system, flolakeData, createSystemKey, table, column);

    if (!result.valid) {
      hasTooltipWarning = true;
      tooltipErrorMessage = result.message ?? null;
      return;
    }
  }
};
