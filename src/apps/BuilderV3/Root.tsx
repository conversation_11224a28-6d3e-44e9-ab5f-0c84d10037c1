import styled from 'styled-components';
import { Toaster } from '@floqastinc/flow-ui_core';
import { Outlet } from 'react-router-dom';
import { FeatureFlagDevTools } from '../../components/FeatureFlag';
import { useModal } from '../../components';
import { AddWorkflowModal, EditWorkflowModal } from './app-components/Modals/WorkflowModals';
import { WorkflowRunNotesModal } from './app-components/Modals/WorkflowRunNotes';
import { AddTaskModal } from './app-components/Modals/AddTaskModal';
import { DeleteWorkflowModal } from './app-components/Modals/DeleteWorkflowModal';
import { DeleteWorkflowRunModal } from './app-components/Modals/DeleteWorkflowRunModal';
import { AddInputModal } from './app-components/Modals/AddInputModal';

const RootPage = styled.div`
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  height: 100%;
  max-width: 100%;
`;
export const Root = () => {
  const { currentModal } = useModal();
  const renderModal = () => {
    if (!currentModal) return null;
    const { name, props } = currentModal;
    switch (name) {
      case 'AddWorkflow':
        return <AddWorkflowModal {...props} />;
      case 'EditWorkflow':
        return <EditWorkflowModal {...props} />;
      case 'EditWorkflowRunNotes':
        return <WorkflowRunNotesModal {...props} />;
      case 'AddTask':
        return <AddTaskModal {...props} />;
      case 'DeleteWorkflow':
        return <DeleteWorkflowModal {...props} />;
      case 'DeleteWorkflowRun':
        return <DeleteWorkflowRunModal {...props} />;
      case 'AddInput':
        return <AddInputModal {...props} />;
      default:
        return null;
    }
  };
  return (
    <RootPage>
      <Toaster />
      <FeatureFlagDevTools />
      {renderModal()}
      <Outlet />
    </RootPage>
  );
};
